/**
 * Interface cho thống kê theo trạng thái
 */
export interface StatusStatistics {
  completed: number;
  inProgress: number;
  pending: number;
}

/**
 * Interface cho thống kê theo điểm
 */
export interface ScoreStatistics {
  averageScore: number;
  totalScoredTasks: number;
}

/**
 * Interface cho thống kê chung
 */
export interface GeneralStatistics {
  byStatus: StatusStatistics;
  byScore: ScoreStatistics;
}

/**
 * Interface cho thông tin người dùng trong thống kê
 */
export interface UserStatisticsInfo {
  id: number;
  name?: string;
  email?: string;
  avatar?: string;
}

/**
 * Interface cho thông tin dự án trong thống kê
 */
export interface ProjectStatisticsInfo {
  id: number;
  name: string;
  description: string | null;
  isActive: boolean;
}

/**
 * Interface cho hiệu suất thành viên
 */
export interface MemberPerformance {
  userId: number;
  name?: string;
  completedTasks: number;
  totalTasks: number;
  averageScore: number;
  completionRate: number;
}

/**
 * Interface cho hiệu suất người dùng
 */
export interface UserPerformanceDto {
  user: UserStatisticsInfo;
  totalTasks: number;
  completedTasks: number;
  inProgressTasks: number;
  pendingTasks: number;
  averageScore: number;
  completionRate: number;
  statistics: GeneralStatistics;
}

/**
 * Interface cho hiệu suất dự án
 */
export interface ProjectPerformanceDto {
  project: ProjectStatisticsInfo;
  totalTasks: number;
  completedTasks: number;
  inProgressTasks: number;
  pendingTasks: number;
  averageScore: number;
  completionRate: number;
  statistics: GeneralStatistics;
  members: MemberPerformance[];
}

/**
 * Interface cho dữ liệu biểu đồ Gantt
 */
export interface GanttTaskDto {
  id: number;
  title: string;
  start: number;
  end: number;
  progress: number;
  dependencies?: number[];
  assigneeName?: string;
}

/**
 * Interface cho dữ liệu biểu đồ Gantt của dự án
 */
export interface ProjectGanttDto {
  projectId: number;
  projectName: string;
  tasks: GanttTaskDto[];
}
