import React from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import * as z from 'zod';

import { Button, Form, FormItem, Input, Select } from '@/shared/components/common';
import { NotificationUtil } from '@/shared/utils/notification';
import { zodResolver } from '@hookform/resolvers/zod';

import { useAddProjectMember, useUpdateProjectMember } from '../hooks/useProjects';
import { ProjectMemberRole, ProjectMemberDto } from '../types/project.types';

// Định nghĩa schema validation
const projectMemberSchema = z.object({
  userId: z
    .string()
    .min(1, 'User ID không được để trống')
    .transform(val => parseInt(val, 10)),
  role: z.enum([ProjectMemberRole.ADMIN, ProjectMemberRole.MEMBER, ProjectMemberRole.VIEWER]),
});

// <PERSON><PERSON>u dữ liệu form
type ProjectMemberFormValues = z.infer<typeof projectMemberSchema>;

// Props component
interface ProjectMemberFormProps {
  projectId: number;
  member?: ProjectMemberDto;
  onSubmit: () => void;
  onCancel: () => void;
}

/**
 * Form thêm/cập nhật thành viên dự án
 */
const ProjectMemberForm: React.FC<ProjectMemberFormProps> = ({
  projectId,
  member,
  onSubmit,
  onCancel,
}) => {
  const { t } = useTranslation(['common', 'todolist']);
  const isEditMode = !!member;

  // Hooks mutation
  const { mutateAsync: addMember, isPending: isAdding } = useAddProjectMember();
  const { mutateAsync: updateMember, isPending: isUpdating } = useUpdateProjectMember();

  // Form hook
  const form = useForm<ProjectMemberFormValues>({
    resolver: zodResolver(projectMemberSchema),
    defaultValues: {
      userId: member?.userId ? member.userId.toString() : '',
      role: member?.role || ProjectMemberRole.MEMBER,
    },
  });

  // Xử lý submit form
  const handleSubmit = async (values: ProjectMemberFormValues) => {
    try {
      if (isEditMode && member) {
        await updateMember({
          projectId,
          memberId: member.id,
          data: {
            role: values.role,
          },
        });
        NotificationUtil.success(
          t('todolist:project.members.notifications.updateSuccess', 'Member updated successfully')
        );
      } else {
        await addMember({
          projectId,
          data: {
            userId: values.userId,
            role: values.role,
          },
        });
        NotificationUtil.success(
          t('todolist:project.members.notifications.addSuccess', 'Member added successfully')
        );
      }
      onSubmit();
    } catch (error) {
      console.error('Error submitting project member form:', error);
      NotificationUtil.error(
        isEditMode
          ? t('todolist:project.members.notifications.updateError', 'Error updating member')
          : t('todolist:project.members.notifications.addError', 'Error adding member')
      );
    }
  };

  // Danh sách vai trò
  const roleOptions = [
    { value: ProjectMemberRole.ADMIN, label: t('todolist:project.roles.admin', 'Admin') },
    { value: ProjectMemberRole.MEMBER, label: t('todolist:project.roles.member', 'Member') },
    { value: ProjectMemberRole.VIEWER, label: t('todolist:project.roles.viewer', 'Viewer') },
  ];

  return (
    <Form form={form} onSubmit={handleSubmit} className="space-y-6">
      <FormItem
        name="userId"
        label={t('todolist:project.members.userId', 'User ID')}
        required
        disabled={isEditMode}
      >
        <Input
          type="number"
          placeholder={t('todolist:project.members.placeholders.userId', 'Enter user ID')}
        />
      </FormItem>

      <FormItem name="role" label={t('todolist:project.members.role', 'Role')} required>
        <Select
          placeholder={t('todolist:project.members.placeholders.role', 'Select role')}
          options={roleOptions}
        />
      </FormItem>

      <div className="flex justify-end space-x-3 pt-4">
        <Button variant="outline" onClick={onCancel} disabled={isAdding || isUpdating}>
          {t('common:cancel', 'Cancel')}
        </Button>
        <Button type="submit" loading={isAdding || isUpdating}>
          {isEditMode ? t('common:update', 'Update') : t('common:add', 'Add')}
        </Button>
      </div>
    </Form>
  );
};

export default ProjectMemberForm;
