/**
 * Trang chính của module Recruitment
 */
import { ModuleCard } from '@/modules/components/card';
import { ResponsiveGrid, Typography } from '@/shared/components/common';
import React from 'react';
import { useTranslation } from 'react-i18next';

import { useRecruitmentStats } from '../hooks/useRecruitment';

interface RecruitmentModule {
  id: string;
  title: string;
  description: string;
  icon: string;
  count: number;
  countLabel: string;
  linkTo: string;
  linkText: string;
  disabled?: boolean;
}

/**
 * Component trang chính Recruitment
 */
const RecruitmentHomePage: React.FC = () => {
  const { t } = useTranslation(['common', 'hrm']);

  // Lấy thống kê recruitment
  const { data: stats, isLoading: isLoadingStats } = useRecruitmentStats();

  // Định nghĩa các module con của Recruitment
  const modules: RecruitmentModule[] = [
    {
      id: 'job-positions',
      title: t('hrm:recruitment.modules.jobPositions.title', 'Vị trí tuyển dụng'),
      description: t(
        'hrm:recruitment.modules.jobPositions.description',
        'Quản lý các vị trí tuyển dụng'
      ),
      icon: 'briefcase',
      count: stats?.totalPositions || 0,
      countLabel: t('hrm:recruitment.modules.jobPositions.countLabel', 'Vị trí'),
      linkTo: '/hrm/recruitment/job-positions',
      linkText: t('common:view', 'Xem'),
    },
    {
      id: 'candidates',
      title: t('hrm:recruitment.modules.candidates.title', 'Ứng viên'),
      description: t('hrm:recruitment.modules.candidates.description', 'Quản lý hồ sơ ứng viên'),
      icon: 'users',
      count: stats?.totalCandidates || 0,
      countLabel: t('hrm:recruitment.modules.candidates.countLabel', 'Ứng viên'),
      linkTo: '/hrm/recruitment/candidates',
      linkText: t('common:view', 'Xem'),
    },
    {
      id: 'recruitment-process',
      title: t('hrm:recruitment.modules.process.title', 'Quy trình tuyển dụng'),
      description: t(
        'hrm:recruitment.modules.process.description',
        'Thiết lập quy trình tuyển dụng'
      ),
      icon: 'workflow',
      count: 3,
      countLabel: t('hrm:recruitment.modules.process.countLabel', 'Quy trình'),
      linkTo: '/hrm/recruitment/process',
      linkText: t('common:view', 'Xem'),
    },
    {
      id: 'interviews',
      title: t('hrm:recruitment.modules.interviews.title', 'Lịch phỏng vấn'),
      description: t('hrm:recruitment.modules.interviews.description', 'Quản lý lịch phỏng vấn'),
      icon: 'calendar',
      count: 12,
      countLabel: t('hrm:recruitment.modules.interviews.countLabel', 'Cuộc phỏng vấn'),
      linkTo: '/hrm/recruitment/interviews',
      linkText: t('common:view', 'Xem'),
      disabled: true,
    },
    {
      id: 'reports',
      title: t('hrm:recruitment.modules.reports.title', 'Báo cáo tuyển dụng'),
      description: t('hrm:recruitment.modules.reports.description', 'Thống kê và báo cáo'),
      icon: 'reports',
      count: stats?.hiredThisMonth || 0,
      countLabel: t('hrm:recruitment.modules.reports.countLabel', 'Tuyển dụng tháng này'),
      linkTo: '/hrm/recruitment/reports',
      linkText: t('common:view', 'Xem'),
      disabled: true,
    },
    {
      id: 'settings',
      title: t('hrm:recruitment.modules.settings.title', 'Cài đặt'),
      description: t(
        'hrm:recruitment.modules.settings.description',
        'Cấu hình hệ thống tuyển dụng'
      ),
      icon: 'settings',
      count: 8,
      countLabel: t('hrm:recruitment.modules.settings.countLabel', 'Cấu hình'),
      linkTo: '/hrm/recruitment/settings',
      linkText: t('common:view', 'Xem'),
      disabled: true,
    },
  ];

  return (
    <div className="w-full bg-background text-foreground">
      {/* Header */}
      <div className="mb-8">
        <Typography variant="h1" className="mb-2">
          {t('hrm:recruitment.title', 'Quản lý tuyển dụng')}
        </Typography>
        <Typography variant="body1" className="text-muted-foreground">
          {t(
            'hrm:recruitment.description',
            'Quản lý toàn bộ quy trình tuyển dụng từ đăng tin đến tuyển dụng'
          )}
        </Typography>
      </div>

      {/* Statistics Overview */}
      {!isLoadingStats && stats && (
        <div className="mb-8">
          <Typography variant="h3" className="mb-4">
            {t('hrm:recruitment.overview', 'Tổng quan')}
          </Typography>
          <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 4, lg: 4 }}>
            <div className="bg-card p-4 rounded-lg border">
              <Typography variant="h4" className="text-primary">
                {stats.totalPositions}
              </Typography>
              <Typography variant="body2" className="text-muted-foreground">
                {t('hrm:recruitment.stats.totalPositions', 'Tổng vị trí')}
              </Typography>
            </div>
            <div className="bg-card p-4 rounded-lg border">
              <Typography variant="h4" className="text-green-600">
                {stats.activePositions}
              </Typography>
              <Typography variant="body2" className="text-muted-foreground">
                {t('hrm:recruitment.stats.activePositions', 'Vị trí đang tuyển')}
              </Typography>
            </div>
            <div className="bg-card p-4 rounded-lg border">
              <Typography variant="h4" className="text-blue-600">
                {stats.totalCandidates}
              </Typography>
              <Typography variant="body2" className="text-muted-foreground">
                {t('hrm:recruitment.stats.totalCandidates', 'Tổng ứng viên')}
              </Typography>
            </div>
            <div className="bg-card p-4 rounded-lg border">
              <Typography variant="h4" className="text-orange-600">
                {stats.newCandidates}
              </Typography>
              <Typography variant="body2" className="text-muted-foreground">
                {t('hrm:recruitment.stats.newCandidates', 'Ứng viên mới')}
              </Typography>
            </div>
          </ResponsiveGrid>
        </div>
      )}

      {/* Modules Grid */}
      <div>
        <Typography variant="h3" className="mb-4">
          {t('hrm:recruitment.modules.title', 'Chức năng')}
        </Typography>
        <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 3, lg: 3 }}>
          {modules.map(module => (
            <ModuleCard
              key={module.id}
              title={module.title}
              description={module.description}
              icon={module.icon}
              count={module.count}
              countLabel={module.countLabel}
              linkTo={module.linkTo}
              linkText={module.linkText}
              disabled={module.disabled}
            />
          ))}
        </ResponsiveGrid>
      </div>
    </div>
  );
};

export default RecruitmentHomePage;
