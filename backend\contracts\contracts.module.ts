import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { AuthModule } from '../auth/auth.module';

// Controllers
import { ContractController } from './controllers/contract.controller';
import { ContractTemplateController } from './controllers/contract-template.controller';
import { ContractApprovalController } from './controllers/contract-approval.controller';
import { ContractSignatureController } from './controllers/contract-signature.controller';
import { ContractReportController } from './controllers/contract-report.controller';

// Entities
import { Contract } from './entities/contract.entity';
import { ContractTemplate } from './entities/contract-template.entity';
import { ContractVersion } from './entities/contract-version.entity';
import { ContractSignature } from './entities/contract-signature.entity';
import { ContractApproval } from './entities/contract-approval.entity';
import { ContractAttachment } from './entities/contract-attachment.entity';
import { ContractComment } from './entities/contract-comment.entity';

// Repositories
import { ContractRepository } from './repositories/contract.repository';
import { ContractTemplateRepository } from './repositories/contract-template.repository';
import { ContractVersionRepository } from './repositories/contract-version.repository';
import { ContractSignatureRepository } from './repositories/contract-signature.repository';
import { ContractApprovalRepository } from './repositories/contract-approval.repository';
import { ContractAttachmentRepository } from './repositories/contract-attachment.repository';
import { ContractCommentRepository } from './repositories/contract-comment.repository';

// Services
import { ContractService } from './services/contract.service';
import { ContractTemplateService } from './services/contract-template.service';
import { ContractApprovalService } from './services/contract-approval.service';
import { ContractSignatureService } from './services/contract-signature.service';
import { ContractReportService } from './services/contract-report.service';
import { ContractNotificationService } from './services/contract-notification.service';

/**
 * Module quản lý hợp đồng
 * 
 * Chức năng chính:
 * - Quản lý hợp đồng và template
 * - Quy trình phê duyệt
 * - Ký số điện tử
 * - Báo cáo và thống kê
 * - Quản lý tài liệu đính kèm
 */
@Module({
  imports: [
    AuthModule,
    TypeOrmModule.forFeature([
      Contract,
      ContractTemplate,
      ContractVersion,
      ContractSignature,
      ContractApproval,
      ContractAttachment,
      ContractComment,
    ]),
  ],
  controllers: [
    ContractController,
    ContractTemplateController,
    ContractApprovalController,
    ContractSignatureController,
    ContractReportController,
  ],
  providers: [
    // Repositories
    ContractRepository,
    ContractTemplateRepository,
    ContractVersionRepository,
    ContractSignatureRepository,
    ContractApprovalRepository,
    ContractAttachmentRepository,
    ContractCommentRepository,

    // Services
    ContractService,
    ContractTemplateService,
    ContractApprovalService,
    ContractSignatureService,
    ContractReportService,
    ContractNotificationService,
  ],
  exports: [
    ContractService,
    ContractTemplateService,
    ContractApprovalService,
    ContractSignatureService,
    ContractReportService,
  ],
})
export class ContractsModule {}
