import { Injectable, Logger } from '@nestjs/common';
import { EmployeeRepository } from '../repositories/employee.repository';
import { CreateEmployeeWithUserDto } from '../dto/create-employee-with-user.dto';
import { EmployeeWithUserResponseDto, EmployeeResponseDto, UserResponseDto } from '../dto/employee-with-user-response.dto';
import { AppException } from '@/common/exceptions/app.exception';
import { HRM_ERROR_CODES } from '../../errors/hrm-error.code';
import { EmployeeStatus } from '../enum/employee-status.enum';
import { UserStatus } from '@/modules/auth/enum/user-status.enum';
import { Transactional } from 'typeorm-transactional';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '@/modules/auth/entities/user.entity';
import { Employee } from '../entities/employee.entity';
import { EncryptionService } from '@/shared/services/encryption.service';

/**
 * Service cho việc tạo nhân viên kèm tài khoản người dùng
 */
@Injectable()
export class EmployeeUserService {
  private readonly logger = new Logger(EmployeeUserService.name);

  constructor(
    private readonly employeeRepository: EmployeeRepository,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly encryptionService: EncryptionService,
  ) {}

  /**
   * Tạo nhân viên mới kèm tài khoản người dùng
   * @param createDto Dữ liệu tạo nhân viên và tài khoản
   * @param currentUserId ID của người dùng đang thực hiện thao tác
   * @returns Thông tin nhân viên và tài khoản đã tạo
   */
  @Transactional()
  async createEmployeeWithUser(
    createDto: CreateEmployeeWithUserDto,
    currentUserId: number,
  ): Promise<EmployeeWithUserResponseDto> {
    try {
      // Kiểm tra mã nhân viên đã tồn tại chưa
      const existingEmployee = await this.employeeRepository.findByEmployeeCode(createDto.employeeCode);
      if (existingEmployee) {
        throw new AppException(
          HRM_ERROR_CODES.EMPLOYEE_CODE_EXISTS,
          `Nhân viên với mã ${createDto.employeeCode} đã tồn tại`,
        );
      }

      // Kiểm tra username đã tồn tại chưa
      const existingUsername = await this.userRepository.findOne({
        where: { username: createDto.userInfo.username }
      });
      if (existingUsername) {
        throw new AppException(
          HRM_ERROR_CODES.USERNAME_EXISTS,
          `Tên đăng nhập ${createDto.userInfo.username} đã được sử dụng`,
        );
      }

      // Kiểm tra email đã tồn tại chưa
      const existingEmail = await this.userRepository.findOne({
        where: { email: createDto.userInfo.email }
      });
      if (existingEmail) {
        throw new AppException(
          HRM_ERROR_CODES.EMAIL_EXISTS,
          `Email ${createDto.userInfo.email} đã được sử dụng`,
        );
      }

      // Mã hóa mật khẩu
      const hashedPassword = this.encryptionService.hashPassword(createDto.userInfo.password);

      // Tạo timestamp hiện tại
      const now = Date.now();

      // Tạo tài khoản người dùng mới
      const user = await this.userRepository.save({
        username: createDto.userInfo.username,
        email: createDto.userInfo.email,
        password: hashedPassword,
        fullName: createDto.userInfo.fullName,
        departmentId: createDto.departmentId,
        status: UserStatus.ACTIVE,
        createdAt: now,
      });

      // Tạo nhân viên mới
      const employee = await this.employeeRepository.create({
        userId: user.id,
        employeeCode: createDto.employeeCode,
        departmentId: createDto.departmentId,
        jobTitle: createDto.jobTitle,
        status: createDto.status || EmployeeStatus.ACTIVE,
        createdAt: now,
        updatedAt: now,
        createdBy: currentUserId,
        updatedBy: currentUserId,
      });

      // Tạo đối tượng phản hồi
      const employeeResponse = new EmployeeResponseDto();
      employeeResponse.id = employee.id;
      employeeResponse.userId = employee.userId;
      employeeResponse.employeeCode = employee.employeeCode;
      employeeResponse.departmentId = employee.departmentId;
      employeeResponse.jobTitle = employee.jobTitle;
      employeeResponse.status = employee.status;

      const userResponse = new UserResponseDto();
      userResponse.id = user.id;
      userResponse.username = user.username;
      userResponse.email = user.email;
      userResponse.fullName = user.fullName;

      const response: EmployeeWithUserResponseDto = {
        employee: employeeResponse,
        user: userResponse,
      };

      return response;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi tạo nhân viên và tài khoản: ${error.message}`, error.stack);
      throw new AppException(
        HRM_ERROR_CODES.EMPLOYEE_CREATE_FAILED,
        `Không thể tạo nhân viên và tài khoản: ${error.message}`,
      );
    }
  }
}
