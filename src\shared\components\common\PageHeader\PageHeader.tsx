import React from 'react';

import { Typography, Breadcrumb } from '@/shared/components/common';
import { BreadcrumbItem } from '@/shared/components/common/Breadcrumb/Breadcrumb';

export interface PageHeaderProps {
  /**
   * Tiêu đề của trang
   */
  title: string;

  /**
   * <PERSON><PERSON> tả của trang (tùy chọn)
   */
  description?: string;

  /**
   * Các mục breadcrumb
   */
  breadcrumbs?: BreadcrumbItem[];

  /**
   * Các action buttons hoặc components khác ở bên phải
   */
  actions?: React.ReactNode;

  /**
   * Class tùy chỉnh
   */
  className?: string;
}

/**
 * Component header cho trang, bao gồm tiêu đề, breadcrumb và các actions
 */
const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  description,
  breadcrumbs,
  actions,
  className = '',
}) => {
  return (
    <div className={`mb-6 ${className}`}>
      {breadcrumbs && breadcrumbs.length > 0 && (
        <div className="mb-2">
          <Breadcrumb items={breadcrumbs} />
        </div>
      )}

      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
        <div>
          <Typography variant="h4" className="font-bold">
            {title}
          </Typography>
          {description && (
            <Typography variant="body1" className="text-muted-foreground mt-1">
              {description}
            </Typography>
          )}
        </div>
        {actions && <div className="flex items-center gap-2">{actions}</div>}
      </div>
    </div>
  );
};

export default PageHeader;
