import { DateSelectArg, EventClickArg, EventChangeArg } from '@fullcalendar/core';

/**
 * Định nghĩa các thuộc tính mở rộng cho sự kiện lịch
 */
export interface CalendarEventExtendedProps {
  /**
   * Loại sự kiện (meeting, appointment, deadline, etc.)
   */
  type?: string;

  /**
   * C<PERSON>c thuộc tính tùy chỉnh khác
   */
  [key: string]: string | number | boolean | object | null | undefined;
}

/**
 * Định nghĩa cấu trúc của một sự kiện lịch
 */
export interface CalendarEvent {
  /**
   * ID duy nhất của sự kiện
   */
  id: string;

  /**
   * Tiêu đề sự kiện
   */
  title: string;

  /**
   * Thời gian bắt đầu sự kiện
   */
  start: Date | string;

  /**
   * Thời gian kết thúc sự kiện (tùy chọn)
   */
  end?: Date | string;

  /**
   * Sự kiện kéo dài cả ngày
   */
  allDay?: boolean;

  /**
   * <PERSON><PERSON> tả sự kiện
   */
  description?: string;

  /**
   * Đ<PERSON><PERSON> điểm sự kiện
   */
  location?: string;

  /**
   * Tên lớp CSS để tùy chỉnh giao diện
   */
  className?: string;

  /**
   * Màu nền sự kiện
   */
  backgroundColor?: string;

  /**
   * Màu viền sự kiện
   */
  borderColor?: string;

  /**
   * Màu chữ sự kiện
   */
  textColor?: string;

  /**
   * Các thuộc tính mở rộng
   */
  extendedProps?: CalendarEventExtendedProps;

  /**
   * Sự kiện có thể chỉnh sửa hay không
   */
  editable?: boolean;

  /**
   * Sự kiện có thể kéo dài hay không
   */
  durationEditable?: boolean;

  /**
   * Sự kiện có thể di chuyển hay không
   */
  startEditable?: boolean;
}

/**
 * Props cho component Calendar
 */
export interface CalendarProps {
  /**
   * Danh sách sự kiện
   */
  events?: CalendarEvent[];

  /**
   * Ngày ban đầu để hiển thị
   */
  initialDate?: Date;

  /**
   * Chế độ xem ban đầu (dayGridMonth, timeGridWeek, timeGridDay, listWeek)
   */
  initialView?: string;

  /**
   * Hiển thị cuối tuần
   */
  weekends?: boolean;

  /**
   * Cho phép chỉnh sửa sự kiện
   */
  editable?: boolean;

  /**
   * Cho phép chọn ngày/giờ
   */
  selectable?: boolean;

  /**
   * Hiển thị slot cả ngày
   */
  allDaySlot?: boolean;

  /**
   * Chiều cao của lịch
   */
  height?: string | number;

  /**
   * Tên lớp CSS bổ sung
   */
  className?: string;

  /**
   * Callback khi chọn ngày/giờ
   */
  onDateSelect?: (selectInfo: DateSelectArg) => void;

  /**
   * Callback khi click vào sự kiện
   */
  onEventClick?: (clickInfo: EventClickArg) => void;

  /**
   * Callback khi sự kiện thay đổi (kéo, thay đổi kích thước)
   */
  onEventChange?: (changeInfo: EventChangeArg) => void;
}
