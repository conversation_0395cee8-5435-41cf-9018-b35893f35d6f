import { apiClient } from '@/shared/api/axios';
import type {
  ContractTemplate,
  ContractTemplateQueryParams,
  ContractTemplateCreateDto,
  ContractTemplateUpdateDto,
  ContractTemplateListResponse,
  TemplateValidationResult,
  TemplateGenerationResult,
} from '../types/contract-template.types';

/**
 * Contract Template API Service
 */
export class ContractTemplateService {
  private static readonly BASE_URL = '/api/contracts/templates';

  /**
   * Get contract templates with pagination and filtering
   */
  static async getTemplates(params?: ContractTemplateQueryParams): Promise<ContractTemplateListResponse> {
    const response = await apiClient.get(this.BASE_URL, { params });
    return response.data;
  }

  /**
   * Get template by ID
   */
  static async getTemplate(id: string): Promise<ContractTemplate> {
    const response = await apiClient.get(`${this.BASE_URL}/${id}`);
    return response.data;
  }

  /**
   * Create new template
   */
  static async createTemplate(data: ContractTemplateCreateDto): Promise<ContractTemplate> {
    const response = await apiClient.post(this.BASE_URL, data);
    return response.data;
  }

  /**
   * Update template
   */
  static async updateTemplate(id: string, data: ContractTemplateUpdateDto): Promise<ContractTemplate> {
    const response = await apiClient.put(`${this.BASE_URL}/${id}`, data);
    return response.data;
  }

  /**
   * Delete template
   */
  static async deleteTemplate(id: string): Promise<void> {
    await apiClient.delete(`${this.BASE_URL}/${id}`);
  }

  /**
   * Duplicate template
   */
  static async duplicateTemplate(id: string): Promise<ContractTemplate> {
    const response = await apiClient.post(`${this.BASE_URL}/${id}/duplicate`);
    return response.data;
  }

  /**
   * Validate template variables
   */
  static async validateTemplate(id: string, variables: Record<string, any>): Promise<TemplateValidationResult> {
    const response = await apiClient.post(`${this.BASE_URL}/${id}/validate`, { variables });
    return response.data;
  }

  /**
   * Generate contract content from template
   */
  static async generateContent(id: string, variables: Record<string, any>): Promise<TemplateGenerationResult> {
    const response = await apiClient.post(`${this.BASE_URL}/${id}/generate`, { variables });
    return response.data;
  }

  /**
   * Preview template with variables
   */
  static async previewTemplate(id: string, variables: Record<string, any>): Promise<string> {
    const response = await apiClient.post(`${this.BASE_URL}/${id}/preview`, { variables });
    return response.data.content;
  }

  /**
   * Get template usage statistics
   */
  static async getTemplateStats(id: string) {
    const response = await apiClient.get(`${this.BASE_URL}/${id}/stats`);
    return response.data;
  }

  /**
   * Get popular templates
   */
  static async getPopularTemplates(limit: number = 10) {
    const response = await apiClient.get(`${this.BASE_URL}/popular`, {
      params: { limit },
    });
    return response.data;
  }

  /**
   * Get recent templates
   */
  static async getRecentTemplates(limit: number = 10) {
    const response = await apiClient.get(`${this.BASE_URL}/recent`, {
      params: { limit },
    });
    return response.data;
  }

  /**
   * Search templates
   */
  static async searchTemplates(query: string, filters?: any) {
    const response = await apiClient.get(`${this.BASE_URL}/search`, {
      params: { q: query, ...filters },
    });
    return response.data;
  }

  /**
   * Get template categories
   */
  static async getTemplateCategories() {
    const response = await apiClient.get(`${this.BASE_URL}/categories`);
    return response.data;
  }

  /**
   * Get templates by category
   */
  static async getTemplatesByCategory(category: string) {
    const response = await apiClient.get(`${this.BASE_URL}/category/${category}`);
    return response.data;
  }

  /**
   * Export template
   */
  static async exportTemplate(id: string, format: 'json' | 'pdf' | 'word'): Promise<Blob> {
    const response = await apiClient.get(`${this.BASE_URL}/${id}/export/${format}`, {
      responseType: 'blob',
    });
    return response.data;
  }

  /**
   * Import template
   */
  static async importTemplate(file: File): Promise<ContractTemplate> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await apiClient.post(`${this.BASE_URL}/import`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  /**
   * Archive template
   */
  static async archiveTemplate(id: string): Promise<ContractTemplate> {
    const response = await apiClient.patch(`${this.BASE_URL}/${id}/archive`);
    return response.data;
  }

  /**
   * Restore template
   */
  static async restoreTemplate(id: string): Promise<ContractTemplate> {
    const response = await apiClient.patch(`${this.BASE_URL}/${id}/restore`);
    return response.data;
  }

  /**
   * Publish template
   */
  static async publishTemplate(id: string): Promise<ContractTemplate> {
    const response = await apiClient.patch(`${this.BASE_URL}/${id}/publish`);
    return response.data;
  }

  /**
   * Unpublish template
   */
  static async unpublishTemplate(id: string): Promise<ContractTemplate> {
    const response = await apiClient.patch(`${this.BASE_URL}/${id}/unpublish`);
    return response.data;
  }

  /**
   * Get template version history
   */
  static async getTemplateVersions(id: string) {
    const response = await apiClient.get(`${this.BASE_URL}/${id}/versions`);
    return response.data;
  }

  /**
   * Create new template version
   */
  static async createTemplateVersion(id: string, data: ContractTemplateUpdateDto) {
    const response = await apiClient.post(`${this.BASE_URL}/${id}/versions`, data);
    return response.data;
  }

  /**
   * Get template tags
   */
  static async getTemplateTags() {
    const response = await apiClient.get(`${this.BASE_URL}/tags`);
    return response.data;
  }

  /**
   * Get templates by tag
   */
  static async getTemplatesByTag(tag: string) {
    const response = await apiClient.get(`${this.BASE_URL}/tag/${tag}`);
    return response.data;
  }
}
