import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Employee } from './entities/employee.entity';
import { EmployeeRepository } from './repositories/employee.repository';
import { EmployeeService } from './services/employee.service';
import { EmployeeController } from './controllers/employee.controller';
import { EmployeeUserService } from './services/employee-user.service';
import { EmployeeUserController } from './controllers/employee-user.controller';
import { EmployeePermissionService } from './services/employee-permission.service';
import { EmployeePermissionController } from './controllers/employee-permission.controller';
import { User } from '@/modules/auth/entities/user.entity';
import { Role } from '@/modules/auth/entities/role.entity';
import { UserRole } from '@/modules/auth/entities/user-role.entity';
import { UserPermission } from '@/modules/auth/entities/user-permission.entity';
import { RolePermission } from '@/modules/auth/entities/role-permission.entity';
import { PermissionRepository } from '@/modules/auth/repositories/permission.repository';
import { UserRepository } from '@/modules/auth/repositories/user.repository';
import { EncryptionService } from '@/shared/services/encryption.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Employee,
      User,
      Role,
      UserRole,
      UserPermission,
      RolePermission
    ]),
  ],
  controllers: [
    EmployeeController,
    EmployeeUserController,
    EmployeePermissionController
  ],
  providers: [
    EmployeeService,
    EmployeeRepository,
    EmployeeUserService,
    EmployeePermissionService,
    PermissionRepository,
    UserRepository,
    EncryptionService
  ],
  exports: [
    EmployeeService,
    EmployeeRepository,
    EmployeeUserService,
    EmployeePermissionService
  ],
})
export class EmployeesModule {}
