import React, { useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';

import {
  Button,
  Card,
  ColorPicker,
  DatePicker,
  Form,
  FormGrid,
  FormItem,
  FormSection,
  Icon,
  Input,
  Radio,
  Select,
  Textarea,
  Toggle,
  Typography,
} from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/types';
import SearchInputWithLazyLoading from '@/shared/components/common/SearchInputWithLazyLoading';

/**
 * Schema validation cho form tùy chỉnh
 */
const customFormSchema = z.object({
  productName: z.string().min(2, 'Tên sản phẩm phải có ít nhất 2 ký tự'),
  description: z.string().optional(),
  price: z
    .number()
    .min(0, 'Giá không được âm')
    .or(
      z
        .string()
        .regex(/^\d+(\.\d{1,2})?$/)
        .transform(Number)
    ),
  category: z.string(),
  tags: z.array(z.string()),
  releaseDate: z.date().optional(),
  isAvailable: z.boolean(),
  color: z.string(),
  weight: z
    .number()
    .min(0)
    .or(
      z
        .string()
        .regex(/^\d+(\.\d{1,2})?$/)
        .transform(Number)
    ),
  rating: z.number().min(1).max(5),
  productType: z.string(),
  supplier: z.string().optional(),
  warrantyPeriod: z.number().min(0).or(z.string().regex(/^\d+$/).transform(Number)),
  dimensions: z.object({
    length: z
      .number()
      .min(0)
      .or(
        z
          .string()
          .regex(/^\d+(\.\d{1,2})?$/)
          .transform(Number)
      ),
    width: z
      .number()
      .min(0)
      .or(
        z
          .string()
          .regex(/^\d+(\.\d{1,2})?$/)
          .transform(Number)
      ),
    height: z
      .number()
      .min(0)
      .or(
        z
          .string()
          .regex(/^\d+(\.\d{1,2})?$/)
          .transform(Number)
      ),
  }),
});

type CustomFormValues = z.infer<typeof customFormSchema>;

interface CustomFormFieldsProps {
  /**
   * Callback khi submit form
   */
  onSubmit: (values: CustomFormValues) => void;

  /**
   * Callback khi hủy
   */
  onCancel: () => void;

  /**
   * Đang xử lý submit
   */
  isSubmitting?: boolean;
}

/**
 * Component form với các trường tùy chỉnh
 */
const CustomFormFields: React.FC<CustomFormFieldsProps> = ({
  onSubmit,
  onCancel,
  isSubmitting = false,
}) => {
  const { t } = useTranslation(['product']);
  const formRef = useRef<FormRef<CustomFormValues>>(null);

  // Mock data cho suppliers
  const suppliers = [
    { id: '1', name: 'Supplier A' },
    { id: '2', name: 'Supplier B' },
    { id: '3', name: 'Supplier C' },
    { id: '4', name: 'Supplier D' },
    { id: '5', name: 'Supplier E' },
  ];

  // Xử lý tìm kiếm supplier
  const handleSearchSupplier = async (query: string) => {
    // Giả lập gọi API
    return new Promise<{ id: string; name: string }[]>(resolve => {
      setTimeout(() => {
        const filteredSuppliers = suppliers.filter(supplier =>
          supplier.name.toLowerCase().includes(query.toLowerCase())
        );
        resolve(filteredSuppliers);
      }, 500);
    });
  };

  return (
    <Card title="Form với trường tùy chỉnh" className="mb-4">
      <Form
        ref={formRef}
        schema={customFormSchema}
        onSubmit={onSubmit}
        className="space-y-6"
        defaultValues={{
          productName: '',
          description: '',
          price: 0,
          category: '',
          tags: [],
          releaseDate: new Date(),
          isAvailable: true,
          color: '#3B82F6',
          weight: 0,
          rating: 3,
          productType: 'physical',
          supplier: '',
          warrantyPeriod: 12,
          dimensions: {
            length: 0,
            width: 0,
            height: 0,
          },
        }}
      >
        <FormSection title="Thông tin cơ bản">
          <FormGrid columns={2} columnsSm={1} gap="md">
            <FormItem name="productName" label="Tên sản phẩm" required>
              <Input
                placeholder="Nhập tên sản phẩm"
                fullWidth
                leftIcon={<Icon name="package" size="sm" />}
              />
            </FormItem>

            <FormItem name="category" label="Danh mục" required>
              <Select
                options={[
                  { value: 'electronics', label: 'Điện tử' },
                  { value: 'clothing', label: 'Thời trang' },
                  { value: 'furniture', label: 'Nội thất' },
                  { value: 'books', label: 'Sách' },
                ]}
                placeholder="Chọn danh mục"
                fullWidth
              />
            </FormItem>
          </FormGrid>

          <FormItem name="description" label="Mô tả">
            <Textarea placeholder="Nhập mô tả sản phẩm" rows={3} fullWidth />
          </FormItem>

          <FormItem name="tags" label="Thẻ">
            <TagInput placeholder="Nhập thẻ và nhấn Enter" fullWidth />
          </FormItem>
        </FormSection>

        <FormSection title="Giá và tình trạng">
          <FormGrid columns={2} columnsSm={1} gap="md">
            <FormItem name="price" label="Giá" required>
              <Input
                type="number"
                placeholder="Nhập giá sản phẩm"
                fullWidth
                leftIcon={<Icon name="dollar-sign" size="sm" />}
              />
            </FormItem>

            <FormItem name="releaseDate" label="Ngày phát hành">
              <DatePicker placeholder="Chọn ngày phát hành" fullWidth />
            </FormItem>
          </FormGrid>

          <FormItem name="isAvailable" label="Có sẵn để bán">
            <Toggle />
          </FormItem>
        </FormSection>

        <FormSection title="Thuộc tính sản phẩm">
          <FormGrid columns={2} columnsSm={1} gap="md">
            <FormItem name="color" label="Màu sắc">
              <ColorPicker />
            </FormItem>

            <FormItem name="weight" label="Trọng lượng (kg)">
              <Input type="number" placeholder="Nhập trọng lượng" fullWidth />
            </FormItem>
          </FormGrid>

          <FormItem
            name="rating"
            label={`Đánh giá: ${formRef.current?.getValues()?.rating || 3}/5`}
          >
            <Slider min={1} max={5} step={1} />
          </FormItem>

          <FormItem name="productType" label="Loại sản phẩm">
            <div className="flex flex-col space-y-2">
              <Radio value="physical" label="Sản phẩm vật lý" name="productType" />
              <Radio value="digital" label="Sản phẩm kỹ thuật số" name="productType" />
              <Radio value="service" label="Dịch vụ" name="productType" />
            </div>
          </FormItem>
        </FormSection>

        <FormSection title="Thông tin bổ sung">
          <FormItem name="supplier" label="Nhà cung cấp">
            <SearchInputWithLazyLoading
              placeholder="Tìm nhà cung cấp"
              onSearch={handleSearchSupplier}
              getOptionLabel={option => option.name}
              getOptionValue={option => option.id}
              onChange={value => formRef.current?.setValue('supplier', value?.id || '')}
            />
          </FormItem>

          <FormItem name="warrantyPeriod" label="Thời gian bảo hành (tháng)">
            <Input type="number" placeholder="Nhập thời gian bảo hành" fullWidth />
          </FormItem>

          <Typography variant="subtitle1" className="mt-4 mb-2">
            Kích thước (cm)
          </Typography>
          <FormGrid columns={3} columnsSm={1} gap="md">
            <FormItem name="dimensions.length" label="Dài">
              <Input type="number" placeholder="Dài" fullWidth />
            </FormItem>
            <FormItem name="dimensions.width" label="Rộng">
              <Input type="number" placeholder="Rộng" fullWidth />
            </FormItem>
            <FormItem name="dimensions.height" label="Cao">
              <Input type="number" placeholder="Cao" fullWidth />
            </FormItem>
          </FormGrid>
        </FormSection>

        <div className="flex justify-end space-x-2 pt-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            Hủy
          </Button>
          <Button type="submit" variant="primary" loading={isSubmitting}>
            Lưu
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default CustomFormFields;
