import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';

import { User } from '../../auth/entities/user.entity';
import { Contract } from './contract.entity';

export enum ApprovalStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  DELEGATED = 'delegated',
  EXPIRED = 'expired',
}

export enum ApprovalType {
  LEGAL_REVIEW = 'legal_review',
  FINANCIAL_REVIEW = 'financial_review',
  TECHNICAL_REVIEW = 'technical_review',
  MANAGEMENT_APPROVAL = 'management_approval',
  COMPLIANCE_CHECK = 'compliance_check',
  CUSTOM = 'custom',
}

/**
 * Entity phê duyệt hợp đồng
 */
@Entity('contract_approvals')
export class ContractApproval {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'enum', enum: ApprovalStatus, default: ApprovalStatus.PENDING })
  status: ApprovalStatus;

  @Column({ type: 'enum', enum: ApprovalType, default: ApprovalType.MANAGEMENT_APPROVAL })
  type: ApprovalType;

  @Column({ type: 'int', default: 1 })
  stepOrder: number;

  @Column({ type: 'varchar', length: 255 })
  stepName: string;

  @Column({ type: 'text', nullable: true })
  stepDescription: string;

  @Column({ type: 'boolean', default: true })
  required: boolean;

  @Column({ type: 'int', default: 1 })
  requiredApprovals: number; // Number of approvals needed for this step

  @Column({ type: 'int', default: 0 })
  currentApprovals: number; // Current number of approvals

  @Column({ type: 'timestamp', nullable: true })
  requestedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  respondedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  expiresAt: Date;

  @Column({ type: 'text', nullable: true })
  comments: string;

  @Column({ type: 'text', nullable: true })
  rejectionReason: string;

  @Column({ type: 'json', nullable: true })
  conditions: {
    minContractValue?: number;
    maxContractValue?: number;
    contractTypes?: string[];
    departments?: string[];
    customRules?: Record<string, any>;
  };

  @Column({ type: 'json', nullable: true })
  approvalData: {
    approvedBy: {
      userId: string;
      userName: string;
      approvedAt: Date;
      comments?: string;
    }[];
    rejectedBy?: {
      userId: string;
      userName: string;
      rejectedAt: Date;
      reason: string;
    };
  };

  @Column({ type: 'boolean', default: false })
  autoApproved: boolean;

  @Column({ type: 'varchar', length: 255, nullable: true })
  autoApprovalRule: string;

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  // Relations
  @ManyToOne(() => Contract, (contract) => contract.approvals, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'contractId' })
  contract: Contract;

  @Column({ type: 'uuid' })
  contractId: string;

  @ManyToOne(() => User, { nullable: false })
  @JoinColumn({ name: 'requestedById' })
  requestedBy: User;

  @Column({ type: 'uuid' })
  requestedById: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'assignedToId' })
  assignedTo: User;

  @Column({ type: 'uuid', nullable: true })
  assignedToId: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'delegatedToId' })
  delegatedTo: User;

  @Column({ type: 'uuid', nullable: true })
  delegatedToId: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Methods
  approve(userId: string, userName: string, comments?: string): void {
    if (this.status !== ApprovalStatus.PENDING) {
      throw new Error('Cannot approve non-pending approval');
    }

    if (!this.approvalData) {
      this.approvalData = { approvedBy: [] };
    }

    // Check if user already approved
    const existingApproval = this.approvalData.approvedBy.find(a => a.userId === userId);
    if (existingApproval) {
      throw new Error('User has already approved this step');
    }

    // Add approval
    this.approvalData.approvedBy.push({
      userId,
      userName,
      approvedAt: new Date(),
      comments,
    });

    this.currentApprovals += 1;

    // Check if step is complete
    if (this.currentApprovals >= this.requiredApprovals) {
      this.status = ApprovalStatus.APPROVED;
      this.respondedAt = new Date();
    }
  }

  reject(userId: string, userName: string, reason: string): void {
    if (this.status !== ApprovalStatus.PENDING) {
      throw new Error('Cannot reject non-pending approval');
    }

    this.status = ApprovalStatus.REJECTED;
    this.rejectionReason = reason;
    this.respondedAt = new Date();

    if (!this.approvalData) {
      this.approvalData = { approvedBy: [] };
    }

    this.approvalData.rejectedBy = {
      userId,
      userName,
      rejectedAt: new Date(),
      reason,
    };
  }

  delegate(toUserId: string): void {
    if (this.status !== ApprovalStatus.PENDING) {
      throw new Error('Cannot delegate non-pending approval');
    }

    this.delegatedToId = toUserId;
    this.status = ApprovalStatus.DELEGATED;
  }

  isExpired(): boolean {
    return this.expiresAt && new Date() > this.expiresAt;
  }

  canApprove(userId: string): boolean {
    if (this.status !== ApprovalStatus.PENDING) {
      return false;
    }

    if (this.isExpired()) {
      return false;
    }

    // Check if user is assigned or delegated
    if (this.assignedToId && this.assignedToId !== userId) {
      return false;
    }

    if (this.delegatedToId && this.delegatedToId !== userId) {
      return false;
    }

    // Check if user already approved
    if (this.approvalData?.approvedBy.some(a => a.userId === userId)) {
      return false;
    }

    return true;
  }

  getApprovalProgress(): {
    current: number;
    required: number;
    percentage: number;
    isComplete: boolean;
  } {
    const percentage = this.requiredApprovals > 0 
      ? Math.round((this.currentApprovals / this.requiredApprovals) * 100)
      : 0;

    return {
      current: this.currentApprovals,
      required: this.requiredApprovals,
      percentage,
      isComplete: this.status === ApprovalStatus.APPROVED,
    };
  }

  checkConditions(contract: Contract): boolean {
    if (!this.conditions) {
      return true;
    }

    const { minContractValue, maxContractValue, contractTypes, departments } = this.conditions;

    // Check contract value
    if (minContractValue && contract.value && contract.value < minContractValue) {
      return false;
    }

    if (maxContractValue && contract.value && contract.value > maxContractValue) {
      return false;
    }

    // Check contract type
    if (contractTypes && !contractTypes.includes(contract.type)) {
      return false;
    }

    // Add more condition checks as needed

    return true;
  }
}
