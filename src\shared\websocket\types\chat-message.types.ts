/**
 * Chat Message Types for WebSocket Communication
 */

// Base message sender information
export interface MessageSender {
  id: string;
  name: string;
  type: 'user' | 'ai' | 'system';
  avatar?: string;
}

// Message metadata
export interface MessageMetadata {
  roomId: string;
  conversationId: string;
  isStreaming: boolean;
  streamComplete: boolean;
  priority: 'normal' | 'high' | 'urgent';
  replyTo?: string;
  edited?: boolean;
  editedAt?: string;
}

// Base message structure
export interface ChatMessage {
  id: string;
  type: 'message';
  timestamp: string;
  sender: MessageSender;
  content: MessageContent;
  metadata: MessageMetadata;
}

// Content types union
export type MessageContentType = 
  | 'text' 
  | 'markdown' 
  | 'image' 
  | 'video' 
  | 'link' 
  | 'form' 
  | 'navigation' 
  | 'stream'
  | 'file'
  | 'audio';

// Base content structure
export interface MessageContent {
  type: MessageContentType;
  data: ContentData;
}

// Content data union type
export type ContentData = 
  | TextContentData
  | MarkdownContentData
  | ImageContentData
  | VideoContentData
  | LinkContentData
  | FormContentData
  | NavigationContentData
  | StreamContentData
  | FileContentData
  | AudioContentData;

// Text content
export interface TextContentData {
  text: string;
  formatting?: {
    bold?: [number, number][];
    italic?: [number, number][];
    code?: [number, number][];
    underline?: [number, number][];
    strikethrough?: [number, number][];
  };
}

// Markdown content
export interface MarkdownContentData {
  markdown: string;
  allowHtml?: boolean;
  sanitize?: boolean;
}

// Image content
export interface ImageContentData {
  url: string;
  alt: string;
  caption?: string;
  thumbnail?: string;
  width?: number;
  height?: number;
  size?: number;
  format?: 'jpg' | 'png' | 'gif' | 'webp' | 'svg';
}

// Video content
export interface VideoContentData {
  url: string;
  thumbnail?: string;
  title?: string;
  duration?: number;
  autoplay?: boolean;
  controls?: boolean;
  muted?: boolean;
  loop?: boolean;
  width?: number;
  height?: number;
}

// Audio content
export interface AudioContentData {
  url: string;
  title?: string;
  duration?: number;
  autoplay?: boolean;
  controls?: boolean;
  muted?: boolean;
  loop?: boolean;
}

// Link content
export interface LinkContentData {
  url: string;
  title?: string;
  description?: string;
  image?: string;
  domain?: string;
  favicon?: string;
}

// File content
export interface FileContentData {
  url: string;
  name: string;
  size: number;
  type: string;
  downloadUrl?: string;
  previewUrl?: string;
}

// Form field definition
export interface FormField {
  type: 'string' | 'number' | 'boolean' | 'select' | 'multiselect' | 'date' | 'datetime' | 'textarea' | 'file';
  title: string;
  required?: boolean;
  placeholder?: string;
  helpText?: string;
  validation?: {
    minLength?: number;
    maxLength?: number;
    min?: number;
    max?: number;
    pattern?: string;
    custom?: string;
  };
  options?: Array<{
    value: string | number;
    label: string;
    disabled?: boolean;
  }>;
  defaultValue?: unknown;
}

// Form schema
export interface FormSchema {
  type: 'object';
  properties: Record<string, FormField>;
  required?: string[];
}

// Form content
export interface FormContentData {
  formId: string;
  title: string;
  description?: string;
  schema: FormSchema;
  submitEndpoint: string;
  submitMethod?: 'POST' | 'PUT' | 'PATCH';
  prefillData?: Record<string, unknown>;
  validation?: {
    realtime?: boolean;
    showErrors?: boolean;
    validateOnBlur?: boolean;
    validateOnChange?: boolean;
  };
  styling?: {
    layout?: 'vertical' | 'horizontal' | 'inline';
    size?: 'small' | 'medium' | 'large';
    variant?: 'outlined' | 'filled' | 'standard';
  };
}

// Navigation content
export interface NavigationContentData {
  action: 'redirect' | 'open_modal' | 'open_sidebar' | 'open_tab' | 'download';
  url: string;
  title: string;
  description?: string;
  target?: '_self' | '_blank' | 'modal' | 'sidebar';
  params?: Record<string, unknown>;
  confirmation?: {
    required: boolean;
    message: string;
    confirmText?: string;
    cancelText?: string;
  };
  icon?: string;
  buttonVariant?: 'primary' | 'secondary' | 'outline' | 'ghost';
}

// Stream content
export interface StreamContentData {
  streamId: string;
  chunk: string;
  isComplete: boolean;
  totalChunks?: number;
  currentChunk?: number;
  contentType?: 'text' | 'markdown' | 'json';
  encoding?: string;
}

// Message status
export type MessageStatus = 'sending' | 'sent' | 'delivered' | 'read' | 'failed';

// Extended message with status
export interface ChatMessageWithStatus extends ChatMessage {
  status: MessageStatus;
  error?: string;
}

// Message reaction
export interface MessageReaction {
  emoji: string;
  count: number;
  users: string[];
  userReacted: boolean;
}

// Extended message with reactions
export interface ChatMessageWithReactions extends ChatMessageWithStatus {
  reactions?: MessageReaction[];
}

// Message thread
export interface MessageThread {
  parentMessageId: string;
  messages: ChatMessage[];
  totalCount: number;
}

// Typing indicator
export interface TypingIndicator {
  userId: string;
  userName: string;
  avatar?: string;
  isTyping: boolean;
  roomId: string;
  timestamp: string;
}

// Message search result
export interface MessageSearchResult {
  message: ChatMessage;
  highlights: Array<{
    field: string;
    fragments: string[];
  }>;
  score: number;
}

// Message history pagination
export interface MessageHistoryParams {
  roomId: string;
  before?: string;
  after?: string;
  limit?: number;
  search?: string;
  contentType?: MessageContentType;
  senderId?: string;
  dateFrom?: string;
  dateTo?: string;
}

// Message history response
export interface MessageHistoryResponse {
  messages: ChatMessage[];
  hasMore: boolean;
  nextCursor?: string;
  prevCursor?: string;
  total: number;
}

// Export all types
export type {
  ChatMessage as default,
  MessageSender,
  MessageMetadata,
  MessageContent,
  MessageContentType,
  ContentData,
  TextContentData,
  MarkdownContentData,
  ImageContentData,
  VideoContentData,
  AudioContentData,
  LinkContentData,
  FileContentData,
  FormContentData,
  NavigationContentData,
  StreamContentData,
  FormField,
  FormSchema,
  MessageStatus,
  ChatMessageWithStatus,
  ChatMessageWithReactions,
  MessageReaction,
  MessageThread,
  TypingIndicator,
  MessageSearchResult,
  MessageHistoryParams,
  MessageHistoryResponse,
};
