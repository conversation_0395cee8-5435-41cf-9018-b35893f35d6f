import React, { useState } from 'react';

import {
  ConditionType,
  ProductDto,
  ProductFilter,
  ProductForm,
  ProductStatus,
  ProductType,
  SegmentDto,
  SegmentForm,
  SegmentStatus,
  SegmentType,
} from '@/modules/product';
import { Button, Card, ResponsiveGrid, Tabs, Typography } from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';

import ComponentDemo from '../../components/ComponentDemo';

/**
 * Demo page for Product components
 */
const ProductComponentsDemo: React.FC = () => {
  // State for product form demo
  const {
    isVisible: isProductFormVisible,
    showForm: showProductForm,
    hideForm: hideProductForm,
  } = useSlideForm();

  // State for segment form demo
  const {
    isVisible: isSegmentFormVisible,
    showForm: showSegmentForm,
    hideForm: hideSegmentForm,
  } = useSlideForm();

  // Mock data for product
  const mockProduct: ProductDto = {
    id: '1',
    name: 'Laptop Dell XPS 13',
    description: 'Laptop cao cấp với màn hình 13 inch, CPU Intel Core i7, RAM 16GB',
    price: 30000000,
    originalPrice: 32000000,
    discount: 6.25,
    images: ['https://picsum.photos/id/0/200/200'],
    mainImage: 'https://picsum.photos/id/0/200/200',
    status: ProductStatus.ACTIVE,
    type: ProductType.PHYSICAL,
    categoryId: '1',
    categoryName: 'Laptop',
    stock: 10,
    rating: 4.5,
    ratingCount: 120,
    createdAt: '2023-06-01T00:00:00Z',
    updatedAt: '2023-06-15T00:00:00Z',
  };

  // Mock data for segment
  const mockSegment: SegmentDto = {
    id: '1',
    name: 'Sản phẩm cao cấp',
    description: 'Các sản phẩm có giá trên 20 triệu đồng',
    type: SegmentType.PRICE_RANGE,
    status: SegmentStatus.ACTIVE,
    conditions: {
      operator: 'AND',
      conditions: [
        {
          field: 'price',
          type: ConditionType.GREATER_THAN,
          value: 20000000,
        },
      ],
    },
    productCount: 12,
    createdAt: '2023-06-01T00:00:00Z',
    updatedAt: '2023-06-15T00:00:00Z',
  };

  // Mock data for categories
  const categories = [
    { value: '1', label: 'Laptop' },
    { value: '2', label: 'Điện thoại' },
    { value: '3', label: 'Phần mềm' },
    { value: '4', label: 'Dịch vụ' },
  ];

  // State for filter demo
  const [filters, setFilters] = useState({});

  // Handle form submissions (just for demo)
  const handleProductSubmit = (values: any) => {
    console.log('Product form submitted:', values);
    hideProductForm();
  };

  const handleSegmentSubmit = (values: any) => {
    console.log('Segment form submitted:', values);
    hideSegmentForm();
  };

  const handleFilter = (newFilters: any) => {
    console.log('Filters applied:', newFilters);
    setFilters(newFilters);
  };

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-8">
        <h1 className="text-xl sm:text-2xl font-bold text-foreground mb-2">Product Components</h1>
        <p className="text-muted">Demo of components for the Product module</p>
      </div>

      <Tabs
        items={[
          {
            key: 'forms',
            label: 'Forms',
            children: (
              <div className="space-y-8">
                <ComponentDemo
                  title="Product Form"
                  description="Form for creating and editing products"
                  code={`import { ProductForm } from '@/modules/product';

<ProductForm 
  onSubmit={handleSubmit}
  onCancel={handleCancel}
  categories={categories}
/>`}
                >
                  <div className="flex justify-center">
                    <Button variant="primary" onClick={showProductForm}>
                      Show Product Form
                    </Button>
                  </div>
                </ComponentDemo>

                <ComponentDemo
                  title="Segment Form"
                  description="Form for creating and editing product segments"
                  code={`import { SegmentForm } from '@/modules/product';

<SegmentForm 
  onSubmit={handleSubmit}
  onCancel={handleCancel}
/>`}
                >
                  <div className="flex justify-center">
                    <Button variant="primary" onClick={showSegmentForm}>
                      Show Segment Form
                    </Button>
                  </div>
                </ComponentDemo>
              </div>
            ),
          },
          {
            key: 'filters',
            label: 'Filters',
            children: (
              <ComponentDemo
                title="Product Filter"
                description="Filter component for products"
                code={`import { ProductFilter } from '@/modules/product';

<ProductFilter 
  onFilter={handleFilter}
  categories={categories}
/>`}
              >
                <ProductFilter
                  onFilter={handleFilter}
                  currentFilters={filters}
                  categories={categories}
                />
              </ComponentDemo>
            ),
          },
          {
            key: 'cards',
            label: 'Cards',
            children: (
              <ComponentDemo
                title="Product Cards"
                description="Various card layouts for displaying products"
                code={`// Import product card components
// Display product cards in a responsive grid`}
              >
                <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 3, lg: 4 }} gap={4}>
                  {Array.from({ length: 4 }).map((_, index) => (
                    <Card key={index} className="p-4">
                      <img
                        src={`https://picsum.photos/id/${index}/200/200`}
                        alt={`Product ${index + 1}`}
                        className="w-full h-40 object-cover rounded-md mb-2"
                      />
                      <Typography variant="subtitle1" className="mb-1">
                        Demo Product {index + 1}
                      </Typography>
                      <Typography variant="body2" className="text-primary font-medium">
                        {new Intl.NumberFormat('vi-VN', {
                          style: 'currency',
                          currency: 'VND',
                        }).format(Math.floor(Math.random() * 10000000) + 1000000)}
                      </Typography>
                    </Card>
                  ))}
                </ResponsiveGrid>
              </ComponentDemo>
            ),
          },
        ]}
      />

      {/* Slide-in forms */}
      <SlideInForm isVisible={isProductFormVisible}>
        <ProductForm
          product={mockProduct}
          onSubmit={handleProductSubmit}
          onCancel={hideProductForm}
          categories={categories}
        />
      </SlideInForm>

      <SlideInForm isVisible={isSegmentFormVisible}>
        <SegmentForm
          segment={mockSegment}
          onSubmit={handleSegmentSubmit}
          onCancel={hideSegmentForm}
        />
      </SlideInForm>
    </div>
  );
};

export default ProductComponentsDemo;
