import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional } from 'class-validator';
import { KeyResultStatus } from '../../enum/key-result-status.enum';
import { QueryDto } from '@/common/dto/query.dto';

/**
 * DTO for querying key results
 */
export class KeyResultQueryDto extends QueryDto {
  /**
   * Filter by objective ID
   * @example 1
   */
  @ApiProperty({
    description: 'Lọc theo ID mục tiêu',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  objectiveId?: number;

  /**
   * Filter by key result status
   * @example "ACTIVE"
   */
  @ApiProperty({
    description: 'Lọc theo trạng thái kết quả chính',
    enum: KeyResultStatus,
    required: false,
  })
  @IsOptional()
  @IsEnum(KeyResultStatus)
  status?: KeyResultStatus;
}
