import React from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import * as z from 'zod';

import { Button, Form, FormItem, Input, Textarea } from '@/shared/components/common';
import { NotificationUtil } from '@/shared/utils/notification';
import { zodResolver } from '@hookform/resolvers/zod';

import { useCreateProject, useUpdateProject } from '../hooks/useProjects';
import { ProjectDto } from '../types/project.types';

// Định nghĩa schema validation
const projectSchema = z.object({
  title: z
    .string()
    .min(1, 'Tên dự án không được để trống')
    .max(255, 'Tên dự án không được vượt quá 255 ký tự'),
  description: z.string().max(1000, 'Mô tả không được vượt quá 1000 ký tự').optional().nullable(),
});

// Kiểu dữ liệu form
type ProjectFormValues = z.infer<typeof projectSchema>;

// Props component
interface ProjectFormProps {
  project?: ProjectDto;
  onSubmit: () => void;
  onCancel: () => void;
}

/**
 * Form tạo/cập nhật dự án
 */
const ProjectForm: React.FC<ProjectFormProps> = ({ project, onSubmit, onCancel }) => {
  const { t } = useTranslation(['common', 'todolist']);
  const isEditMode = !!project;

  // Hooks mutation
  const { mutateAsync: createProject, isPending: isCreating } = useCreateProject();
  const { mutateAsync: updateProject, isPending: isUpdating } = useUpdateProject();

  // Form hook
  const form = useForm<ProjectFormValues>({
    resolver: zodResolver(projectSchema),
    defaultValues: {
      title: project?.title || '',
      description: project?.description || '',
    },
  });

  // Xử lý submit form
  const handleSubmit = async (values: ProjectFormValues) => {
    try {
      if (isEditMode && project) {
        await updateProject({
          id: project.id,
          data: values,
        });
        NotificationUtil.success({
          message: t('todolist:project.notifications.updateSuccess', 'Cập nhật dự án thành công'),
        });
      } else {
        await createProject(values);
        NotificationUtil.success({
          message: t('todolist:project.notifications.createSuccess', 'Tạo dự án thành công'),
        });
      }
      onSubmit();
    } catch (error) {
      console.error('Error submitting project form:', error);
      NotificationUtil.error({
        message: isEditMode
          ? t('todolist:project.notifications.updateError', 'Lỗi khi cập nhật dự án')
          : t('todolist:project.notifications.createError', 'Lỗi khi tạo dự án'),
      });
    }
  };

  return (
    <Form form={form} onSubmit={handleSubmit} className="space-y-6">
      <FormItem name="title" label={t('todolist:project.fields.title', 'Tên dự án')} required>
        <Input placeholder={t('todolist:project.placeholders.title', 'Nhập tên dự án')} />
      </FormItem>

      <FormItem name="description" label={t('todolist:project.fields.description', 'Mô tả')}>
        <Textarea
          placeholder={t('todolist:project.placeholders.description', 'Nhập mô tả dự án')}
          rows={4}
        />
      </FormItem>

      <div className="flex justify-end space-x-3 pt-4">
        <Button variant="outline" onClick={onCancel} disabled={isCreating || isUpdating}>
          {t('common:cancel', 'Hủy')}
        </Button>
        <Button type="submit" isLoading={isCreating || isUpdating}>
          {isEditMode ? t('common:update', 'Cập nhật') : t('common:create', 'Tạo mới')}
        </Button>
      </div>
    </Form>
  );
};

export default ProjectForm;
