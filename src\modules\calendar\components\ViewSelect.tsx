import React, { useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Icon, Button } from '@/shared/components/common';
import { IconName } from '@/shared/components/common/Icon/Icon';
import { useIsMobile } from '@/shared/hooks/common';
import { cn } from '@/shared/utils/cn';

interface ViewSelectProps {
  currentView: string;
  onViewChange: (view: string) => void;
}

interface ViewOption {
  value: string;
  label: string;
  icon: IconName;
}

const ViewSelect: React.FC<ViewSelectProps> = ({ currentView, onViewChange }) => {
  const { t } = useTranslation(['common', 'calendar']);
  const isMobile = useIsMobile();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Define view options
  const viewOptions: ViewOption[] = [
    { value: 'dayGridMonth', label: t('calendar:month', 'Tháng'), icon: 'calendar' as IconName },
    { value: 'timeGridWeek', label: t('calendar:week', 'Tuần'), icon: 'layout' as IconName },
    { value: 'timeGridDay', label: t('calendar:day', 'Ngày'), icon: 'list' as IconName },
    { value: 'listWeek', label: t('calendar:list', 'Danh sách'), icon: 'document' as IconName },
  ];

  // Handle view change
  const handleViewChange = (view: string) => {
    onViewChange(view);
    setIsOpen(false);
  };

  // Get current view label
  const getCurrentViewLabel = () => {
    const currentOption = viewOptions.find(option => option.value === currentView);
    return currentOption ? currentOption.label : 'Tháng';
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className={cn('calendar-view-select relative', isMobile ? 'w-20' : 'w-32')} ref={dropdownRef}>
      <Button
        variant="primary"
        size="sm"
        className="calendar-view-select-button w-full justify-between"
        onClick={() => setIsOpen(!isOpen)}
        rightIcon={<Icon name={isOpen ? 'chevron-up' : 'chevron-down'} size="sm" />}
      >
        {getCurrentViewLabel()}
      </Button>

      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-card rounded-md shadow-lg z-50">
          {viewOptions.map((option) => (
            <button
              key={option.value}
              className={cn(
                'w-full px-3 py-2 text-left text-sm hover:bg-muted flex items-center gap-2',
                currentView === option.value ? 'bg-primary/10 text-primary' : 'text-foreground'
              )}
              onClick={() => handleViewChange(option.value)}
            >
              <Icon name={option.icon} size="sm" />
              {option.label}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default ViewSelect;
