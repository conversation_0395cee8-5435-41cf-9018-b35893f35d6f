import { QueryDto } from '@/shared/dto/request/query.dto';

/**
 * Enum trạng thái sản phẩm
 */
export enum ProductStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DRAFT = 'draft',
}

/**
 * Enum loại sản phẩm
 */
export enum ProductType {
  PHYSICAL = 'physical',
  DIGITAL = 'digital',
  SERVICE = 'service',
}

/**
 * Interface cho sản phẩm
 */
export interface ProductDto {
  /**
   * ID sản phẩm
   */
  id: string;

  /**
   * Tên sản phẩm
   */
  name: string;

  /**
   * <PERSON><PERSON> tả sản phẩm
   */
  description?: string;

  /**
   * Gi<PERSON> sản phẩm
   */
  price: number;

  /**
   * Gi<PERSON> gốc sản phẩm (trước khi giảm giá)
   */
  originalPrice?: number;

  /**
   * Phần trăm giảm giá
   */
  discount?: number;

  /**
   * Danh sách URL hình ảnh sản phẩm
   */
  images: string[];

  /**
   * Hình ảnh chính của sản phẩm
   */
  mainImage: string;

  /**
   * Trạng thái sản phẩm
   */
  status: ProductStatus;

  /**
   * Loại sản phẩm
   */
  type: ProductType;

  /**
   * ID danh mục sản phẩm
   */
  categoryId?: string;

  /**
   * Tên danh mục sản phẩm
   */
  categoryName?: string;

  /**
   * Danh sách tags
   */
  tags?: string[];

  /**
   * Số lượng tồn kho
   */
  stock?: number;

  /**
   * Đánh giá trung bình
   */
  rating?: number;

  /**
   * Số lượng đánh giá
   */
  ratingCount?: number;

  /**
   * Ngày tạo
   */
  createdAt: string;

  /**
   * Ngày cập nhật
   */
  updatedAt: string;
}

/**
 * Interface cho tham số truy vấn danh sách sản phẩm
 */
export interface ProductQueryDto extends QueryDto {
  /**
   * Lọc theo trạng thái
   */
  status?: ProductStatus;

  /**
   * Lọc theo loại sản phẩm
   */
  type?: ProductType;

  /**
   * Lọc theo danh mục
   */
  categoryId?: string;

  /**
   * Lọc theo khoảng giá tối thiểu
   */
  minPrice?: number;

  /**
   * Lọc theo khoảng giá tối đa
   */
  maxPrice?: number;

  /**
   * Lọc theo tag
   */
  tag?: string;
}

/**
 * Interface cho dữ liệu tạo sản phẩm
 */
export interface CreateProductDto {
  /**
   * Tên sản phẩm
   */
  name: string;

  /**
   * Mô tả sản phẩm
   */
  description?: string;

  /**
   * Giá sản phẩm
   */
  price: number;

  /**
   * Giá gốc sản phẩm (trước khi giảm giá)
   */
  originalPrice?: number;

  /**
   * Danh sách URL hình ảnh sản phẩm
   */
  images?: string[];

  /**
   * Trạng thái sản phẩm
   */
  status?: ProductStatus;

  /**
   * Loại sản phẩm
   */
  type: ProductType;

  /**
   * ID danh mục sản phẩm
   */
  categoryId?: string;

  /**
   * Danh sách tags
   */
  tags?: string[];

  /**
   * Số lượng tồn kho
   */
  stock?: number;
}

/**
 * Interface cho dữ liệu cập nhật sản phẩm
 */
export interface UpdateProductDto extends Partial<CreateProductDto> {
  /**
   * ID sản phẩm
   */
  id: string;
}
