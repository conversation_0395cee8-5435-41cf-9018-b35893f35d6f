import { apiClient } from '@/shared/api';

import { UserLoginRequest, UserLoginResponse } from '../types/user-auth.types';

/**
 * Service xử lý các API liên quan đến xác thực người dùng
 */
export const UserAuthService = {
  /**
   * Đăng nhập tài khoản người dùng
   * @param data Thông tin đăng nhập
   * @returns Kết quả đăng nhập
   */
  login: (data: UserLoginRequest) => {
    return apiClient.post<UserLoginResponse>('/auth/user/login', data);
  },

  /**
   * Lấy thông tin người dùng hiện tại
   * @returns Thông tin người dùng
   */
  getProfile: () => {
    return apiClient.get<UserLoginResponse>('/auth/user/profile');
  },
};
