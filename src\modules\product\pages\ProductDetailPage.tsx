import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams, useNavigate } from 'react-router-dom';

import {
  Card,
  Button,
  Tabs,
  Badge,
  Icon,
  Typography,
  Divider,
  ResponsiveGrid,
} from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { formatDate } from '@/shared/utils/date';
import { formatCurrency } from '@/shared/utils/format';

import ProductForm from '../components/forms/ProductForm';
import { ProductDto, ProductStatus, ProductType } from '../types/product.types';

/**
 * Trang chi tiết sản phẩm
 */
const ProductDetailPage: React.FC = () => {
  const { t } = useTranslation(['product']);
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  // State cho form
  const { isVisible, showForm, hideForm } = useSlideForm();

  // Mock data cho sản phẩm
  const [product, setProduct] = useState<ProductDto | null>(null);
  const [loading, setLoading] = useState(true);

  // Mock data cho danh mục
  const categories = [
    { value: '1', label: 'Laptop' },
    { value: '2', label: 'Điện thoại' },
    { value: '3', label: 'Phần mềm' },
    { value: '4', label: 'Dịch vụ' },
  ];

  // Lấy thông tin sản phẩm
  useEffect(() => {
    // Giả lập gọi API
    setTimeout(() => {
      // Mock data
      const mockProduct: ProductDto = {
        id: id || '1',
        name: 'Laptop Dell XPS 13',
        description:
          'Laptop cao cấp với màn hình 13 inch, CPU Intel Core i7, RAM 16GB, SSD 512GB. Thiết kế mỏng nhẹ, sang trọng, phù hợp cho công việc và giải trí.',
        price: 30000000,
        originalPrice: 32000000,
        discount: 6.25,
        images: [
          'https://picsum.photos/id/0/800/600',
          'https://picsum.photos/id/1/800/600',
          'https://picsum.photos/id/2/800/600',
        ],
        mainImage: 'https://picsum.photos/id/0/800/600',
        status: ProductStatus.ACTIVE,
        type: ProductType.PHYSICAL,
        categoryId: '1',
        categoryName: 'Laptop',
        stock: 10,
        rating: 4.5,
        ratingCount: 120,
        tags: ['laptop', 'dell', 'premium'],
        createdAt: '2023-06-01T00:00:00Z',
        updatedAt: '2023-06-15T00:00:00Z',
      };

      setProduct(mockProduct);
      setLoading(false);
    }, 500);
  }, [id]);

  // Xử lý cập nhật sản phẩm
  const handleUpdateProduct = (values: any) => {
    if (product) {
      setProduct({
        ...product,
        ...values,
        updatedAt: new Date().toISOString(),
      });
    }
    hideForm();
  };

  // Xử lý quay lại
  const handleBack = () => {
    navigate('/product');
  };

  // Nếu đang tải
  if (loading) {
    return (
      <Card>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </Card>
    );
  }

  // Nếu không tìm thấy sản phẩm
  if (!product) {
    return (
      <Card>
        <div className="flex flex-col items-center justify-center h-64">
          <Icon name="alert-triangle" size="xl" className="text-warning mb-4" />
          <Typography variant="h5" className="mb-2">
            {t('product:detail.notFound')}
          </Typography>
          <Button variant="outline" onClick={handleBack}>
            {t('product:common.back')}
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <Button variant="outline" onClick={handleBack} leftIcon={<Icon name="arrow-left" size="sm" />}>
          {t('product:common.back')}
        </Button>
        <Button variant="primary" onClick={showForm} leftIcon={<Icon name="edit" size="sm" />}>
          {t('product:common.edit')}
        </Button>
      </div>

      <Card className="mb-4">
        <div className="flex flex-col md:flex-row gap-6">
          {/* Hình ảnh sản phẩm */}
          <div className="md:w-1/3">
            <div className="mb-4">
              <img
                src={product.mainImage}
                alt={product.name}
                className="w-full h-auto rounded-lg object-cover"
              />
            </div>
            <div className="flex gap-2 overflow-x-auto pb-2">
              {product.images.map((image, index) => (
                <img
                  key={index}
                  src={image}
                  alt={`${product.name} ${index + 1}`}
                  className="w-20 h-20 rounded-md object-cover cursor-pointer"
                />
              ))}
            </div>
          </div>

          {/* Thông tin sản phẩm */}
          <div className="md:w-2/3">
            <div className="flex items-center gap-2 mb-2">
              <Badge
                variant={
                  product.status === ProductStatus.ACTIVE
                    ? 'success'
                    : product.status === ProductStatus.INACTIVE
                    ? 'danger'
                    : 'warning'
                }
              >
                {t(`product:common.${product.status}`)}
              </Badge>
              <Badge variant="info">{t(`product:common.${product.type}`)}</Badge>
            </div>

            <Typography variant="h4" className="mb-2">
              {product.name}
            </Typography>

            <div className="flex items-center gap-2 mb-4">
              <Typography variant="h5" className="text-primary font-bold">
                {formatCurrency(product.price)}
              </Typography>
              {product.originalPrice && (
                <>
                  <Typography variant="body2" className="text-muted line-through">
                    {formatCurrency(product.originalPrice)}
                  </Typography>
                  <Badge variant="danger">-{product.discount}%</Badge>
                </>
              )}
            </div>

            {product.rating && (
              <div className="flex items-center gap-1 mb-4">
                <div className="flex">
                  {Array.from({ length: 5 }).map((_, index) => (
                    <Icon
                      key={index}
                      name="star"
                      size="sm"
                      className={index < Math.floor(product.rating || 0) ? 'text-yellow-500' : 'text-gray-300'}
                    />
                  ))}
                </div>
                <Typography variant="body2">
                  {product.rating} ({product.ratingCount} đánh giá)
                </Typography>
              </div>
            )}

            <Divider className="my-4" />

            <div className="space-y-4">
              <div>
                <Typography variant="subtitle1" className="font-medium mb-1">
                  {t('product:detail.basicInfo')}
                </Typography>
                <Typography variant="body2">{product.description}</Typography>
              </div>

              <div>
                <Typography variant="subtitle1" className="font-medium mb-1">
                  {t('product:detail.categories')}
                </Typography>
                <Typography variant="body2">{product.categoryName || '-'}</Typography>
              </div>

              {product.tags && product.tags.length > 0 && (
                <div>
                  <Typography variant="subtitle1" className="font-medium mb-1">
                    {t('product:detail.tags')}
                  </Typography>
                  <div className="flex flex-wrap gap-1">
                    {product.tags.map((tag, index) => (
                      <Badge key={index} variant="secondary">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {product.type === ProductType.PHYSICAL && (
                <div>
                  <Typography variant="subtitle1" className="font-medium mb-1">
                    {t('product:detail.inventory')}
                  </Typography>
                  <Typography variant="body2">
                    {t('product:table.stock')}: {product.stock || 0}
                  </Typography>
                </div>
              )}

              <div>
                <Typography variant="subtitle1" className="font-medium mb-1">
                  {t('product:detail.history')}
                </Typography>
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <Typography variant="caption" className="text-muted">
                      {t('product:table.createdAt')}
                    </Typography>
                    <Typography variant="body2">{formatDate(product.createdAt)}</Typography>
                  </div>
                  <div>
                    <Typography variant="caption" className="text-muted">
                      {t('product:table.updatedAt')}
                    </Typography>
                    <Typography variant="body2">{formatDate(product.updatedAt)}</Typography>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Card>

      <Tabs
        items={[
          {
            key: 'details',
            label: t('product:detail.basicInfo'),
            children: (
              <Card>
                <Typography variant="body1">{product.description}</Typography>
              </Card>
            ),
          },
          {
            key: 'related',
            label: t('product:detail.relatedProducts'),
            children: (
              <Card>
                <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 3 }} gap={4}>
                  {Array.from({ length: 3 }).map((_, index) => (
                    <Card key={index} className="p-4">
                      <img
                        src={`https://picsum.photos/id/${index + 10}/200/200`}
                        alt={`Related product ${index + 1}`}
                        className="w-full h-40 object-cover rounded-md mb-2"
                      />
                      <Typography variant="subtitle1" className="mb-1">
                        Sản phẩm liên quan {index + 1}
                      </Typography>
                      <Typography variant="body2" className="text-primary font-medium">
                        {formatCurrency(Math.floor(Math.random() * 10000000) + 1000000)}
                      </Typography>
                    </Card>
                  ))}
                </ResponsiveGrid>
              </Card>
            ),
          },
          {
            key: 'reviews',
            label: t('product:detail.reviews'),
            children: (
              <Card>
                <div className="text-center py-8">
                  <Typography variant="body1">Chưa có đánh giá nào</Typography>
                </div>
              </Card>
            ),
          },
        ]}
      />

      <SlideInForm isVisible={isVisible}>
        <ProductForm
          product={product}
          onSubmit={handleUpdateProduct}
          onCancel={hideForm}
          categories={categories}
        />
      </SlideInForm>
    </div>
  );
};

export default ProductDetailPage;
