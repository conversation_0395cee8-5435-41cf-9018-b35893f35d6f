import DatePicker, { DatePickerFormField } from './DatePicker';
import DateTimePicker from './DateTimePicker';
import RangePicker from './RangePicker';
import Calendar from './Calendar';
import TimeScrollWheel from './TimeScrollWheel';
import MultiSelectCalendar from './MultiSelectCalendar';
import EventCalendar from './EventCalendar';
import AnimatedCalendar from './AnimatedCalendar';
import AdvancedRangePicker from './AdvancedRangePicker';
import PresetRanges from './PresetRanges';
import CalendarThemeProvider, { CalendarThemeCustomizer } from './CalendarThemeProvider';
import TimeZoneCalendar from './TimeZoneCalendar';
import RecurringEventCalendar from './RecurringEventCalendar';

export {
  DatePicker,
  DatePickerFormField,
  DateTimePicker,
  RangePicker,
  Calendar,
  TimeScrollWheel,
  MultiSelectCalendar,
  EventCalendar,
  AnimatedCalendar,
  AdvancedRangePicker,
  PresetRanges,
  CalendarThemeProvider,
  CalendarThemeCustomizer,
  TimeZoneCalendar,
  RecurringEventCalendar,
};

export default DatePicker;

// Export types
export type {
  DatePickerProps,
  DateTimePickerProps,
  RangePickerProps,
  CalendarProps
} from './types';

export type { TimeScrollWheelProps } from './TimeScrollWheel';



export type { DatePickerFormFieldProps } from './DatePicker';

export type {
  MultiSelectCalendarProps
} from './MultiSelectCalendar';

export type {
  EventCalendarProps,
  CalendarEvent
} from './EventCalendar';

export type {
  AnimatedCalendarProps
} from './AnimatedCalendar';

export type {
  AdvancedRangePickerProps
} from './AdvancedRangePicker';

export type {
  PresetRange,
  PresetRangesProps
} from './PresetRanges';

export type {
  CalendarThemeProviderProps,
  CalendarThemeCustomizerProps
} from './CalendarThemeProvider';

export type {
  TimeZoneCalendarProps
} from './TimeZoneCalendar';

export type {
  RecurringEventCalendarProps,
  RecurringEvent,
  RecurrenceRule,
  RecurrencePattern
} from './RecurringEventCalendar';

// Export theme types
export type * from './types/theme.types';

// Export hooks
export * from './hooks';

// Export theme utilities
export { defaultThemes, createThemeWithColorScheme } from './constants/defaultThemes';
export { withCalendarTheme } from './utils/withCalendarTheme';
