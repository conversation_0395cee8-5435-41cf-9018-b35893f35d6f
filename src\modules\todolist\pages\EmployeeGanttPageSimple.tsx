import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import {
  <PERSON><PERSON>,
  <PERSON>ton,
  Card,
  Icon,
  RangePicker,
  Select,
  Table,
  Typography,
} from '@/shared/components/common';
import { formatDate } from '@/shared/utils/date';

import { useProjects } from '../hooks/useProjects';
import { useProjectGantt } from '../hooks/useStatistics';
import { useTasks } from '../hooks/useTasks';
import { TaskStatus } from '../types/task.types';

// Dữ liệu mẫu cho nhân viên
const SAMPLE_EMPLOYEES = [
  { id: 1, name: '<PERSON><PERSON><PERSON><PERSON>n <PERSON>' },
  { id: 2, name: '<PERSON><PERSON><PERSON><PERSON>h<PERSON>' },
  { id: 3, name: '<PERSON><PERSON>' },
];

// D<PERSON> liệu mẫu cho công việc
const SAMPLE_TASKS = [
  {
    id: 1,
    title: '<PERSON><PERSON><PERSON><PERSON> kế giao diện',
    assigneeId: 1,
    status: TaskStatus.COMPLETED,
    createdAt: Date.now() - 10 * 24 * 60 * 60 * 1000, // 10 ngày trước
    completedAt: Date.now() - 7 * 24 * 60 * 60 * 1000, // 7 ngày trước
  },
  {
    id: 2,
    title: 'Phát triển backend',
    assigneeId: 2,
    status: TaskStatus.IN_PROGRESS,
    createdAt: Date.now() - 8 * 24 * 60 * 60 * 1000, // 8 ngày trước
    completedAt: null,
  },
  {
    id: 3,
    title: 'Kiểm thử ứng dụng',
    assigneeId: 3,
    status: TaskStatus.PENDING,
    createdAt: Date.now() - 5 * 24 * 60 * 60 * 1000, // 5 ngày trước
    completedAt: null,
  },
  {
    id: 4,
    title: 'Tối ưu hiệu suất',
    assigneeId: 1,
    status: TaskStatus.IN_PROGRESS,
    createdAt: Date.now() - 3 * 24 * 60 * 60 * 1000, // 3 ngày trước
    completedAt: null,
  },
  {
    id: 5,
    title: 'Triển khai lên server',
    assigneeId: 2,
    status: TaskStatus.PENDING,
    createdAt: Date.now() - 2 * 24 * 60 * 60 * 1000, // 2 ngày trước
    completedAt: null,
  },
];

/**
 * Trang hiển thị thời gian hoạt động và thời gian trống của nhân viên
 * (Phiên bản đơn giản không sử dụng thư viện Gantt)
 */
const EmployeeGanttPageSimple: React.FC = () => {
  const { t } = useTranslation(['common', 'todolist']);
  const [selectedProjectId, setSelectedProjectId] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null]);

  // Lấy danh sách dự án
  const { data: projectsData, isLoading: isLoadingProjects } = useProjects();

  // Chuyển đổi dateRange thành timestamp
  const startDate = dateRange[0] ? dateRange[0].getTime() : undefined;
  const endDate = dateRange[1] ? dateRange[1].getTime() : undefined;

  // Lấy dữ liệu Gantt của dự án
  const { data: projectGantt, isLoading: isLoadingProjectGantt } = useProjectGantt(
    selectedProjectId ? parseInt(selectedProjectId, 10) : 0
  );

  // Lấy danh sách công việc
  const { data: tasksData, isLoading: isLoadingTasks } = useTasks({
    projectId: selectedProjectId ? parseInt(selectedProjectId, 10) : undefined,
    startDate,
    endDate,
    limit: 1000,
  });

  // Chuẩn bị danh sách nhân viên từ dữ liệu dự án hoặc dữ liệu mẫu
  const employees = useMemo(() => {
    // Nếu có dữ liệu dự án, sử dụng dữ liệu thực
    if (projectGantt?.members && projectGantt.members.length > 0) {
      return projectGantt.members.map(member => ({
        id: member.userId,
        name: `User ${member.userId}`, // Trong thực tế, cần lấy tên thật của người dùng
      }));
    }

    // Nếu đã chọn dự án nhưng không có dữ liệu, sử dụng dữ liệu mẫu
    if (selectedProjectId) {
      return SAMPLE_EMPLOYEES;
    }

    // Nếu chưa chọn dự án, trả về mảng rỗng
    return [];
  }, [projectGantt, selectedProjectId]);

  // Chuẩn bị dữ liệu cho bảng thời gian
  const timelineData = useMemo(() => {
    if (!employees.length) {return [];}

    // Xác định danh sách công việc (thực hoặc mẫu)
    const tasks =
      tasksData?.items?.length > 0 ? tasksData.items : selectedProjectId ? SAMPLE_TASKS : [];

    if (!tasks.length) {return [];}

    const result = [];

    // Xác định khoảng thời gian
    const start = dateRange[0]
      ? dateRange[0]
      : tasks.length > 0
        ? new Date(Math.min(...tasks.filter(t => t.createdAt).map(t => t.createdAt || Date.now())))
        : new Date(Date.now() - 14 * 24 * 60 * 60 * 1000); // Mặc định 2 tuần trước

    const end = dateRange[1]
      ? dateRange[1]
      : tasks.length > 0
        ? new Date(
            Math.max(...tasks.filter(t => t.completedAt).map(t => t.completedAt || Date.now()))
          )
        : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // Mặc định 1 tuần sau

    // Tạo dữ liệu cho mỗi nhân viên
    for (const employee of employees) {
      const employeeTasks = tasks
        .filter(task => task.assigneeId === employee.id)
        .sort((a, b) => {
          const aStart = a.createdAt || 0;
          const bStart = b.createdAt || 0;
          return aStart - bStart;
        });

      // Nếu không có công việc nào, thêm một khoảng trống từ start đến end
      if (employeeTasks.length === 0) {
        result.push({
          employeeId: employee.id,
          employeeName: employee.name,
          taskId: null,
          taskTitle: t('todolist:employeeGantt.freeTime', 'Thời gian trống'),
          startDate: start,
          endDate: end,
          status: 'FREE_TIME',
          duration: Math.round((end.getTime() - start.getTime()) / (24 * 60 * 60 * 1000)),
        });
        continue;
      }

      // Thêm khoảng trống từ start đến công việc đầu tiên
      const firstTask = employeeTasks[0];
      const firstTaskStart = firstTask.createdAt ? new Date(firstTask.createdAt) : new Date(start);

      if (firstTaskStart.getTime() > start.getTime()) {
        result.push({
          employeeId: employee.id,
          employeeName: employee.name,
          taskId: null,
          taskTitle: t('todolist:employeeGantt.freeTime', 'Thời gian trống'),
          startDate: start,
          endDate: firstTaskStart,
          status: 'FREE_TIME',
          duration: Math.round(
            (firstTaskStart.getTime() - start.getTime()) / (24 * 60 * 60 * 1000)
          ),
        });
      }

      // Thêm các công việc và khoảng trống giữa chúng
      for (let i = 0; i < employeeTasks.length; i++) {
        const currentTask = employeeTasks[i];

        const taskStart = currentTask.createdAt ? new Date(currentTask.createdAt) : new Date(start);
        const taskEnd = currentTask.completedAt
          ? new Date(currentTask.completedAt)
          : new Date(taskStart.getTime() + 24 * 60 * 60 * 1000); // Mặc định 1 ngày

        // Thêm công việc
        result.push({
          employeeId: employee.id,
          employeeName: employee.name,
          taskId: currentTask.id,
          taskTitle: currentTask.title,
          startDate: taskStart,
          endDate: taskEnd,
          status: currentTask.status,
          duration: Math.round((taskEnd.getTime() - taskStart.getTime()) / (24 * 60 * 60 * 1000)),
        });

        // Nếu không phải công việc cuối cùng, kiểm tra khoảng trống đến công việc tiếp theo
        if (i < employeeTasks.length - 1) {
          const nextTask = employeeTasks[i + 1];
          const nextTaskStart = nextTask.createdAt ? new Date(nextTask.createdAt) : new Date(end);

          // Nếu có khoảng trống giữa hai công việc
          if (nextTaskStart.getTime() > taskEnd.getTime()) {
            result.push({
              employeeId: employee.id,
              employeeName: employee.name,
              taskId: null,
              taskTitle: t('todolist:employeeGantt.freeTime', 'Thời gian trống'),
              startDate: taskEnd,
              endDate: nextTaskStart,
              status: 'FREE_TIME',
              duration: Math.round(
                (nextTaskStart.getTime() - taskEnd.getTime()) / (24 * 60 * 60 * 1000)
              ),
            });
          }
        }
      }

      // Thêm khoảng trống từ công việc cuối cùng đến end
      const lastTask = employeeTasks[employeeTasks.length - 1];
      const lastTaskEnd = lastTask.completedAt
        ? new Date(lastTask.completedAt)
        : lastTask.createdAt
          ? new Date(lastTask.createdAt.getTime() + 24 * 60 * 60 * 1000)
          : new Date(start);

      if (lastTaskEnd.getTime() < end.getTime()) {
        result.push({
          employeeId: employee.id,
          employeeName: employee.name,
          taskId: null,
          taskTitle: t('todolist:employeeGantt.freeTime', 'Thời gian trống'),
          startDate: lastTaskEnd,
          endDate: end,
          status: 'FREE_TIME',
          duration: Math.round((end.getTime() - lastTaskEnd.getTime()) / (24 * 60 * 60 * 1000)),
        });
      }
    }

    return result;
  }, [tasksData, employees, dateRange, t, selectedProjectId, SAMPLE_TASKS]);

  // Xử lý thay đổi dự án
  const handleProjectChange = (value: string | number | string[] | number[]) => {
    if (typeof value === 'string') {
      setSelectedProjectId(value);
    } else if (typeof value === 'number') {
      setSelectedProjectId(value.toString());
    }
  };

  // Xử lý thay đổi khoảng thời gian
  const handleDateRangeChange = (range: [Date | null, Date | null]) => {
    setDateRange(range);
  };

  // Xử lý đặt lại bộ lọc
  const handleResetFilters = () => {
    setSelectedProjectId(null);
    setDateRange([null, null]);
  };

  // Cột cho bảng thời gian
  const columns = [
    {
      header: t('todolist:employeeGantt.employee', 'Nhân viên'),
      accessorKey: 'employeeName',
    },
    {
      header: t('todolist:employeeGantt.task', 'Công việc'),
      accessorKey: 'taskTitle',
    },
    {
      header: t('todolist:employeeGantt.status', 'Trạng thái'),
      accessorKey: 'status',
      cell: ({ row }: any) => {
        const status = row.original.status;

        if (status === 'FREE_TIME') {
          return (
            <Badge color="secondary">
              {t('todolist:employeeGantt.freeTime', 'Thời gian trống')}
            </Badge>
          );
        }

        if (status === TaskStatus.COMPLETED || status === TaskStatus.APPROVED) {
          return <Badge color="success">{t('todolist:task.status.done', 'Hoàn thành')}</Badge>;
        }

        if (status === TaskStatus.IN_PROGRESS) {
          return (
            <Badge color="primary">{t('todolist:task.status.inProgress', 'Đang thực hiện')}</Badge>
          );
        }

        return <Badge color="warning">{t('todolist:task.status.todo', 'Chưa bắt đầu')}</Badge>;
      },
    },
    {
      header: t('todolist:employeeGantt.startDate', 'Ngày bắt đầu'),
      accessorKey: 'startDate',
      cell: ({ row }: any) => formatDate(row.original.startDate),
    },
    {
      header: t('todolist:employeeGantt.endDate', 'Ngày kết thúc'),
      accessorKey: 'endDate',
      cell: ({ row }: any) => formatDate(row.original.endDate),
    },
    {
      header: t('todolist:employeeGantt.duration', 'Thời gian (ngày)'),
      accessorKey: 'duration',
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <Typography variant="h3" className="mb-2">
            {t('todolist:employeeGantt.title', 'Thời gian hoạt động nhân viên')}
          </Typography>
          <Typography variant="body1" color="muted">
            {t(
              'todolist:employeeGantt.description',
              'Xem thời gian hoạt động và thời gian trống của nhân viên'
            )}
          </Typography>
        </div>
      </div>

      <Card className="p-4">
        <div className="flex flex-wrap gap-4">
          <div className="w-full md:w-64">
            <Typography variant="body2" className="mb-1">
              {t('todolist:statistics.filters.project', 'Dự án')}
            </Typography>
            <Select
              placeholder={t('todolist:statistics.filters.selectProject', 'Chọn dự án')}
              onChange={handleProjectChange}
              value={selectedProjectId || ''}
              options={
                projectsData?.items.map(project => ({
                  value: project.id.toString(),
                  label: project.title,
                })) || []
              }
              loading={isLoadingProjects}
              fullWidth
            />
          </div>
          <div className="w-full md:w-auto flex-grow">
            <Typography variant="body2" className="mb-1">
              {t('todolist:statistics.filters.dateRange', 'Khoảng thời gian')}
            </Typography>
            <RangePicker
              value={dateRange}
              onChange={handleDateRangeChange}
              placeholder={[
                t('todolist:statistics.filters.startDate', 'Ngày bắt đầu'),
                t('todolist:statistics.filters.endDate', 'Ngày kết thúc'),
              ]}
              fullWidth
            />
          </div>
          <div className="w-full md:w-auto flex items-end">
            <Button
              variant="outline"
              leftIcon={<Icon name="refresh" size="sm" />}
              onClick={handleResetFilters}
            >
              {t('common:reset', 'Đặt lại')}
            </Button>
          </div>
        </div>
      </Card>

      <Card className="p-4">
        <Typography variant="h6" className="mb-4">
          {t('todolist:employeeGantt.timelineTitle', 'Dòng thời gian hoạt động')}
        </Typography>

        {selectedProjectId ? (
          <>
            {timelineData.length > 0 ? (
              <Table
                columns={columns}
                data={timelineData}
                isLoading={isLoadingTasks || isLoadingProjectGantt}
                pagination
              />
            ) : (
              <div className="h-40 flex items-center justify-center">
                <Typography variant="body2" color="muted">
                  {t('todolist:statistics.noData', 'Không có dữ liệu')}
                  <br />
                  <span className="text-xs mt-2 block">
                    {t('todolist:employeeGantt.usingSampleData', 'Đang sử dụng dữ liệu mẫu')}
                  </span>
                </Typography>
              </div>
            )}
            <div className="mt-4 p-2 bg-blue-50 dark:bg-blue-900/20 rounded-md">
              <Typography variant="caption" color="primary">
                <Icon name="info" size="sm" className="mr-1" />
                {t(
                  'todolist:employeeGantt.sampleDataNote',
                  'Lưu ý: Nếu không có dữ liệu thực, hệ thống sẽ hiển thị dữ liệu mẫu để minh họa.'
                )}
              </Typography>
            </div>
          </>
        ) : (
          <div className="h-40 flex items-center justify-center">
            <Typography variant="body1" color="muted">
              {t(
                'todolist:employeeGantt.selectProject',
                'Vui lòng chọn một dự án để xem thời gian hoạt động'
              )}
            </Typography>
          </div>
        )}
      </Card>

      {selectedProjectId && (
        <Card className="p-4">
          <Typography variant="h6" className="mb-4">
            {t('todolist:employeeGantt.legend', 'Chú thích')}
          </Typography>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="flex items-center">
              <Badge color="warning" className="mr-2" />
              <Typography variant="body2">
                {t('todolist:task.status.todo', 'Chưa bắt đầu')}
              </Typography>
            </div>
            <div className="flex items-center">
              <Badge color="primary" className="mr-2" />
              <Typography variant="body2">
                {t('todolist:task.status.inProgress', 'Đang thực hiện')}
              </Typography>
            </div>
            <div className="flex items-center">
              <Badge color="success" className="mr-2" />
              <Typography variant="body2">
                {t('todolist:task.status.done', 'Đã hoàn thành')}
              </Typography>
            </div>
            <div className="flex items-center">
              <Badge color="secondary" className="mr-2" />
              <Typography variant="body2">
                {t('todolist:employeeGantt.freeTime', 'Thời gian trống')}
              </Typography>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};

export default EmployeeGanttPageSimple;
