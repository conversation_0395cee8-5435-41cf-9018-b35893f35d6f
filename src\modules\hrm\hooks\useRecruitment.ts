/**
 * Custom hooks cho module Recruitment
 */
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import {
  JobPositionService,
  CandidateService,
  RecruitmentProcessService,
  RecruitmentStatsService,
} from '../services/recruitment.service';
import {
  JobPositionQueryDto,
  CandidateQueryDto,
  CreateJobPositionDto,
  UpdateJobPositionDto,
  CreateCandidateDto,
  UpdateCandidateDto,
} from '../types/recruitment.types';

// Query keys
export const RECRUITMENT_QUERY_KEYS = {
  JOB_POSITIONS: 'job-positions',
  JOB_POSITION: (id: string) => ['job-position', id],
  CANDIDATES: 'candidates',
  CANDIDATE: (id: string) => ['candidate', id],
  RECRUITMENT_PROCESSES: 'recruitment-processes',
  RECRUITMENT_PROCESS: (id: string) => ['recruitment-process', id],
  RECRUITMENT_STATS: 'recruitment-stats',
};

/**
 * Hook để lấy danh sách vị trí tuyển dụng
 */
export const useJobPositions = (params?: JobPositionQueryDto) => {
  return useQuery({
    queryKey: [RECRUITMENT_QUERY_KEYS.JOB_POSITIONS, params],
    queryFn: () => JobPositionService.getJobPositions(params),
    select: data => data.result,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để lấy chi tiết vị trí tuyển dụng
 */
export const useJobPosition = (id: string) => {
  return useQuery({
    queryKey: RECRUITMENT_QUERY_KEYS.JOB_POSITION(id),
    queryFn: () => JobPositionService.getJobPosition(id),
    select: data => data.result,
    enabled: !!id,
  });
};

/**
 * Hook để tạo vị trí tuyển dụng mới
 */
export const useCreateJobPosition = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateJobPositionDto) => JobPositionService.createJobPosition(data),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [RECRUITMENT_QUERY_KEYS.JOB_POSITIONS],
      });
      queryClient.invalidateQueries({
        queryKey: [RECRUITMENT_QUERY_KEYS.RECRUITMENT_STATS],
      });
    },
  });
};

/**
 * Hook để cập nhật vị trí tuyển dụng
 */
export const useUpdateJobPosition = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateJobPositionDto }) =>
      JobPositionService.updateJobPosition(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({
        queryKey: [RECRUITMENT_QUERY_KEYS.JOB_POSITIONS],
      });
      queryClient.invalidateQueries({
        queryKey: RECRUITMENT_QUERY_KEYS.JOB_POSITION(id),
      });
      queryClient.invalidateQueries({
        queryKey: [RECRUITMENT_QUERY_KEYS.RECRUITMENT_STATS],
      });
    },
  });
};

/**
 * Hook để xóa vị trí tuyển dụng
 */
export const useDeleteJobPosition = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => JobPositionService.deleteJobPosition(id),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [RECRUITMENT_QUERY_KEYS.JOB_POSITIONS],
      });
      queryClient.invalidateQueries({
        queryKey: [RECRUITMENT_QUERY_KEYS.RECRUITMENT_STATS],
      });
    },
  });
};

/**
 * Hook để publish vị trí tuyển dụng
 */
export const usePublishJobPosition = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => JobPositionService.publishJobPosition(id),
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({
        queryKey: [RECRUITMENT_QUERY_KEYS.JOB_POSITIONS],
      });
      queryClient.invalidateQueries({
        queryKey: RECRUITMENT_QUERY_KEYS.JOB_POSITION(id),
      });
    },
  });
};

/**
 * Hook để close vị trí tuyển dụng
 */
export const useCloseJobPosition = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => JobPositionService.closeJobPosition(id),
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({
        queryKey: [RECRUITMENT_QUERY_KEYS.JOB_POSITIONS],
      });
      queryClient.invalidateQueries({
        queryKey: RECRUITMENT_QUERY_KEYS.JOB_POSITION(id),
      });
    },
  });
};

/**
 * Hook để lấy danh sách ứng viên
 */
export const useCandidates = (params?: CandidateQueryDto) => {
  return useQuery({
    queryKey: [RECRUITMENT_QUERY_KEYS.CANDIDATES, params],
    queryFn: () => CandidateService.getCandidates(params),
    select: data => data.result,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để lấy chi tiết ứng viên
 */
export const useCandidate = (id: string) => {
  return useQuery({
    queryKey: RECRUITMENT_QUERY_KEYS.CANDIDATE(id),
    queryFn: () => CandidateService.getCandidate(id),
    select: data => data.result,
    enabled: !!id,
  });
};

/**
 * Hook để tạo ứng viên mới
 */
export const useCreateCandidate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateCandidateDto) => CandidateService.createCandidate(data),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [RECRUITMENT_QUERY_KEYS.CANDIDATES],
      });
      queryClient.invalidateQueries({
        queryKey: [RECRUITMENT_QUERY_KEYS.RECRUITMENT_STATS],
      });
    },
  });
};

/**
 * Hook để cập nhật ứng viên
 */
export const useUpdateCandidate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateCandidateDto }) =>
      CandidateService.updateCandidate(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({
        queryKey: [RECRUITMENT_QUERY_KEYS.CANDIDATES],
      });
      queryClient.invalidateQueries({
        queryKey: RECRUITMENT_QUERY_KEYS.CANDIDATE(id),
      });
      queryClient.invalidateQueries({
        queryKey: [RECRUITMENT_QUERY_KEYS.RECRUITMENT_STATS],
      });
    },
  });
};

/**
 * Hook để xóa ứng viên
 */
export const useDeleteCandidate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => CandidateService.deleteCandidate(id),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [RECRUITMENT_QUERY_KEYS.CANDIDATES],
      });
      queryClient.invalidateQueries({
        queryKey: [RECRUITMENT_QUERY_KEYS.RECRUITMENT_STATS],
      });
    },
  });
};

/**
 * Hook để cập nhật trạng thái ứng viên
 */
export const useUpdateCandidateStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, status }: { id: string; status: string }) =>
      CandidateService.updateCandidateStatus(id, status),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({
        queryKey: [RECRUITMENT_QUERY_KEYS.CANDIDATES],
      });
      queryClient.invalidateQueries({
        queryKey: RECRUITMENT_QUERY_KEYS.CANDIDATE(id),
      });
      queryClient.invalidateQueries({
        queryKey: [RECRUITMENT_QUERY_KEYS.RECRUITMENT_STATS],
      });
    },
  });
};

/**
 * Hook để lấy danh sách quy trình tuyển dụng
 */
export const useRecruitmentProcesses = () => {
  return useQuery({
    queryKey: [RECRUITMENT_QUERY_KEYS.RECRUITMENT_PROCESSES],
    queryFn: () => RecruitmentProcessService.getRecruitmentProcesses(),
    select: data => data.result,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

/**
 * Hook để lấy thống kê tuyển dụng
 */
export const useRecruitmentStats = () => {
  return useQuery({
    queryKey: [RECRUITMENT_QUERY_KEYS.RECRUITMENT_STATS],
    queryFn: () => RecruitmentStatsService.getRecruitmentStats(),
    select: data => data.result,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};
