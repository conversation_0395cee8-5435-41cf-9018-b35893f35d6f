import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsInt } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@/common/dto/query.dto';
import { EmployeeStatus } from '../enum/employee-status.enum';
import { EmploymentType } from '../enum/employment-type.enum';

/**
 * DTO for querying employees
 */
export class EmployeeQueryDto extends QueryDto {
  /**
   * Filter by department ID
   * @example 1
   */
  @ApiProperty({ required: false, description: 'Filter by department ID', example: 1 })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  departmentId?: number;

  /**
   * Filter by manager ID
   * @example 2
   */
  @ApiProperty({ required: false, description: 'Filter by manager ID', example: 2 })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  managerId?: number;

  /**
   * Filter by employee status
   * @example "active"
   */
  @ApiProperty({ 
    required: false, 
    description: 'Filter by employee status', 
    enum: EmployeeStatus,
    example: EmployeeStatus.ACTIVE 
  })
  @IsOptional()
  @IsEnum(EmployeeStatus)
  status?: EmployeeStatus;

  /**
   * Filter by employment type
   * @example "full_time"
   */
  @ApiProperty({ 
    required: false, 
    description: 'Filter by employment type', 
    enum: EmploymentType,
    example: EmploymentType.FULL_TIME 
  })
  @IsOptional()
  @IsEnum(EmploymentType)
  employmentType?: EmploymentType;
}
