import React from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';

import {
  Card,
  Button,
  Typography,
  Icon,
} from '@/shared/components/common';


/**
 * Demo page for Product pages
 */
const ProductPagesDemo: React.FC = () => {
  const { t } = useTranslation();

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-8">
        <h1 className="text-xl sm:text-2xl font-bold text-foreground mb-2">
          Product Pages
        </h1>
        <p className="text-muted">
          Demo of pages for the Product module
        </p>
      </div>

      <div className="space-y-8">
        <Card className="p-6">
          <Typography variant="h5" className="mb-4">
            Available Product Pages
          </Typography>
          <div className="space-y-4">
            <div className="p-4 border rounded-md hover:bg-background-hover transition-colors">
              <div className="flex justify-between items-center">
                <div>
                  <Typography variant="subtitle1" className="mb-1">
                    Product List Page
                  </Typography>
                  <Typography variant="body2" className="text-muted">
                    Page for displaying and managing products
                  </Typography>
                </div>
                <Link to="/product">
                  <Button variant="primary" leftIcon={<Icon name="external-link" size="sm" />}>
                    View Page
                  </Button>
                </Link>
              </div>
            </div>

            <div className="p-4 border rounded-md hover:bg-background-hover transition-colors">
              <div className="flex justify-between items-center">
                <div>
                  <Typography variant="subtitle1" className="mb-1">
                    Product Detail Page
                  </Typography>
                  <Typography variant="body2" className="text-muted">
                    Page for viewing and editing product details
                  </Typography>
                </div>
                <Link to="/product/1">
                  <Button variant="primary" leftIcon={<Icon name="external-link" size="sm" />}>
                    View Page
                  </Button>
                </Link>
              </div>
            </div>

            <div className="p-4 border rounded-md hover:bg-background-hover transition-colors">
              <div className="flex justify-between items-center">
                <div>
                  <Typography variant="subtitle1" className="mb-1">
                    Segment List Page
                  </Typography>
                  <Typography variant="body2" className="text-muted">
                    Page for managing product segments
                  </Typography>
                </div>
                <Link to="/product/segment">
                  <Button variant="primary" leftIcon={<Icon name="external-link" size="sm" />}>
                    View Page
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <Typography variant="h5" className="mb-4">
            Implementation Details
          </Typography>
          <div className="space-y-4">
            <div>
              <Typography variant="subtitle1" className="mb-1">
                Features
              </Typography>
              <ul className="list-disc pl-5 space-y-1">
                <li>Responsive design for all screen sizes</li>
                <li>Multilingual support (Vietnamese, English, Chinese)</li>
                <li>Theme compatibility (light/dark mode)</li>
                <li>Form validation with Zod</li>
                <li>Reusable components</li>
              </ul>
            </div>

            <div>
              <Typography variant="subtitle1" className="mb-1">
                Technologies Used
              </Typography>
              <ul className="list-disc pl-5 space-y-1">
                <li>React with TypeScript</li>
                <li>React Hook Form with Zod validation</li>
                <li>i18next for internationalization</li>
                <li>Tailwind CSS for styling</li>
                <li>React Router for navigation</li>
              </ul>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default ProductPagesDemo;
