import { Card } from '@/shared/components/common';
import { cn } from '@/shared/utils';
import React from 'react';
import Skeleton from '../../common/Skeleton';
import type { OverviewCardProps } from './OverviewCard.types';

/**
 * Component hiển thị thẻ thống kê tổng quan
 */
const OverviewCard: React.FC<OverviewCardProps> = ({
  title,
  value,
  description,
  icon: Icon,
  color = 'blue',
  isLoading = false,
  className,
  onClick,
  hoverable = false,
  customValue,
}) => {
  // Định nghĩa màu sắc cho từng loại
  const colorClasses = {
    blue: 'text-blue-600',
    green: 'text-green-600',
    orange: 'text-orange-600',
    purple: 'text-purple-600',
    red: 'text-red-600',
    gray: 'text-gray-600',
  };

  const iconColorClass = colorClasses[color];
  const valueColorClass = colorClasses[color];

  return (
    <Card
      className={cn(
        'p-4',
        hoverable && 'hover:shadow-md transition-shadow cursor-pointer',
        className
      )}
      onClick={onClick}
      hoverable={hoverable}
    >
      <div className="flex items-center justify-between mb-2">
        <span className="text-sm font-medium text-muted-foreground">{title}</span>
        {Icon && <Icon className={cn('h-4 w-4', iconColorClass)} />}
      </div>

      <div className={cn('text-2xl font-bold', valueColorClass)}>
        {isLoading ? <Skeleton className="h-8 w-16" /> : customValue ? customValue : value}
      </div>

      {description && <p className="text-xs text-muted-foreground mt-1">{description}</p>}
    </Card>
  );
};

export default OverviewCard;
