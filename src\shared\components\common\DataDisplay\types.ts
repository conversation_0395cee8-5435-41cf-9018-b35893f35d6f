import { ReactNode } from 'react';

import { TableColumn } from '../Table/types';

// Định nghĩa kiểu hiển thị
export type ViewMode = 'table' | 'card';

// Props cho từng item trong chế độ card
export interface ItemProps<T> {
  item: T;
  index: number;
}

// Props cho component DataDisplay
export interface DataDisplayProps<T> {
  // Dữ liệu hiển thị
  data: T[];
  // Cột cho chế độ hiển thị dạng bảng
  columns?: TableColumn<T>[];
  // Hàm render tùy chỉnh cho item trong chế độ card
  renderItem?: (props: ItemProps<T>) => ReactNode;
  // ID field dùng làm key
  keyField?: string;
  // Chế độ hiển thị mặc định
  defaultViewMode?: ViewMode;
  // Callback khi chuyển đổi chế độ hiển thị
  onViewModeChange?: (mode: ViewMode) => void;
  // Icon cho mỗi item (nếu có)
  itemIcon?: ReactNode | ((item: T) => ReactNode);
  // Callback khi người dùng click vào item
  onItemClick?: (item: T) => void;
  // Callback khi scroll đến cuối để tải thêm dữ liệu
  onLoadMore?: () => void;
  // Trạng thái đang tải
  loading?: boolean;
  // Có thêm dữ liệu để tải hay không
  hasMore?: boolean;
  // Chiều cao tối đa của vùng hiển thị
  maxHeight?: number | string;
  // Số cột hiển thị trong chế độ card
  gridColumns?: 1 | 2 | 3 | 4 | 5 | 6;
  // Khoảng cách giữa các card
  gridGap?: 1 | 2 | 3 | 4 | 5 | 6 | 8;
  // Các thuộc tính CSS tùy chỉnh
  className?: string;
  // Props bổ sung cho bảng
  tableProps?: Record<string, unknown>;
}
