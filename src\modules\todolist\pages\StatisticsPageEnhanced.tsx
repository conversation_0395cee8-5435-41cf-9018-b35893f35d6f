import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { useAuth } from '@/modules/auth';
import { StatisticCard } from '@/shared/components/cards';
import { PieChart } from '@/shared/components/charts';
import {
  Button,
  Card,
  Icon,
  RangePicker,
  ResponsiveGrid,
  Select,
  Tabs,
  Typography,
} from '@/shared/components/common';

import CompletionRateChart from '../components/charts/CompletionRateChart';
import TaskStatusChart from '../components/charts/TaskStatusChart';
import { useProjects } from '../hooks/useProjects';
import { useProjectGantt, useProjectPerformance, useUserPerformance } from '../hooks/useStatistics';
import { TaskStatus } from '../types/task.types';

/**
 * Trang thống kê nâng cao với biểu đồ
 */
const StatisticsPageEnhanced: React.FC = () => {
  const { t } = useTranslation(['common', 'todolist']);
  const { user } = useAuth();
  const [selectedProjectId, setSelectedProjectId] = useState<number | null>(null);
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null]);
  const [activeTab, setActiveTab] = useState('overview');

  // Lấy danh sách dự án
  const { data: projectsData, isLoading: isLoadingProjects } = useProjects();

  // Chuyển đổi dateRange thành timestamp
  const startDate = dateRange[0] ? dateRange[0].getTime() : undefined;
  const endDate = dateRange[1] ? dateRange[1].getTime() : undefined;

  // Lấy hiệu suất dự án nếu đã chọn dự án
  const { data: projectPerformance, isLoading: isLoadingProjectPerformance } =
    useProjectPerformance(selectedProjectId || 0, startDate, endDate);

  // Lấy hiệu suất người dùng hiện tại
  const { data: userPerformance, isLoading: isLoadingUserPerformance } = useUserPerformance(
    typeof user?.id === 'string' ? parseInt(user.id, 10) : user?.id || 0,
    startDate,
    endDate,
    selectedProjectId || undefined
  );

  // Lấy dữ liệu Gantt của dự án
  const { data: projectGantt, isLoading: isLoadingProjectGantt } = useProjectGantt(
    selectedProjectId || 0
  );

  // Xử lý thay đổi dự án
  const handleProjectChange = (value: string | number | string[] | number[]) => {
    if (typeof value === 'string') {
      setSelectedProjectId(value ? parseInt(value, 10) : null);
    } else if (typeof value === 'number') {
      setSelectedProjectId(value);
    }
  };

  // Xử lý thay đổi khoảng thời gian
  const handleDateRangeChange = (range: [Date | null, Date | null]) => {
    setDateRange(range);
  };

  // Chuẩn bị dữ liệu cho biểu đồ trạng thái công việc
  const taskStatusData = useMemo(() => {
    if (!userPerformance) {return [];}

    return [
      {
        name: t('todolist:task.status.todo', 'To Do'),
        value: userPerformance.pendingTasks,
      },
      {
        name: t('todolist:task.status.inProgress', 'In Progress'),
        value: userPerformance.inProgressTasks,
      },
      {
        name: t('todolist:task.status.done', 'Done'),
        value: userPerformance.completedTasks,
      },
    ];
  }, [userPerformance, t]);

  // Chuẩn bị dữ liệu cho biểu đồ hiệu suất thành viên
  const memberPerformanceData = useMemo(() => {
    if (!projectPerformance?.members) {return [];}

    return projectPerformance.members.map(member => ({
      name: member.name || `User ${member.userId}`,
      completedTasks: member.completedTasks,
      totalTasks: member.totalTasks,
      completionRate: member.completionRate,
    }));
  }, [projectPerformance]);

  // Chuẩn bị dữ liệu cho biểu đồ tiến độ dự án theo thời gian
  const projectProgressData = useMemo(() => {
    if (!projectGantt?.tasks) {return [];}

    // Nhóm các task theo ngày
    const tasksByDate = projectGantt.tasks.reduce(
      (acc, task) => {
        const date = new Date(task.start).toISOString().split('T')[0];
        if (!acc[date]) {
          acc[date] = { date, completed: 0, inProgress: 0, pending: 0, total: 0 };
        }

        acc[date].total += 1;
        if (task.progress === 100) {
          acc[date].completed += 1;
        } else if (task.progress > 0) {
          acc[date].inProgress += 1;
        } else {
          acc[date].pending += 1;
        }

        return acc;
      },
      {} as Record<
        string,
        { date: string; completed: number; inProgress: number; pending: number; total: number }
      >
    );

    // Chuyển đổi thành mảng và sắp xếp theo ngày
    return Object.values(tasksByDate).sort((a, b) => a.date.localeCompare(b.date));
  }, [projectGantt]);

  // Cấu hình tabs
  const tabs = [
    {
      key: 'overview',
      label: (
        <div className="flex items-center">
          <Icon name="chart-pie" size="sm" className="mr-2" />
          {t('todolist:statistics.tabs.overview', 'Tổng quan')}
        </div>
      ),
    },
    {
      key: 'personal',
      label: (
        <div className="flex items-center">
          <Icon name="user" size="sm" className="mr-2" />
          {t('todolist:statistics.tabs.personal', 'Cá nhân')}
        </div>
      ),
    },
    {
      key: 'project',
      label: (
        <div className="flex items-center">
          <Icon name="briefcase" size="sm" className="mr-2" />
          {t('todolist:statistics.tabs.project', 'Dự án')}
        </div>
      ),
      disabled: !selectedProjectId,
    },
    {
      key: 'team',
      label: (
        <div className="flex items-center">
          <Icon name="users" size="sm" className="mr-2" />
          {t('todolist:statistics.tabs.team', 'Nhóm')}
        </div>
      ),
      disabled: !selectedProjectId,
    },
  ];

  return (
    <div>
      <div className="mb-6 flex justify-between items-center">
        <div>
          <Typography variant="h3" className="mb-2">
            {t('todolist:statistics.title', 'Thống kê & Báo cáo')}
          </Typography>
          <Typography variant="body1" color="muted">
            {t('todolist:statistics.description', 'Xem báo cáo và thống kê công việc')}
          </Typography>
        </div>
        <a
          href="/todolist/employee-gantt"
          className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors flex items-center"
        >
          <span className="mr-2">
            {t('todolist:statistics.viewEmployeeGantt', 'Xem biểu đồ Gantt nhân viên')}
          </span>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fillRule="evenodd"
              d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
              clipRule="evenodd"
            />
          </svg>
        </a>
      </div>

      <Card className="mb-6">
        <div className="p-4">
          <div className="flex flex-wrap gap-4">
            <div className="w-full md:w-64">
              <Typography variant="body2" className="mb-1">
                {t('todolist:statistics.filters.project', 'Dự án')}
              </Typography>
              <Select
                placeholder={t('todolist:statistics.filters.selectProject', 'Chọn dự án')}
                onChange={handleProjectChange}
                value={selectedProjectId?.toString() || ''}
                options={
                  projectsData?.items.map(project => ({
                    value: project.id.toString(),
                    label: project.title,
                  })) || []
                }
                loading={isLoadingProjects}
                fullWidth
              />
            </div>
            <div className="w-full md:w-auto flex-grow">
              <Typography variant="body2" className="mb-1">
                {t('todolist:statistics.filters.dateRange', 'Khoảng thời gian')}
              </Typography>
              <RangePicker
                value={dateRange}
                onChange={handleDateRangeChange}
                placeholder={[
                  t('todolist:statistics.filters.startDate', 'Ngày bắt đầu'),
                  t('todolist:statistics.filters.endDate', 'Ngày kết thúc'),
                ]}
                fullWidth
              />
            </div>
            <div className="w-full md:w-auto flex items-end">
              <Button
                variant="outline"
                leftIcon={<Icon name="refresh" size="sm" />}
                onClick={() => {
                  setDateRange([null, null]);
                  setSelectedProjectId(null);
                }}
              >
                {t('common:reset', 'Đặt lại')}
              </Button>
            </div>
          </div>
        </div>
      </Card>

      <Tabs
        items={tabs}
        activeKey={activeTab}
        onChange={key => setActiveTab(key)}
        className="mb-6"
      />

      {activeTab === 'overview' && (
        <div className="space-y-6">
          <ResponsiveGrid gap={6} maxColumns={{ xs: 1, sm: 2, md: 2, lg: 4, xl: 4 }}>
            <StatisticCard
              title={t('todolist:statistics.metrics.totalTasks', 'Tổng số công việc')}
              value={userPerformance?.totalTasks || 0}
              icon="clipboard-list"
              iconColor="primary"
            />
            <StatisticCard
              title={t('todolist:statistics.metrics.completedTasks', 'Đã hoàn thành')}
              value={userPerformance?.completedTasks || 0}
              icon="check-circle"
              iconColor="success"
              change={userPerformance?.completionRate || 0}
              trend="up"
            />
            <StatisticCard
              title={t('todolist:statistics.metrics.inProgressTasks', 'Đang thực hiện')}
              value={userPerformance?.inProgressTasks || 0}
              icon="clock"
              iconColor="warning"
            />
            <StatisticCard
              title={t('todolist:statistics.metrics.pendingTasks', 'Chưa bắt đầu')}
              value={userPerformance?.pendingTasks || 0}
              icon="hourglass"
              iconColor="danger"
            />
          </ResponsiveGrid>

          <ResponsiveGrid gap={6} maxColumns={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 2 }}>
            <Card className="h-80">
              <div className="p-4">
                <Typography variant="h6" className="mb-4">
                  {t('todolist:statistics.charts.taskStatus', 'Trạng thái công việc')}
                </Typography>
                <div className="h-64">
                  {isLoadingUserPerformance ? (
                    <div className="h-full flex items-center justify-center">
                      <Typography variant="body2" color="muted">
                        {t('common:loading', 'Đang tải...')}
                      </Typography>
                    </div>
                  ) : userPerformance ? (
                    <TaskStatusChart
                      tasks={
                        [
                          ...Array(userPerformance.pendingTasks).fill({
                            status: TaskStatus.PENDING,
                          }),
                          ...Array(userPerformance.inProgressTasks).fill({
                            status: TaskStatus.IN_PROGRESS,
                          }),
                          ...Array(userPerformance.completedTasks).fill({
                            status: TaskStatus.COMPLETED,
                          }),
                        ] as any
                      }
                    />
                  ) : (
                    <div className="h-full flex items-center justify-center">
                      <Typography variant="body2" color="muted">
                        {t('todolist:statistics.noData', 'Không có dữ liệu')}
                      </Typography>
                    </div>
                  )}
                </div>
              </div>
            </Card>

            <Card className="h-80">
              <div className="p-4">
                <Typography variant="h6" className="mb-4">
                  {t('todolist:statistics.charts.completionRate', 'Tỷ lệ hoàn thành')}
                </Typography>
                <div className="h-64">
                  {isLoadingUserPerformance ? (
                    <div className="h-full flex items-center justify-center">
                      <Typography variant="body2" color="muted">
                        {t('common:loading', 'Đang tải...')}
                      </Typography>
                    </div>
                  ) : userPerformance ? (
                    <CompletionRateChart rate={userPerformance.completionRate} />
                  ) : (
                    <div className="h-full flex items-center justify-center">
                      <Typography variant="body2" color="muted">
                        {t('todolist:statistics.noData', 'Không có dữ liệu')}
                      </Typography>
                    </div>
                  )}
                </div>
              </div>
            </Card>
          </ResponsiveGrid>
        </div>
      )}

      {activeTab === 'personal' && (
        <div className="space-y-6">
          <Card>
            <div className="p-4">
              <Typography variant="h6" className="mb-4">
                {t('todolist:statistics.personalPerformance', 'Hiệu suất cá nhân')}
              </Typography>
              {isLoadingUserPerformance ? (
                <div className="h-40 flex items-center justify-center">
                  <Typography variant="body2" color="muted">
                    {t('common:loading', 'Đang tải...')}
                  </Typography>
                </div>
              ) : userPerformance ? (
                <div className="space-y-6">
                  <ResponsiveGrid gap={4} maxColumns={{ xs: 1, sm: 2, md: 4, lg: 4, xl: 4 }}>
                    <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                      <Typography variant="body2" color="muted">
                        {t('todolist:statistics.metrics.totalTasks', 'Tổng số công việc')}
                      </Typography>
                      <Typography variant="h3">{userPerformance.totalTasks}</Typography>
                    </div>
                    <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                      <Typography variant="body2" color="muted">
                        {t('todolist:statistics.metrics.completedTasks', 'Đã hoàn thành')}
                      </Typography>
                      <Typography variant="h3">{userPerformance.completedTasks}</Typography>
                    </div>
                    <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
                      <Typography variant="body2" color="muted">
                        {t('todolist:statistics.metrics.averageScore', 'Điểm trung bình')}
                      </Typography>
                      <Typography variant="h3">
                        {userPerformance.averageScore.toFixed(1)}
                      </Typography>
                    </div>
                    <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
                      <Typography variant="body2" color="muted">
                        {t('todolist:statistics.metrics.completionRate', 'Tỷ lệ hoàn thành')}
                      </Typography>
                      <Typography variant="h3">
                        {userPerformance.completionRate.toFixed(1)}%
                      </Typography>
                    </div>
                  </ResponsiveGrid>

                  <div className="h-80">
                    <Typography variant="subtitle1" className="mb-4">
                      {t('todolist:statistics.charts.taskDistribution', 'Phân bố công việc')}
                    </Typography>
                    <PieChart
                      data={[
                        {
                          name: t('todolist:task.status.todo', 'To Do'),
                          value: userPerformance.pendingTasks,
                        },
                        {
                          name: t('todolist:task.status.inProgress', 'In Progress'),
                          value: userPerformance.inProgressTasks,
                        },
                        {
                          name: t('todolist:task.status.done', 'Done'),
                          value: userPerformance.completedTasks,
                        },
                      ]}
                      slices={[{ nameKey: 'name', valueKey: 'value' }]}
                      height={300}
                      showTooltip
                      showLegend
                      colorScheme={['#94a3b8', '#3b82f6', '#22c55e']}
                    />
                  </div>
                </div>
              ) : (
                <div className="h-40 flex items-center justify-center">
                  <Typography variant="body2" color="muted">
                    {t('todolist:statistics.noData', 'Không có dữ liệu')}
                  </Typography>
                </div>
              )}
            </div>
          </Card>
        </div>
      )}

      {activeTab === 'project' && selectedProjectId && (
        <div className="space-y-6">
          <Card>
            <div className="p-4">
              <Typography variant="h6" className="mb-4">
                {t('todolist:statistics.projectPerformance', 'Hiệu suất dự án')}
              </Typography>
              {isLoadingProjectPerformance ? (
                <div className="h-40 flex items-center justify-center">
                  <Typography variant="body2" color="muted">
                    {t('common:loading', 'Đang tải...')}
                  </Typography>
                </div>
              ) : projectPerformance ? (
                <div className="space-y-6">
                  <ResponsiveGrid gap={4} maxColumns={{ xs: 1, sm: 2, md: 4, lg: 4, xl: 4 }}>
                    <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                      <Typography variant="body2" color="muted">
                        {t('todolist:statistics.metrics.totalTasks', 'Tổng số công việc')}
                      </Typography>
                      <Typography variant="h3">{projectPerformance.totalTasks}</Typography>
                    </div>
                    <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                      <Typography variant="body2" color="muted">
                        {t('todolist:statistics.metrics.completedTasks', 'Đã hoàn thành')}
                      </Typography>
                      <Typography variant="h3">{projectPerformance.completedTasks}</Typography>
                    </div>
                    <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
                      <Typography variant="body2" color="muted">
                        {t('todolist:statistics.metrics.averageScore', 'Điểm trung bình')}
                      </Typography>
                      <Typography variant="h3">
                        {projectPerformance.averageScore.toFixed(1)}
                      </Typography>
                    </div>
                    <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
                      <Typography variant="body2" color="muted">
                        {t('todolist:statistics.metrics.completionRate', 'Tỷ lệ hoàn thành')}
                      </Typography>
                      <Typography variant="h3">
                        {projectPerformance.completionRate.toFixed(1)}%
                      </Typography>
                    </div>
                  </ResponsiveGrid>

                  <div className="h-80">
                    <Typography variant="subtitle1" className="mb-4">
                      {t('todolist:statistics.charts.taskDistribution', 'Phân bố công việc')}
                    </Typography>
                    <PieChart
                      data={[
                        {
                          name: t('todolist:task.status.todo', 'To Do'),
                          value: projectPerformance.pendingTasks,
                        },
                        {
                          name: t('todolist:task.status.inProgress', 'In Progress'),
                          value: projectPerformance.inProgressTasks,
                        },
                        {
                          name: t('todolist:task.status.done', 'Done'),
                          value: projectPerformance.completedTasks,
                        },
                      ]}
                      slices={[{ nameKey: 'name', valueKey: 'value' }]}
                      height={300}
                      showTooltip
                      showLegend
                      colorScheme={['#94a3b8', '#3b82f6', '#22c55e']}
                    />
                  </div>
                </div>
              ) : (
                <div className="h-40 flex items-center justify-center">
                  <Typography variant="body2" color="muted">
                    {t('todolist:statistics.noData', 'Không có dữ liệu')}
                  </Typography>
                </div>
              )}
            </div>
          </Card>

          <Card>
            <div className="p-4">
              <Typography variant="h6" className="mb-4">
                {t('todolist:statistics.charts.projectProgress', 'Tiến độ dự án theo thời gian')}
              </Typography>
              {isLoadingProjectGantt ? (
                <div className="h-80 flex items-center justify-center">
                  <Typography variant="body2" color="muted">
                    {t('common:loading', 'Đang tải...')}
                  </Typography>
                </div>
              ) : projectProgressData.length > 0 ? (
                <div className="h-80">
                  <AreaChart
                    data={projectProgressData}
                    xAxisKey="date"
                    areas={[
                      {
                        dataKey: 'completed',
                        name: t('todolist:task.status.done', 'Hoàn thành'),
                        color: '#22c55e',
                      },
                      {
                        dataKey: 'inProgress',
                        name: t('todolist:task.status.inProgress', 'Đang thực hiện'),
                        color: '#3b82f6',
                      },
                      {
                        dataKey: 'pending',
                        name: t('todolist:task.status.todo', 'Chưa bắt đầu'),
                        color: '#94a3b8',
                      },
                    ]}
                    height={300}
                    showGrid
                    showTooltip
                    showLegend
                    stacked
                    xAxisFormatter={value => formatDate(value)}
                    xAxisLabel={t('todolist:statistics.charts.date', 'Ngày')}
                    yAxisLabel={t('todolist:statistics.charts.tasks', 'Số công việc')}
                  />
                </div>
              ) : (
                <div className="h-80 flex items-center justify-center">
                  <Typography variant="body2" color="muted">
                    {t('todolist:statistics.noData', 'Không có dữ liệu')}
                  </Typography>
                </div>
              )}
            </div>
          </Card>
        </div>
      )}

      {activeTab === 'team' && selectedProjectId && projectPerformance && (
        <div className="space-y-6">
          <Card>
            <div className="p-4">
              <Typography variant="h6" className="mb-4">
                {t('todolist:statistics.teamPerformance', 'Hiệu suất nhóm')}
              </Typography>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-800">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        {t('todolist:statistics.team.member', 'Thành viên')}
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        {t('todolist:statistics.team.completedTasks', 'Công việc hoàn thành')}
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        {t('todolist:statistics.team.totalTasks', 'Tổng công việc')}
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        {t('todolist:statistics.team.completionRate', 'Tỷ lệ hoàn thành')}
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        {t('todolist:statistics.team.averageScore', 'Điểm trung bình')}
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                    {projectPerformance.members.map(member => (
                      <tr key={member.userId}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                {member.name || `User ${member.userId}`}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          {member.completedTasks}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          {member.totalTasks}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          {member.completionRate.toFixed(1)}%
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          {member.averageScore.toFixed(1)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </Card>

          <Card>
            <div className="p-4">
              <Typography variant="h6" className="mb-4">
                {t('todolist:statistics.charts.memberComparison', 'So sánh hiệu suất thành viên')}
              </Typography>
              <div className="h-80">
                <BarChart
                  data={memberPerformanceData}
                  xAxisKey="name"
                  bars={[
                    {
                      dataKey: 'completedTasks',
                      name: t('todolist:statistics.team.completedTasks', 'Công việc hoàn thành'),
                      color: '#22c55e',
                    },
                    {
                      dataKey: 'totalTasks',
                      name: t('todolist:statistics.team.totalTasks', 'Tổng công việc'),
                      color: '#3b82f6',
                    },
                  ]}
                  height={300}
                  showGrid
                  showTooltip
                  showLegend
                  xAxisLabel={t('todolist:statistics.team.member', 'Thành viên')}
                  yAxisLabel={t('todolist:statistics.team.tasks', 'Số công việc')}
                />
              </div>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
};

export default StatisticsPageEnhanced;
