import { SetMetadata } from '@nestjs/common';
import { Permission } from '../enum/permission.enum';

export const PERMISSIONS_KEY = 'permissions';
/**
 * Decorator để đánh dấu các permissions cần thiết cho một endpoint
 * sử dụng enum Permission đã được định nghĩa sẵn
 *
 * @param permissions Danh sách các Permission cần thiết
 * @returns Decorator
 *
 * @example
 * // Sử dụng với một Permission
 * @RequirePermissionEnum(Permission.USER_VIEW_LIST)
 *
 * // Sử dụng với nhiều Permission
 * @RequirePermissionEnum(
 *   Permission.USER_VIEW_LIST,
 *   Permission.USER_CREATE
 * )
 */
export const RequirePermissionEnum = (...permissions: Permission[]) => {
  // Sử dụng trực tiếp các giá trị từ enum Permission
  // Các giá trị này đã được định dạng sẵn theo "module:action"
  return SetMetadata(PERMISSIONS_KEY, permissions);
};
