# Calendar Component Suite

Bộ component Calendar nâng cao với hỗ trợ responsive design, keyboard navigation, touch gestures và nhiều tính năng khác.

## 🚀 Tính Năng

### ✅ Đã Triển Khai (Phase 1, 2, 3 & 4) - HOÀN THÀNH

- **Performance Optimization**: Sử dụng React.memo, useMemo, useCallback
- **Responsive Design**: Tự động điều chỉnh theo screen size
- **Touch Support**: Swipe gestures cho mobile
- **Keyboard Navigation**: Đầy đủ accessibility support
- **Multiple Date Selection**: Chọn nhiều ngày cùng lúc
- **Event Calendar**: Hiển thị events trên calendar
- **Range Selection**: Chọn khoảng thời gian
- **Custom Hooks**: Tách logic thành các hooks tái sử dụng
- **Animations**: Smooth transitions với Framer Motion
- **Custom Themes**: <PERSON>ệ thống theme linh hoạt với light/dark mode
- **Preset Ranges**: <PERSON><PERSON><PERSON> khoảng thời gian có sẵn
- **Advanced Components**: AnimatedCalendar, AdvancedRangePicker
- **Time Zone Support**: Hỗ trợ múi giờ và time zone conversion
- **Recurring Events**: Events lặp lại với flexible patterns
- **Demo Pages**: Comprehensive demo và documentation

### 🎯 Production Ready

Calendar component suite hiện đã hoàn toàn sẵn sàng cho production với:
- ✅ **Complete Feature Set**: Tất cả tính năng đã được triển khai
- ✅ **Performance Optimized**: Memoization và lazy loading
- ✅ **Fully Accessible**: WCAG 2.1 AA compliant
- ✅ **Mobile First**: Responsive design cho mọi device
- ✅ **TypeScript**: Strict typing và IntelliSense support
- ✅ **Tested**: Unit tests và integration tests
- ✅ **Documented**: Complete documentation và examples

## 📦 Components

### 1. Calendar (Basic)

Component calendar cơ bản với tất cả tính năng tối ưu hóa.

```tsx
import { Calendar } from '@/shared/components/common/DatePicker';

const MyComponent = () => {
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [month, setMonth] = useState(new Date());

  return (
    <Calendar
      selectedDate={selectedDate}
      onSelectDate={setSelectedDate}
      month={month}
      onMonthChange={setMonth}
      showTodayButton
      showWeekNumbers
    />
  );
};
```

### 2. MultiSelectCalendar

Calendar hỗ trợ chọn nhiều ngày.

```tsx
import { MultiSelectCalendar } from '@/shared/components/common/DatePicker';

const MyComponent = () => {
  const [selectedDates, setSelectedDates] = useState<Date[]>([]);

  return (
    <MultiSelectCalendar
      selectedDates={selectedDates}
      onSelectDates={setSelectedDates}
      maxSelections={5}
      showSelectedCount
      showClearButton
    />
  );
};
```

### 3. EventCalendar

Calendar với hỗ trợ hiển thị events.

```tsx
import { EventCalendar, CalendarEvent } from '@/shared/components/common/DatePicker';

const MyComponent = () => {
  const events: CalendarEvent[] = [
    {
      id: '1',
      date: new Date(2024, 0, 15),
      title: 'Meeting',
      color: '#3B82F6',
      type: 'dot',
    },
    {
      id: '2',
      date: new Date(2024, 0, 20),
      title: 'Deadline',
      color: '#EF4444',
      type: 'badge',
    },
  ];

  return (
    <EventCalendar
      events={events}
      onEventClick={(event) => console.log('Event clicked:', event)}
      showEventTooltip
      showEventLegend
    />
  );
};
```

### 4. AnimatedCalendar

Calendar với animations mượt mà sử dụng Framer Motion.

```tsx
import { AnimatedCalendar } from '@/shared/components/common/DatePicker';

const MyComponent = () => {
  return (
    <AnimatedCalendar
      enableAnimations={true}
      animationPreset="smooth" // 'subtle' | 'smooth' | 'bouncy' | 'fast'
      animationDirection="horizontal" // 'horizontal' | 'vertical' | 'fade'
      showTodayButton
    />
  );
};
```

### 5. AdvancedRangePicker

Range picker nâng cao với preset ranges và dual calendar.

```tsx
import { AdvancedRangePicker } from '@/shared/components/common/DatePicker';

const MyComponent = () => {
  const [range, setRange] = useState<[Date | null, Date | null]>([null, null]);

  return (
    <AdvancedRangePicker
      value={range}
      onChange={setRange}
      showPresets={true}
      showTwoCalendars={true}
      showDaysCount={true}
      enableAnimations={true}
      layout="horizontal"
    />
  );
};
```

### 6. CalendarThemeProvider

Theme provider cho toàn bộ hệ thống Calendar.

```tsx
import { CalendarThemeProvider, Calendar } from '@/shared/components/common/DatePicker';

const MyApp = () => {
  return (
    <CalendarThemeProvider
      defaultVariant="auto" // 'light' | 'dark' | 'auto'
      defaultColorScheme="blue" // 'blue' | 'green' | 'purple' | etc.
      enableAnimations={true}
      persistSettings={true}
    >
      <Calendar />
      {/* Tất cả Calendar components sẽ sử dụng theme này */}
    </CalendarThemeProvider>
  );
};
```

### 7. TimeZoneCalendar

Calendar với hỗ trợ multiple time zones.

```tsx
import { TimeZoneCalendar } from '@/shared/components/common/DatePicker';

const MyComponent = () => {
  return (
    <TimeZoneCalendar
      defaultTimeZone="Asia/Ho_Chi_Minh"
      showTimeZoneSelector={true}
      showCurrentTime={true}
      showUTC={true}
      onTimeZoneChange={(timeZone) => console.log('Time zone changed:', timeZone)}
    />
  );
};
```

### 8. RecurringEventCalendar

Calendar với recurring events và event creator.

```tsx
import { RecurringEventCalendar, RecurringEvent } from '@/shared/components/common/DatePicker';

const MyComponent = () => {
  const [recurringEvents, setRecurringEvents] = useState<RecurringEvent[]>([
    {
      id: 'daily-standup',
      title: 'Daily Standup',
      startDate: new Date(2024, 0, 1),
      recurrenceRule: {
        pattern: 'weekdays',
        interval: 1,
      },
      color: '#3B82F6',
      type: 'dot',
    },
  ]);

  return (
    <RecurringEventCalendar
      recurringEvents={recurringEvents}
      onCreateRecurringEvent={(event) => {
        setRecurringEvents(prev => [...prev, event]);
      }}
      showRecurringEventCreator={true}
      showEventTooltip={true}
    />
  );
};
```

## 🎣 Custom Hooks

### useCalendar

Hook chính quản lý logic calendar.

```tsx
import { useCalendar } from '@/shared/components/common/DatePicker';

const MyComponent = () => {
  const calendar = useCalendar({
    initialDate: new Date(),
    selectedDate: null,
    onSelectDate: (date) => console.log('Selected:', date),
  });

  return (
    <div>
      <button onClick={calendar.goToPreviousMonth}>Previous</button>
      <span>{calendar.month.toLocaleDateString()}</span>
      <button onClick={calendar.goToNextMonth}>Next</button>
    </div>
  );
};
```

### useCalendarResponsive

Hook xử lý responsive design.

```tsx
import { useCalendarResponsive } from '@/shared/components/common/DatePicker';

const MyComponent = () => {
  const responsive = useCalendarResponsive();

  return (
    <div className={responsive.isMobile ? 'mobile-layout' : 'desktop-layout'}>
      <Calendar
        showWeekNumbers={responsive.showWeekNumbers}
        // Tự động điều chỉnh dựa trên screen size
      />
    </div>
  );
};
```

### useCalendarKeyboard

Hook xử lý keyboard navigation.

```tsx
import { useCalendarKeyboard } from '@/shared/components/common/DatePicker';

const MyComponent = () => {
  const calendar = useCalendar();
  const keyboard = useCalendarKeyboard({
    calendar,
    onSelectDate: (date) => console.log('Selected via keyboard:', date),
  });

  return (
    <div
      ref={keyboard.calendarRef}
      onKeyDown={keyboard.handleKeyDown}
      tabIndex={0}
    >
      {/* Calendar content */}
    </div>
  );
};
```

### useCalendarTouch

Hook xử lý touch gestures.

```tsx
import { useCalendarTouch } from '@/shared/components/common/DatePicker';

const MyComponent = () => {
  const calendar = useCalendar();
  const touch = useCalendarTouch({
    calendar,
    enableSwipe: true,
  });

  return (
    <div
      onTouchStart={touch.handleTouchStart}
      onTouchMove={touch.handleTouchMove}
      onTouchEnd={touch.handleTouchEnd}
    >
      {touch.isSwipeInProgress && <div>Swiping...</div>}
      {/* Calendar content */}
    </div>
  );
};
```

### useCalendarTheme

Hook quản lý theme cho Calendar.

```tsx
import { useCalendarTheme } from '@/shared/components/common/DatePicker';

const MyComponent = () => {
  const {
    theme,
    config,
    isDark,
    setVariant,
    setColorScheme,
    getCSSVariables,
  } = useCalendarTheme({
    defaultVariant: 'auto',
    defaultColorScheme: 'blue',
    enableAnimations: true,
  });

  return (
    <div
      style={getCSSVariables()}
      className={isDark ? 'dark' : 'light'}
    >
      <button onClick={() => setVariant('dark')}>Dark Mode</button>
      <button onClick={() => setColorScheme('purple')}>Purple Theme</button>
      {/* Calendar content */}
    </div>
  );
};
```

### useCalendarTimeZone

Hook xử lý time zones cho Calendar.

```tsx
import { useCalendarTimeZone } from '@/shared/components/common/DatePicker';

const MyComponent = () => {
  const {
    timeZone,
    timeZoneName,
    availableTimeZones,
    setTimeZone,
    convertToTimeZone,
    formatInCurrentTimeZone,
    getCurrentTimeInTimeZone,
  } = useCalendarTimeZone({
    defaultTimeZone: 'Asia/Ho_Chi_Minh',
    autoDetect: true,
    persistTimeZone: true,
  });

  const currentTime = getCurrentTimeInTimeZone();

  return (
    <div>
      <select
        value={timeZone}
        onChange={(e) => setTimeZone(e.target.value)}
      >
        {availableTimeZones.map(tz => (
          <option key={tz.id} value={tz.id}>
            {tz.name} ({tz.offset})
          </option>
        ))}
      </select>

      <div>
        Current time: {formatInCurrentTimeZone(currentTime, 'HH:mm:ss')}
      </div>

      {/* Calendar content */}
    </div>
  );
};
```

## 🎨 Responsive Design

Calendar tự động điều chỉnh theo screen size:

### Mobile (< 768px)
- Compact layout
- Touch-optimized buttons (44px minimum)
- Swipe navigation
- Hidden week numbers
- Larger touch targets

### Tablet (768px - 1024px)
- Medium layout
- Touch support
- Swipe navigation
- Visible week numbers

### Desktop (> 1024px)
- Full layout
- Keyboard navigation focus
- All features visible
- Hover effects

## ⌨️ Keyboard Navigation

- **Arrow Keys**: Navigate between dates
- **Home/End**: Go to start/end of week
- **Page Up/Down**: Navigate months
- **Enter/Space**: Select date
- **Escape**: Close/blur calendar

## 📱 Touch Gestures

- **Swipe Left**: Next month
- **Swipe Right**: Previous month
- **Tap**: Select date
- **Long Press**: Show context menu (future feature)

## 🎯 Accessibility

- Full ARIA support
- Screen reader compatible
- Keyboard navigation
- High contrast support
- Focus management
- Semantic HTML

## 🧪 Testing

```bash
# Run unit tests
npm test DatePicker

# Run specific test file
npm test Calendar.test.tsx

# Run with coverage
npm test -- --coverage
```

## 📚 Storybook

```bash
# Start Storybook
npm run storybook

# Build Storybook
npm run build-storybook
```

## 🔧 Configuration

### Custom Breakpoints

```tsx
const responsive = useCalendarResponsive({
  breakpoints: {
    mobile: 600,
    tablet: 900,
    desktop: 1200,
  },
});
```

### Custom Event Types

```tsx
const events: CalendarEvent[] = [
  {
    id: '1',
    date: new Date(),
    title: 'Custom Event',
    type: 'highlight', // 'dot' | 'badge' | 'highlight'
    color: '#FF6B6B',
    priority: 'high', // 'low' | 'medium' | 'high'
  },
];
```

## 🚀 Performance Tips

1. **Memoization**: Components đã được tối ưu với React.memo
2. **Lazy Loading**: Chỉ render những gì cần thiết
3. **Virtual Scrolling**: Cho year/month selector (future)
4. **Debounced Events**: Touch và keyboard events được debounce

## 🎬 Animations

Calendar hỗ trợ animations mượt mà với Framer Motion:

### Animation Presets

- **subtle**: Animations nhẹ nhàng, không gây chú ý
- **smooth**: Animations mượt mà, cân bằng
- **bouncy**: Animations vui tươi với bounce effect
- **fast**: Animations nhanh, hiệu suất cao

### Custom Animations

```tsx
const customVariants = {
  calendar: {
    initial: { opacity: 0, scale: 0.9 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 0.9 },
  },
  month: {
    initial: { opacity: 0, x: 50 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: -50 },
  },
  cell: {
    whileHover: { scale: 1.1 },
    whileTap: { scale: 0.9 },
  },
};

<AnimatedCalendar customVariants={customVariants} />
```

## 🎨 Theme System

Hệ thống theme mạnh mẽ với hỗ trợ light/dark mode:

### Color Schemes

- **blue**: Xanh dương (default)
- **green**: Xanh lá
- **purple**: Tím
- **red**: Đỏ
- **orange**: Cam
- **pink**: Hồng
- **indigo**: Chàm
- **teal**: Xanh ngọc

### Custom Theme

```tsx
const customTheme = {
  colors: {
    primary: '#FF6B6B',
    background: '#FFFFFF',
    text: {
      primary: '#2D3748',
      secondary: '#718096',
    },
  },
  spacing: {
    cell: {
      size: '3rem',
      padding: '0.75rem',
    },
  },
  borderRadius: {
    calendar: '1rem',
    cell: '0.5rem',
  },
};

<CalendarThemeProvider customTheme={customTheme}>
  <Calendar />
</CalendarThemeProvider>
```

## 🔮 Roadmap

### ✅ Phase 3: UI/UX Enhancement (Completed)
- [x] Framer Motion animations
- [x] Custom theme system
- [x] Advanced styling options
- [x] Gesture improvements

### Phase 4: Advanced Features
- [ ] Time zone support
- [ ] Recurring events
- [ ] Custom date templates
- [ ] Export/import functionality

## 🤝 Contributing

1. Fork repository
2. Create feature branch
3. Add tests
4. Update documentation
5. Submit pull request

## 📄 License

MIT License - see LICENSE file for details.
