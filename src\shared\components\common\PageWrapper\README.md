# PageWrapper Component

Component wrapper chung cho các trang, cung cấp styling chuẩn cho layout.

## Features

- ✅ Full width layout (`w-full`)
- ✅ Minimum height screen (`min-h-screen`) 
- ✅ Theme colors (`bg-background text-foreground`)
- ✅ Configurable padding
- ✅ Configurable full height
- ✅ Custom className support

## Usage

### Sử dụng cơ bản
```tsx
import { PageWrapper } from '@/shared/components/common';

const MyPage = () => {
  return (
    <PageWrapper>
      <div>Nội dung trang của bạn</div>
    </PageWrapper>
  );
};
```

### Tùy chỉnh padding
```tsx
// Padding lớn hơn
<PageWrapper padding="p-8">
  <div>Nội dung với padding lớn hơn</div>
</PageWrapper>

// Không có padding
<PageWrapper padding={false}>
  <div>Nội dung không có padding</div>
</PageWrapper>

// Padding tùy chỉnh
<PageWrapper padding="px-4 py-8">
  <div>Nội dung với padding tùy chỉnh</div>
</PageWrapper>
```

### Tùy chỉnh height
```tsx
// Không sử dụng full height
<PageWrapper fullHeight={false}>
  <div>Nội dung không cần full height</div>
</PageWrapper>
```

### Thêm className tùy chỉnh
```tsx
<PageWrapper className="custom-class">
  <div>Nội dung với class tùy chỉnh</div>
</PageWrapper>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `children` | `React.ReactNode` | - | Nội dung của trang |
| `className` | `string` | `''` | CSS class tùy chỉnh |
| `padding` | `boolean \| string` | `true` | Padding cho trang. `true` = `p-6`, `false` = không padding, `string` = padding tùy chỉnh |
| `fullHeight` | `boolean` | `true` | Có sử dụng `min-h-screen h-full` hay không |

## Default Styling

Khi sử dụng với props mặc định, PageWrapper sẽ render:

```html
<div className="w-full min-h-screen h-full bg-background text-foreground p-6">
  {children}
</div>
```

## Migration từ div thủ công

### Trước khi có PageWrapper:
```tsx
const MyPage = () => {
  return (
    <div className="w-full min-h-screen h-full bg-background text-foreground p-6">
      <div>Nội dung trang</div>
    </div>
  );
};
```

### Sau khi sử dụng PageWrapper:
```tsx
import { PageWrapper } from '@/shared/components/common';

const MyPage = () => {
  return (
    <PageWrapper>
      <div>Nội dung trang</div>
    </PageWrapper>
  );
};
```
