/**
 * Stream Message Component
 * Renders streaming content with real-time updates
 */

import React from 'react';
import { Typography, Button, Icon } from '@/shared/components/common';
import { StreamContentData } from '@/shared/websocket/types/chat-message.types';
import { useMessageStream } from '@/shared/websocket/hooks/useMessageStream';

// Props interface
export interface StreamMessageProps {
  data: StreamContentData;
  messageId: string;
  className?: string;
}

/**
 * Stream Message Component
 */
const StreamMessage: React.FC<StreamMessageProps> = ({ 
  data, 
  messageId, 
  className = '' 
}) => {
  const {
    streamId,
    chunk,
    isComplete,
    totalChunks,
    currentChunk,
    contentType = 'text',
  } = data;

  // Use message stream hook
  const {
    currentContent,
    isStreaming,
    isComplete: streamComplete,
    hasError,
    progress,
    error,
    retryStream,
    cancelStream,
    receivedChunks,
    totalChunks: streamTotalChunks,
  } = useMessageStream({
    messageId,
    onStreamStart: (data) => {
      console.log('Stream started:', data);
    },
    onStreamChunk: (chunk) => {
      console.log('Stream chunk received:', chunk);
    },
    onStreamComplete: (finalContent) => {
      console.log('Stream completed:', finalContent);
    },
    onStreamError: (error) => {
      console.error('Stream error:', error);
    },
  });

  // Render content based on content type
  const renderContent = () => {
    const content = currentContent || chunk;

    switch (contentType) {
      case 'markdown':
        // For markdown, we'd need to import and use a markdown renderer
        return (
          <div className="prose prose-sm dark:prose-invert max-w-none">
            <pre className="whitespace-pre-wrap">{content}</pre>
          </div>
        );

      case 'json':
        try {
          const jsonData = JSON.parse(content);
          return (
            <pre className="bg-gray-100 dark:bg-gray-800 p-3 rounded text-sm overflow-x-auto">
              {JSON.stringify(jsonData, null, 2)}
            </pre>
          );
        } catch {
          return (
            <pre className="bg-gray-100 dark:bg-gray-800 p-3 rounded text-sm overflow-x-auto">
              {content}
            </pre>
          );
        }

      case 'text':
      default:
        return (
          <Typography variant="body2" className="whitespace-pre-wrap">
            {content}
          </Typography>
        );
    }
  };

  // Render progress indicator
  const renderProgress = () => {
    if (streamComplete || (!isStreaming && !hasError)) return null;

    const progressValue = streamTotalChunks ? progress : 0;
    const chunksInfo = streamTotalChunks 
      ? `${receivedChunks}/${streamTotalChunks}` 
      : `${receivedChunks}`;

    return (
      <div className="mt-3 p-2 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <div className="flex items-center justify-between mb-2">
          <Typography variant="caption" className="text-gray-600 dark:text-gray-400">
            {isStreaming ? 'Streaming...' : hasError ? 'Stream Error' : 'Stream Complete'}
          </Typography>
          <Typography variant="caption" className="text-gray-600 dark:text-gray-400">
            {chunksInfo} chunks
          </Typography>
        </div>

        {/* Progress bar */}
        {streamTotalChunks && (
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              className="bg-blue-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progressValue}%` }}
            />
          </div>
        )}

        {/* Streaming indicator */}
        {isStreaming && (
          <div className="flex items-center mt-2">
            <div className="animate-pulse flex space-x-1">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <div className="w-2 h-2 bg-blue-500 rounded-full animation-delay-75"></div>
              <div className="w-2 h-2 bg-blue-500 rounded-full animation-delay-150"></div>
            </div>
            <Typography variant="caption" className="ml-2 text-blue-600 dark:text-blue-400">
              Receiving data...
            </Typography>
          </div>
        )}
      </div>
    );
  };

  // Render error state
  const renderError = () => {
    if (!hasError) return null;

    return (
      <div className="mt-3 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
        <div className="flex items-start justify-between">
          <div className="flex items-start">
            <Icon name="alert-circle" size="sm" className="text-red-500 mt-0.5 mr-2" />
            <div>
              <Typography variant="caption" className="text-red-700 dark:text-red-300 font-medium">
                Stream Error
              </Typography>
              <Typography variant="caption" className="text-red-600 dark:text-red-400 block mt-1">
                {error}
              </Typography>
            </div>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={retryStream}
            className="ml-2"
          >
            <Icon name="refresh-cw" size="xs" className="mr-1" />
            Retry
          </Button>
        </div>
      </div>
    );
  };

  // Render stream controls
  const renderControls = () => {
    if (!isStreaming && !hasError) return null;

    return (
      <div className="mt-3 flex gap-2">
        {isStreaming && (
          <Button
            variant="outline"
            size="sm"
            onClick={cancelStream}
            className="text-red-600 hover:text-red-700"
          >
            <Icon name="x" size="xs" className="mr-1" />
            Cancel Stream
          </Button>
        )}
        
        {hasError && (
          <Button
            variant="outline"
            size="sm"
            onClick={retryStream}
          >
            <Icon name="refresh-cw" size="xs" className="mr-1" />
            Retry
          </Button>
        )}
      </div>
    );
  };

  return (
    <div className={`p-4 rounded-lg bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 ${className}`}>
      {/* Stream header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center">
          <Icon name="zap" size="sm" className="text-blue-500 mr-2" />
          <Typography variant="caption" className="text-gray-600 dark:text-gray-400">
            Streaming Content
          </Typography>
        </div>
        
        {/* Content type indicator */}
        <div className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs">
          {contentType.toUpperCase()}
        </div>
      </div>

      {/* Stream content */}
      <div className="min-h-[2rem]">
        {renderContent()}
      </div>

      {/* Progress indicator */}
      {renderProgress()}

      {/* Error state */}
      {renderError()}

      {/* Stream controls */}
      {renderControls()}

      {/* Stream completion indicator */}
      {streamComplete && (
        <div className="mt-3 flex items-center text-green-600 dark:text-green-400">
          <Icon name="check-circle" size="sm" className="mr-2" />
          <Typography variant="caption">
            Stream completed successfully
          </Typography>
        </div>
      )}
    </div>
  );
};

export default StreamMessage;
