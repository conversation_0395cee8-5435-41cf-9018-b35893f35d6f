/* Kanban Board Styles */

/* Drag & Drop Styles */
.kanban-card {
  user-select: none;
  position: relative;
}

/* Custom cursor for draggable elements */
.kanban-card:active {
  cursor: grabbing !important;
}

/* Drag preview styles */
.kanban-card.is-dragging {
  pointer-events: none;
}

/* Column styles */
.kanban-column {
  transition:
    background-color 0.2s ease,
    transform 0.2s ease;
}

.kanban-column.can-drop {
  background-color: rgba(59, 130, 246, 0.05);
}

/* Animations */
@keyframes pulse-border {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  70% {
    box-shadow: 0 0 0 5px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

@keyframes drop-success {
  0% {
    background-color: rgba(34, 197, 94, 0);
  }
  50% {
    background-color: rgba(34, 197, 94, 0.2);
  }
  100% {
    background-color: rgba(34, 197, 94, 0);
  }
}

.kanban-column.is-over {
  animation: pulse-border 1.5s infinite;
}

.kanban-column.drop-success {
  animation: drop-success 0.5s ease;
}

/* Variant-specific styles */
.kanban-compact .kanban-card {
  padding: 0.5rem;
  font-size: 0.875rem;
}

.kanban-detailed .kanban-card {
  padding: 1rem;
}

.kanban-vertical {
  width: 100%;
}

.kanban-vertical .kanban-column {
  width: 100%;
  margin-bottom: 1rem;
}

.kanban-swimlane .kanban-column {
  border-top-width: 4px;
}

.kanban-timeline .kanban-column {
  position: relative;
  padding-left: 2rem;
}

.kanban-timeline .kanban-column::before {
  content: '';
  position: absolute;
  left: 0.75rem;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: #e5e7eb;
}

.dark .kanban-timeline .kanban-column::before {
  background-color: #374151;
}

/* Theme-specific styles */
.kanban-colorful .kanban-card {
  border-left-width: 4px;
}

.kanban-minimal .kanban-card {
  box-shadow: none;
  border: none;
}

.kanban-bordered .kanban-card {
  border-width: 2px;
  box-shadow: none;
}

.kanban-dark {
  background-color: #1f2937;
  color: #f9fafb;
}

.kanban-light {
  background-color: #ffffff;
  color: #111827;
}

.kanban-gradient {
  background: linear-gradient(to right, #3b82f6, #8b5cf6);
}

.kanban-material .kanban-card {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.kanban-glassmorphism .kanban-card {
  backdrop-filter: blur(8px);
  background-color: rgba(255, 255, 255, 0.3);
}

.dark .kanban-glassmorphism .kanban-card {
  background-color: rgba(31, 41, 55, 0.3);
}

.kanban-neumorphism .kanban-card {
  box-shadow:
    5px 5px 10px rgba(0, 0, 0, 0.1),
    -5px -5px 10px rgba(255, 255, 255, 0.7);
}

.dark .kanban-neumorphism .kanban-card {
  box-shadow:
    5px 5px 10px rgba(0, 0, 0, 0.3),
    -5px -5px 10px rgba(255, 255, 255, 0.05);
}
