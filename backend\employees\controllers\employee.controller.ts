import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Query,
  Patch,
  ParseIntPipe,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiExtraModels,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiResponseDto, PaginatedResult } from '@/common/response/api-response-dto';
import { SWAGGER_API_TAG } from '@/common/swagger/swagger.tags';
import { EmployeeService } from '../services/employee.service';
import { CreateEmployeeDto } from '../dto/create-employee.dto';
import { UpdateEmployeeDto } from '../dto/update-employee.dto';
import { EmployeeQueryDto } from '../dto/employee-query.dto';
import { EmployeeResponseDto } from '../dto/employee-response.dto';
import { UpdateEmployeeStatusDto } from '../dto/update-employee-status.dto';
import { AssignDepartmentDto } from '../dto/assign-department.dto';
import { AssignManagerDto } from '../dto/assign-manager.dto';
import { Employee } from '../entities/employee.entity';

/**
 * Controller for employee management
 */
@ApiTags(SWAGGER_API_TAG.HRM)
@ApiExtraModels(ApiResponseDto, EmployeeResponseDto)
@Controller('api/hrm/employees')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class EmployeeController {
  constructor(private readonly employeeService: EmployeeService) {}

  /**
   * Get all employees with pagination and filtering
   */
  @Get()
  @ApiOperation({ summary: 'Get all employees' })
  @ApiResponse({
    status: 200,
    description: 'List of employees',
    schema: ApiResponseDto.getPaginatedSchema(EmployeeResponseDto),
  })
  async findAll(@Query() query: EmployeeQueryDto): Promise<ApiResponseDto<PaginatedResult<Employee>>> {
    const employees = await this.employeeService.findAll(query);
    return ApiResponseDto.success(employees);
  }

  /**
   * Get employee by ID
   */
  @Get(':id')
  @ApiOperation({ summary: 'Get employee by ID' })
  @ApiParam({ name: 'id', description: 'Employee ID', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Employee details',
    schema: ApiResponseDto.getSchema(EmployeeResponseDto),
  })
  async findOne(@Param('id', ParseIntPipe) id: number): Promise<ApiResponseDto<Employee>> {
    const employee = await this.employeeService.findById(id);
    return ApiResponseDto.success(employee);
  }

  /**
   * Get employee by user ID
   */
  @Get('user/:userId')
  @ApiOperation({ summary: 'Get employee by user ID' })
  @ApiParam({ name: 'userId', description: 'User ID', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Employee details',
    schema: ApiResponseDto.getSchema(EmployeeResponseDto),
  })
  async findByUserId(@Param('userId', ParseIntPipe) userId: number): Promise<ApiResponseDto<Employee>> {
    const employee = await this.employeeService.findByUserId(userId);
    return ApiResponseDto.success(employee);
  }

  /**
   * Create a new employee
   */
  @Post()
  @ApiOperation({ summary: 'Create a new employee' })
  @ApiResponse({
    status: 201,
    description: 'Employee created successfully',
    schema: ApiResponseDto.getSchema(EmployeeResponseDto),
  })
  async create(
    @Body() createEmployeeDto: CreateEmployeeDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<Employee>> {
    const employee = await this.employeeService.create(createEmployeeDto, user.id);
    return ApiResponseDto.created(employee);
  }

  /**
   * Update employee
   */
  @Patch(':id')
  @ApiOperation({ summary: 'Update employee' })
  @ApiParam({ name: 'id', description: 'Employee ID', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Employee updated successfully',
    schema: ApiResponseDto.getSchema(EmployeeResponseDto),
  })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateEmployeeDto: UpdateEmployeeDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<Employee>> {
    const employee = await this.employeeService.update(id, updateEmployeeDto, user.id);
    return ApiResponseDto.success(employee);
  }

  /**
   * Update employee status
   */
  @Patch(':id/status')
  @ApiOperation({ summary: 'Update employee status' })
  @ApiParam({ name: 'id', description: 'Employee ID', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Employee status updated successfully',
    schema: ApiResponseDto.getSchema(EmployeeResponseDto),
  })
  async updateStatus(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateStatusDto: UpdateEmployeeStatusDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<Employee>> {
    const employee = await this.employeeService.updateStatus(id, updateStatusDto, user.id);
    return ApiResponseDto.success(employee);
  }

  /**
   * Assign employee to department
   */
  @Patch(':id/department')
  @ApiOperation({ summary: 'Assign employee to department' })
  @ApiParam({ name: 'id', description: 'Employee ID', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Employee assigned to department successfully',
    schema: ApiResponseDto.getSchema(EmployeeResponseDto),
  })
  async assignToDepartment(
    @Param('id', ParseIntPipe) id: number,
    @Body() assignDepartmentDto: AssignDepartmentDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<Employee>> {
    const employee = await this.employeeService.assignToDepartment(
      id,
      assignDepartmentDto.departmentId,
      user.id,
    );
    return ApiResponseDto.success(employee);
  }

  /**
   * Assign manager to employee
   */
  @Patch(':id/manager')
  @ApiOperation({ summary: 'Assign manager to employee' })
  @ApiParam({ name: 'id', description: 'Employee ID', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Manager assigned to employee successfully',
    schema: ApiResponseDto.getSchema(EmployeeResponseDto),
  })
  async assignManager(
    @Param('id', ParseIntPipe) id: number,
    @Body() assignManagerDto: AssignManagerDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<Employee>> {
    const employee = await this.employeeService.assignManager(
      id,
      assignManagerDto.managerId,
      user.id,
    );
    return ApiResponseDto.success(employee);
  }

  /**
   * Delete employee
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Delete employee' })
  @ApiParam({ name: 'id', description: 'Employee ID', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Employee deleted successfully',
    schema: ApiResponseDto.getSchema(Boolean),
  })
  async remove(@Param('id', ParseIntPipe) id: number): Promise<ApiResponseDto<boolean>> {
    await this.employeeService.delete(id);
    return ApiResponseDto.success(true);
  }
}
