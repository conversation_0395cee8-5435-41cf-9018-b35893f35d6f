import React, { useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import {
  Card,
  Form,
  FormItem,
  Input,
  Button,
  Select,
  Textarea,
  FormGrid,
  FormSection,
  Icon,
} from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/types';

import { segmentSchema, SegmentFormValues } from '../../schemas/segment.schema';
import {
  SegmentStatus,
  SegmentType,
  SegmentDto,
  ConditionType,
  SegmentCondition,
  SegmentConditionGroup,
} from '../../types/segment.types';

interface SegmentFormProps {
  /**
   * Dữ liệu phân đoạn (nếu là chỉnh sửa)
   */
  segment?: SegmentDto;

  /**
   * Callback khi submit form
   */
  onSubmit: (values: SegmentFormValues) => void;

  /**
   * Callback khi hủy
   */
  onCancel: () => void;

  /**
   * Đang xử lý submit
   */
  isSubmitting?: boolean;

  /**
   * <PERSON><PERSON> sách trường có thể sử dụng cho điều kiện
   */
  availableFields?: Array<{ value: string; label: string }>;
}

/**
 * Component form thêm/sửa phân đoạn sản phẩm
 */
const SegmentForm: React.FC<SegmentFormProps> = ({
  segment,
  onSubmit,
  onCancel,
  isSubmitting = false,
  availableFields = [],
}) => {
  const { t } = useTranslation(['product']);
  const formRef = useRef<FormRef<SegmentFormValues>>(null);
  const isEditMode = !!segment;

  // Danh sách trường mặc định nếu không có availableFields
  const defaultFields = [
    { value: 'name', label: t('product:table.name') },
    { value: 'price', label: t('product:table.price') },
    { value: 'status', label: t('product:table.status') },
    { value: 'type', label: t('product:table.type') },
    { value: 'categoryId', label: t('product:table.category') },
    { value: 'stock', label: t('product:table.stock') },
  ];

  const fields = availableFields.length > 0 ? availableFields : defaultFields;

  // Danh sách loại điều kiện
  const conditionTypes = [
    { value: ConditionType.EQUALS, label: t('product:segment.conditions.equals') },
    { value: ConditionType.NOT_EQUALS, label: t('product:segment.conditions.notEquals') },
    { value: ConditionType.CONTAINS, label: t('product:segment.conditions.contains') },
    { value: ConditionType.NOT_CONTAINS, label: t('product:segment.conditions.notContains') },
    { value: ConditionType.GREATER_THAN, label: t('product:segment.conditions.greaterThan') },
    { value: ConditionType.LESS_THAN, label: t('product:segment.conditions.lessThan') },
    { value: ConditionType.BETWEEN, label: t('product:segment.conditions.between') },
    { value: ConditionType.IN, label: t('product:segment.conditions.in') },
    { value: ConditionType.NOT_IN, label: t('product:segment.conditions.notIn') },
  ];

  // Điều kiện mặc định
  const defaultCondition: SegmentCondition = {
    field: 'name',
    type: ConditionType.CONTAINS,
    value: '',
  };

  // Nhóm điều kiện mặc định
  const defaultConditionGroup: SegmentConditionGroup = {
    operator: 'AND',
    conditions: [defaultCondition],
  };

  // State cho nhóm điều kiện
  const [conditionGroup, setConditionGroup] = useState<SegmentConditionGroup>(
    segment?.conditions || defaultConditionGroup
  );

  // Xử lý submit form
  const handleSubmit = (values: SegmentFormValues) => {
    // Thêm điều kiện vào values
    const formValues = {
      ...values,
      conditions: conditionGroup,
    };
    onSubmit(formValues);
  };

  // Xử lý thêm điều kiện
  const handleAddCondition = () => {
    setConditionGroup({
      ...conditionGroup,
      conditions: [...conditionGroup.conditions, { ...defaultCondition }],
    });
  };

  // Xử lý xóa điều kiện
  const handleRemoveCondition = (index: number) => {
    if (conditionGroup.conditions.length > 1) {
      const newConditions = [...conditionGroup.conditions];
      newConditions.splice(index, 1);
      setConditionGroup({
        ...conditionGroup,
        conditions: newConditions,
      });
    }
  };

  // Xử lý thay đổi điều kiện
  const handleConditionChange = (index: number, field: keyof SegmentCondition, value: any) => {
    const newConditions = [...conditionGroup.conditions];
    newConditions[index] = {
      ...newConditions[index],
      [field]: value,
    };
    setConditionGroup({
      ...conditionGroup,
      conditions: newConditions,
    });
  };

  // Xử lý thay đổi toán tử
  const handleOperatorChange = (operator: 'AND' | 'OR') => {
    setConditionGroup({
      ...conditionGroup,
      operator,
    });
  };

  return (
    <Card
      title={isEditMode ? t('product:segment.form.editTitle') : t('product:segment.form.createTitle')}
      className="mb-4"
    >
      <Form
        ref={formRef}
        schema={segmentSchema}
        onSubmit={handleSubmit}
        className="space-y-6"
        defaultValues={{
          name: segment?.name || '',
          description: segment?.description || '',
          type: segment?.type || SegmentType.CUSTOM,
          status: segment?.status || SegmentStatus.DRAFT,
          conditions: segment?.conditions || defaultConditionGroup,
        }}
      >
        <FormGrid columns={2} columnsSm={1} gap="md">
          <FormItem name="name" label={t('product:segment.form.nameLabel')} required>
            <Input
              placeholder={t('product:segment.form.namePlaceholder')}
              fullWidth
              leftIcon={<Icon name="filter" size="sm" />}
            />
          </FormItem>

          <FormItem name="type" label={t('product:segment.form.typeLabel')} required>
            <Select
              options={[
                { value: SegmentType.CATEGORY, label: t('product:common.category') },
                { value: SegmentType.TAG, label: t('product:common.tag') },
                { value: SegmentType.PRICE_RANGE, label: t('product:common.priceRange') },
                { value: SegmentType.CUSTOM, label: t('product:common.custom') },
              ]}
              placeholder={t('product:segment.form.typeLabel')}
              fullWidth
            />
          </FormItem>
        </FormGrid>

        <FormItem name="description" label={t('product:segment.form.descriptionLabel')}>
          <Textarea
            placeholder={t('product:segment.form.descriptionPlaceholder')}
            rows={3}
            fullWidth
          />
        </FormItem>

        <FormItem name="status" label={t('product:segment.form.statusLabel')}>
          <Select
            options={[
              { value: SegmentStatus.ACTIVE, label: t('product:common.active') },
              { value: SegmentStatus.INACTIVE, label: t('product:common.inactive') },
              { value: SegmentStatus.DRAFT, label: t('product:common.draft') },
            ]}
            placeholder={t('product:segment.form.statusLabel')}
            fullWidth
          />
        </FormItem>

        <FormSection title={t('product:segment.form.conditionsLabel')}>
          <div className="mb-4">
            <div className="flex items-center space-x-2 mb-4">
              <span>{t('product:segment.form.operatorLabel')}:</span>
              <div className="flex space-x-2">
                <Button
                  type="button"
                  size="sm"
                  variant={conditionGroup.operator === 'AND' ? 'primary' : 'outline'}
                  onClick={() => handleOperatorChange('AND')}
                >
                  {t('product:segment.form.andOperator')}
                </Button>
                <Button
                  type="button"
                  size="sm"
                  variant={conditionGroup.operator === 'OR' ? 'primary' : 'outline'}
                  onClick={() => handleOperatorChange('OR')}
                >
                  {t('product:segment.form.orOperator')}
                </Button>
              </div>
            </div>

            {conditionGroup.conditions.map((condition, index) => (
              <div key={index} className="mb-4 p-4 border rounded-md bg-background">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium">{t('product:segment.form.conditionsLabel')} #{index + 1}</h4>
                  <Button
                    type="button"
                    variant="text"
                    size="sm"
                    onClick={() => handleRemoveCondition(index)}
                    disabled={conditionGroup.conditions.length <= 1}
                  >
                    <Icon name="trash-2" size="sm" className="text-danger" />
                  </Button>
                </div>

                <FormGrid columns={3} columnsSm={1} gap="md">
                  <div>
                    <label className="block text-sm font-medium mb-1">
                      {t('product:segment.form.fieldLabel')}
                    </label>
                    <Select
                      options={fields}
                      value={condition.field}
                      onChange={(value) => handleConditionChange(index, 'field', value)}
                      fullWidth
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-1">
                      {t('product:segment.form.operatorLabel')}
                    </label>
                    <Select
                      options={conditionTypes}
                      value={condition.type}
                      onChange={(value) => handleConditionChange(index, 'type', value)}
                      fullWidth
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-1">
                      {t('product:segment.form.valueLabel')}
                    </label>
                    <Input
                      value={condition.value as string}
                      onChange={(e) => handleConditionChange(index, 'value', e.target.value)}
                      placeholder={t('product:segment.form.valuePlaceholder')}
                      fullWidth
                    />
                  </div>
                </FormGrid>
              </div>
            ))}

            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleAddCondition}
              leftIcon={<Icon name="plus" size="sm" />}
            >
              {t('product:segment.form.addCondition')}
            </Button>
          </div>
        </FormSection>

        <div className="flex justify-end space-x-2 pt-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            {t('product:common.cancel')}
          </Button>
          <Button type="submit" variant="primary" loading={isSubmitting}>
            {t('product:common.save')}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default SegmentForm;
