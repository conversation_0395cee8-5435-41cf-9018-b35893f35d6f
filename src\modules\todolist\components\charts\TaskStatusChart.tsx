import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Legend, Tooltip } from 'recharts';

import { TaskDto, TaskStatus } from '../../types/task.types';

interface TaskStatusChartProps {
  tasks: TaskDto[];
}

/**
 * Chart component to display task distribution by status
 */
const TaskStatusChart: React.FC<TaskStatusChartProps> = ({ tasks }) => {
  const { t } = useTranslation(['todolist']);

  // Prepare data for the chart
  const chartData = useMemo(() => {
    const statusCounts = {
      [TaskStatus.PENDING]: 0,
      [TaskStatus.IN_PROGRESS]: 0,
      [TaskStatus.COMPLETED]: 0,
      [TaskStatus.APPROVED]: 0,
      [TaskStatus.REJECTED]: 0,
    };

    // Count tasks by status
    for (const task of tasks) {
      if (task.status) {
        statusCounts[task.status] = (statusCounts[task.status] || 0) + 1;
      }
    }

    // Combine COMPLETED and APPROVED for simplicity
    const completedCount = statusCounts[TaskStatus.COMPLETED] + statusCounts[TaskStatus.APPROVED];

    // Create data array for the chart
    return [
      {
        name: t('todolist:task.status.todo', 'To Do'),
        value: statusCounts[TaskStatus.PENDING],
        color: '#94a3b8', // gray-400
      },
      {
        name: t('todolist:task.status.inProgress', 'In Progress'),
        value: statusCounts[TaskStatus.IN_PROGRESS],
        color: '#3b82f6', // blue-500
      },
      {
        name: t('todolist:task.status.done', 'Done'),
        value: completedCount,
        color: '#22c55e', // green-500
      },
      {
        name: t('todolist:task.status.cancelled', 'Cancelled'),
        value: statusCounts[TaskStatus.REJECTED],
        color: '#ef4444', // red-500
      },
    ].filter(item => item.value > 0); // Only include statuses with tasks
  }, [tasks, t]);

  // If no tasks, show empty state
  if (chartData.length === 0) {
    return (
      <div className="h-full flex items-center justify-center text-gray-500">
        {t('todolist:dashboard.charts.noData', 'No data available')}
      </div>
    );
  }

  return (
    <ResponsiveContainer width="100%" height="100%">
      <PieChart>
        <Pie
          data={chartData}
          cx="50%"
          cy="50%"
          labelLine={false}
          outerRadius={80}
          fill="#8884d8"
          dataKey="value"
          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
        >
          {chartData.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={entry.color} />
          ))}
        </Pie>
        <Tooltip
          formatter={(value: number) => [
            `${value} ${t('todolist:dashboard.charts.tasks', 'tasks')}`,
            t('todolist:dashboard.charts.count', 'Count'),
          ]}
        />
        <Legend />
      </PieChart>
    </ResponsiveContainer>
  );
};

export default TaskStatusChart;
