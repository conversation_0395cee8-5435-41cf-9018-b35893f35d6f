import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { OkrCycleStatus } from '../../enum/okr-cycle-status.enum';

/**
 * DTO for creating a new OKR cycle
 */
export class CreateOkrCycleDto {
  /**
   * Name of the OKR cycle
   * @example "Q1-2025"
   */
  @ApiProperty({
    description: 'Tên chu kỳ OKR',
    example: 'Q1-2025',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  /**
   * Start date of the OKR cycle
   * @example "2025-01-01"
   */
  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> bắt đầu chu kỳ',
    example: '2025-01-01',
  })
  @IsDateString()
  startDate: string;

  /**
   * End date of the OKR cycle
   * @example "2025-03-31"
   */
  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> kết thúc chu kỳ',
    example: '2025-03-31',
  })
  @IsDateString()
  endDate: string;

  /**
   * Status of the OKR cycle
   * @example "PLANNING"
   */
  @ApiProperty({
    description: 'Trạng thái chu kỳ',
    enum: OkrCycleStatus,
    example: OkrCycleStatus.PLANNING,
    required: false,
  })
  @IsEnum(OkrCycleStatus)
  @IsOptional()
  status?: OkrCycleStatus;
}
