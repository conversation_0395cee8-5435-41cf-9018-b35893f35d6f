import React from 'react';

import Modal from '../Modal';

// Tạo interface kế thừa từ Modal props nhưng không yêu cầu isOpen
interface ModalWrapperProps {
  /**
   * Trạng thái hiển thị của modal (mặc định là true)
   */
  isOpen?: boolean;

  /**
   * Callback khi đóng modal
   */
  onClose: () => void;

  /**
   * Tiêu đề của modal
   */
  title?: string;

  /**
   * Nội dung của modal
   */
  children: React.ReactNode;

  /**
   * Footer của modal
   */
  footer?: React.ReactNode;

  /**
   * Kích thước của modal
   */
  size?: 'sm' | 'md' | 'lg' | 'xl';

  /**
   * Đóng modal khi click bên ngoài
   */
  closeOnClickOutside?: boolean;

  /**
   * Đóng modal khi nhấn phím Escape
   */
  closeOnEsc?: boolean;

  /**
   * Class name bổ sung
   */
  className?: string;
}

/**
 * Wrapper cho Modal component với isOpen mặc định là true
 * Sử dụng khi bạn luôn muốn hiển thị modal (ví dụ: đã được điều khiển bởi phần tử bao bọc)
 */
const ModalWrapper: React.FC<ModalWrapperProps> = ({
  isOpen = true,
  onClose,
  title,
  children,
  footer,
  size,
  closeOnClickOutside,
  closeOnEsc,
  className,
}) => {
  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      footer={footer}
      size={size}
      closeOnClickOutside={closeOnClickOutside}
      closeOnEsc={closeOnEsc}
      className={className}
    >
      {children}
    </Modal>
  );
};

export default ModalWrapper;
