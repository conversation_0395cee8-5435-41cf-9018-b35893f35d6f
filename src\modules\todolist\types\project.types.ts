/**
 * Enum vai trò thành viên dự án
 */
export enum ProjectMemberRole {
  ADMIN = 'admin',
  MEMBER = 'member',
  VIEWER = 'viewer',
}

/**
 * Interface cho Project
 */
export interface ProjectDto {
  id: number;
  title: string;
  description: string | null;
  ownerId: number;
  createdBy: number;
  createdAt: number | null;
  updatedAt: number | null;
  isActive: boolean;
}

/**
 * Interface cho ProjectMember
 */
export interface ProjectMemberDto {
  id: number;
  projectId: number;
  userId: number;
  role: ProjectMemberRole;
  createdAt: number | null;
  updatedAt: number | null;
}

/**
 * Interface cho ProjectQueryDto
 */
export interface ProjectQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
  isActive?: boolean;
  ownerId?: number;
  startDate?: number;
  endDate?: number;
}

/**
 * Interface cho CreateProjectDto
 */
export interface CreateProjectDto {
  title: string;
  description?: string;
  ownerId?: number;
}

/**
 * Interface cho UpdateProjectDto
 */
export interface UpdateProjectDto {
  title?: string;
  description?: string;
  ownerId?: number;
  isActive?: boolean;
}

/**
 * Interface cho CreateProjectMemberDto
 */
export interface CreateProjectMemberDto {
  userId: number;
  role: ProjectMemberRole;
}

/**
 * Interface cho UpdateProjectMemberDto
 */
export interface UpdateProjectMemberDto {
  role: ProjectMemberRole;
}
