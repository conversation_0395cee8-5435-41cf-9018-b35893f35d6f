import React from 'react';
import { CalendarProps } from './types';
import { useTheme } from '@/shared/contexts/theme';
import { useTranslation } from 'react-i18next';
import CalendarHeader from './CalendarHeader';
import CalendarGrid from './CalendarGrid';
import { useCalendar, useCalendarKeyboard, useCalendarTouch, useCalendarResponsive } from './hooks';

/**
 * Calendar component hiển thị lịch cho phép chọn ngày.
 *
 * Component này được sử dụng bởi DatePicker và RangePicker để hiển thị lịch và xử lý việc chọn ngày.
 * Nó hỗ trợ cả chế độ chọn ngày đơn và chọn khoảng thời gian.
 *
 * @example
 * ```tsx
 * import { Calendar } from '@/shared/components/common/DatePicker';
 * import { useState } from 'react';
 *
 * const MyComponent = () => {
 *   const [selectedDate, setSelectedDate] = useState<Date | null>(null);
 *   const [currentMonth, setCurrentMonth] = useState(new Date());
 *
 *   return (
 *     <Calendar
 *       selectedDate={selectedDate}
 *       onSelectDate={setSelectedDate}
 *       month={currentMonth}
 *       onMonthChange={setCurrentMonth}
 *       showTodayButton
 *     />
 *   );
 * };
 * ```
 *
 * @internal Thường không sử dụng trực tiếp mà thông qua DatePicker hoặc RangePicker
 */
const Calendar: React.FC<CalendarProps> = ({
  selectedDate,
  onSelectDate,
  month: propMonth,
  onMonthChange,
  disabledDates,
  minDate,
  maxDate,
  showToday = true,
  showWeekNumbers = false,
  firstDayOfWeek = 1,
  weekDayNames,
  monthNames,
  className = '',
  rangeMode = false,
  startDate,
  endDate,
  onRangeSelect,
  showTodayButton = false,
  todayButtonText,
  noRightRadius = false,
}) => {
  const { t } = useTranslation();
  useTheme();

  // Responsive design hook
  const responsive = useCalendarResponsive();

  // Calendar logic hook
  const calendar = useCalendar({
    initialDate: propMonth,
    selectedDate,
    rangeMode,
    startDate,
    endDate,
    firstDayOfWeek,
    onSelectDate,
    onRangeSelect,
    onMonthChange,
  });

  // Keyboard navigation hook
  const keyboard = useCalendarKeyboard({
    calendar,
    onSelectDate: calendar.handleSelectDate,
    disabled: false,
    autoFocus: false,
  });

  // Touch gestures hook
  const touch = useCalendarTouch({
    calendar,
    disabled: false,
    enableSwipe: responsive.enableSwipe,
  });

  // Determine final showWeekNumbers based on responsive design
  const finalShowWeekNumbers = showWeekNumbers && responsive.showWeekNumbers;

  // Base classes with responsive adjustments
  const baseClasses = `
    bg-card p-2 border-0
    ${noRightRadius ? 'rounded-l-lg' : 'rounded-lg shadow-lg'}
    ${responsive.compactMode ? 'w-72' : 'w-80'}
    ${responsive.touchOptimized ? 'touch-manipulation' : ''}
  `.trim();

  // Combine all classes
  const calendarClasses = [baseClasses, className].join(' ');

  return (
    <div
      className={calendarClasses}
      ref={keyboard.calendarRef}
      tabIndex={0}
      onKeyDown={keyboard.handleKeyDown}
      onTouchStart={touch.handleTouchStart}
      onTouchMove={touch.handleTouchMove}
      onTouchEnd={touch.handleTouchEnd}
      role="grid"
      aria-label={t('datepicker.calendar', 'Calendar')}
      style={{
        minHeight: responsive.minTouchTarget * 7, // Ensure minimum touch target size
      }}
    >
      <CalendarHeader
        month={calendar.month}
        onMonthChange={calendar.handleMonthChange}
        monthNames={monthNames}
      />

      <CalendarGrid
        month={calendar.month}
        selectedDate={rangeMode ? null : selectedDate}
        onSelectDate={calendar.handleSelectDate}
        disabledDates={disabledDates}
        minDate={minDate}
        maxDate={maxDate}
        showToday={showToday}
        showWeekNumbers={finalShowWeekNumbers}
        firstDayOfWeek={firstDayOfWeek}
        weekDayNames={weekDayNames}
        rangeMode={rangeMode}
        startDate={startDate}
        endDate={endDate}
        focusedDate={calendar.focusedDate}
      />

      {showTodayButton && (
        <div className="mt-2 flex justify-center">
          <button
            type="button"
            className={`
              px-3 py-1 text-sm rounded transition-colors
              ${responsive.touchOptimized ? 'min-h-[44px] px-4' : ''}
              bg-gray-100 hover:bg-gray-200
              dark:bg-gray-800 dark:hover:bg-gray-700
            `.trim()}
            onClick={calendar.handleTodayClick}
            style={{
              minHeight: responsive.touchOptimized ? responsive.minTouchTarget : undefined,
            }}
          >
            {todayButtonText || t('datepicker.today', 'Today')}
          </button>
        </div>
      )}

      {/* Swipe indicator for mobile */}
      {touch.isSwipeInProgress && responsive.isMobile && (
        <div className="absolute inset-0 pointer-events-none flex items-center justify-center">
          <div className="bg-black bg-opacity-50 text-white px-3 py-1 rounded text-sm">
            {touch.swipeDirection === 'left' ? '→' : '←'}
          </div>
        </div>
      )}
    </div>
  );
};

export default Calendar;
