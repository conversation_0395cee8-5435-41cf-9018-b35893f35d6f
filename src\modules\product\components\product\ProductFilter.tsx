import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import {
  Card,
  Button,
  Select,
  Input,
  Icon,
  ResponsiveGrid,
} from '@/shared/components/common';

import { ProductStatus, ProductType, ProductQueryDto } from '../../types/product.types';

interface ProductFilterProps {
  /**
   * Callback khi áp dụng filter
   */
  onFilter: (filters: Partial<ProductQueryDto>) => void;

  /**
   * Giá trị filter hiện tại
   */
  currentFilters?: Partial<ProductQueryDto>;

  /**
   * Danh sách danh mục
   */
  categories?: Array<{ value: string; label: string }>;

  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Component filter sản phẩm
 */
const ProductFilter: React.FC<ProductFilterProps> = ({
  onFilter,
  currentFilters = {},
  categories = [],
  className = '',
}) => {
  const { t } = useTranslation(['product']);

  // State cho các filter
  const [status, setStatus] = useState<ProductStatus | undefined>(currentFilters.status);
  const [type, setType] = useState<ProductType | undefined>(currentFilters.type);
  const [categoryId, setCategoryId] = useState<string | undefined>(currentFilters.categoryId);
  const [minPrice, setMinPrice] = useState<number | undefined>(currentFilters.minPrice);
  const [maxPrice, setMaxPrice] = useState<number | undefined>(currentFilters.maxPrice);
  const [search, setSearch] = useState<string | undefined>(currentFilters.search);

  // Xử lý áp dụng filter
  const handleApplyFilter = () => {
    onFilter({
      status,
      type,
      categoryId,
      minPrice,
      maxPrice,
      search,
    });
  };

  // Xử lý reset filter
  const handleResetFilter = () => {
    setStatus(undefined);
    setType(undefined);
    setCategoryId(undefined);
    setMinPrice(undefined);
    setMaxPrice(undefined);
    setSearch(undefined);
    onFilter({});
  };

  return (
    <Card className={`mb-4 ${className}`}>
      <div className="space-y-4">
        <div className="flex flex-col sm:flex-row sm:items-center gap-2">
          <div className="flex-grow">
            <Input
              placeholder={t('product:list.searchPlaceholder')}
              value={search || ''}
              onChange={(e) => setSearch(e.target.value)}
              leftIcon={<Icon name="search" size="sm" />}
              fullWidth
            />
          </div>
          <div className="flex space-x-2">
            <Button
              variant="primary"
              onClick={handleApplyFilter}
              leftIcon={<Icon name="filter" size="sm" />}
            >
              {t('product:common.filter')}
            </Button>
            <Button variant="outline" onClick={handleResetFilter}>
              {t('product:common.cancel')}
            </Button>
          </div>
        </div>

        <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 4 }} gap={4}>
          <div>
            <label className="block text-sm font-medium mb-1">
              {t('product:list.filterByStatus')}
            </label>
            <Select
              options={[
                { value: '', label: t('product:common.all') },
                { value: ProductStatus.ACTIVE, label: t('product:common.active') },
                { value: ProductStatus.INACTIVE, label: t('product:common.inactive') },
                { value: ProductStatus.DRAFT, label: t('product:common.draft') },
              ]}
              value={status || ''}
              onChange={(value) => setStatus(value ? (value as ProductStatus) : undefined)}
              fullWidth
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">
              {t('product:list.filterByType')}
            </label>
            <Select
              options={[
                { value: '', label: t('product:common.all') },
                { value: ProductType.PHYSICAL, label: t('product:common.physical') },
                { value: ProductType.DIGITAL, label: t('product:common.digital') },
                { value: ProductType.SERVICE, label: t('product:common.service') },
              ]}
              value={type || ''}
              onChange={(value) => setType(value ? (value as ProductType) : undefined)}
              fullWidth
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">
              {t('product:list.filterByCategory')}
            </label>
            <Select
              options={[{ value: '', label: t('product:common.all') }, ...categories]}
              value={categoryId || ''}
              onChange={(value) => setCategoryId(value || undefined)}
              fullWidth
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">
              {t('product:list.filterByPriceRange')}
            </label>
            <div className="flex items-center space-x-2">
              <Input
                type="number"
                placeholder="Min"
                value={minPrice || ''}
                onChange={(e) => setMinPrice(e.target.value ? Number(e.target.value) : undefined)}
                className="flex-1"
              />
              <span>-</span>
              <Input
                type="number"
                placeholder="Max"
                value={maxPrice || ''}
                onChange={(e) => setMaxPrice(e.target.value ? Number(e.target.value) : undefined)}
                className="flex-1"
              />
            </div>
          </div>
        </ResponsiveGrid>
      </div>
    </Card>
  );
};

export default ProductFilter;
