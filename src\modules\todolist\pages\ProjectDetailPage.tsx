import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams, useNavigate } from 'react-router-dom';

import {
  Card,
  Button,
  Tabs,
  Badge,
  Tooltip,
  IconButton,
  Typography,
  Divider,
  Table,
} from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { formatDate } from '@/shared/utils/date';

import ProjectForm from '../components/ProjectForm';
import ProjectMemberForm from '../components/ProjectMemberForm';
import { useProject, useProjectMembers } from '../hooks/useProjects';
import { useProjectTasks } from '../hooks/useTasks';
import { ProjectMemberRole, ProjectMemberDto } from '../types/project.types';
import { TaskStatus, TaskDto } from '../types/task.types';

/**
 * Project detail page
 */
const ProjectDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const projectId = parseInt(id || '0', 10);
  const { t } = useTranslation(['common', 'todolist']);
  const navigate = useNavigate();
  const {
    isVisible: isEditFormOpen,
    showForm: showEditForm,
    hideForm: hideEditForm,
  } = useSlideForm();
  const {
    isVisible: isMemberFormOpen,
    showForm: showMemberForm,
    hideForm: hideMemberForm,
  } = useSlideForm();
  const [activeTab, setActiveTab] = useState<string>('details');

  // Fetch project details
  const { data: project, isLoading, refetch } = useProject(projectId);

  // Fetch project members
  const { data: membersData, isLoading: isLoadingMembers } = useProjectMembers(projectId);

  // Fetch project tasks
  const { data: tasksData, isLoading: isLoadingTasks } = useProjectTasks(projectId);

  // Handle edit form submit
  const handleEditSubmit = () => {
    hideEditForm();
    refetch();
  };

  // Handle member form submit
  const handleMemberSubmit = () => {
    hideMemberForm();
    refetch();
  };

  // Handle back button click
  const handleBack = () => {
    navigate('/todolist/projects');
  };

  // Handle view task
  const handleViewTask = (taskId: number) => {
    navigate(`/todolist/tasks/${taskId}`);
  };

  // Get role badge color
  const getRoleBadgeColor = (role: ProjectMemberRole) => {
    switch (role) {
      case ProjectMemberRole.ADMIN:
        return 'bg-purple-100 text-purple-800';
      case ProjectMemberRole.MEMBER:
        return 'bg-blue-100 text-blue-800';
      case ProjectMemberRole.VIEWER:
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get role text
  const getRoleText = (role: ProjectMemberRole) => {
    switch (role) {
      case ProjectMemberRole.ADMIN:
        return t('todolist:project.roles.admin', 'Admin');
      case ProjectMemberRole.MEMBER:
        return t('todolist:project.roles.member', 'Member');
      case ProjectMemberRole.VIEWER:
        return t('todolist:project.roles.viewer', 'Viewer');
      default:
        return '';
    }
  };

  // Get status badge color
  const getStatusBadgeColor = (status: TaskStatus | null) => {
    switch (status) {
      case TaskStatus.PENDING:
        return 'bg-gray-100 text-gray-800';
      case TaskStatus.IN_PROGRESS:
        return 'bg-blue-100 text-blue-800';
      case TaskStatus.COMPLETED:
      case TaskStatus.APPROVED:
        return 'bg-green-100 text-green-800';
      case TaskStatus.REJECTED:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get status text
  const getStatusText = (status: TaskStatus | null) => {
    switch (status) {
      case TaskStatus.PENDING:
        return t('todolist:task.status.todo', 'To Do');
      case TaskStatus.IN_PROGRESS:
        return t('todolist:task.status.inProgress', 'In Progress');
      case TaskStatus.COMPLETED:
      case TaskStatus.APPROVED:
        return t('todolist:task.status.done', 'Done');
      case TaskStatus.REJECTED:
        return t('todolist:task.status.cancelled', 'Cancelled');
      default:
        return '';
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-lg text-gray-500">{t('common:loading', 'Loading...')}</div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="text-center py-8">
        <Typography variant="h4" className="mb-2">
          {t('todolist:project.notFound', 'Project not found')}
        </Typography>
        <Button onClick={handleBack} className="mt-4">
          {t('common:back', 'Back')}
        </Button>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-6">
        <div className="flex items-center space-x-2 text-sm">
          <a href="/todolist" className="text-blue-500 hover:underline">
            {t('todolist:title', 'Task Management')}
          </a>
          <span className="text-gray-500">/</span>
          <a href="/todolist/projects" className="text-blue-500 hover:underline">
            {t('todolist:modules.projects.title', 'Projects')}
          </a>
          <span className="text-gray-500">/</span>
          <span className="text-gray-700 font-medium">{project.title}</span>
        </div>
      </div>

      <div className="flex justify-between items-center mb-6">
        <div>
          <Typography variant="h3">{project.title}</Typography>
          <div className="mt-1">
            <Badge
              className={
                project.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
              }
            >
              {project.isActive
                ? t('todolist:project.status.active', 'Active')
                : t('todolist:project.status.inactive', 'Inactive')}
            </Badge>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={handleBack}>
            {t('common:back', 'Back')}
          </Button>
          <Button onClick={showEditForm}>{t('common:edit', 'Edit')}</Button>
        </div>
      </div>

      <Card className="mb-6">
        <Tabs
          items={[
            {
              key: 'details',
              label: t('todolist:project.tabs.details', 'Details'),
              children: null,
            },
            {
              key: 'members',
              label: t('todolist:project.tabs.members', 'Members'),
              children: null,
            },
            {
              key: 'tasks',
              label: t('todolist:project.tabs.tasks', 'Tasks'),
              children: null,
            },
          ]}
          activeKey={activeTab}
          onChange={setActiveTab}
        />

        <div className="p-4">
          {activeTab === 'details' && (
            <div className="space-y-4">
              <div>
                <Typography variant="h6" className="mb-2">
                  {t('todolist:project.fields.description', 'Description')}
                </Typography>
                <Typography variant="body1">
                  {project.description ||
                    t('todolist:project.noDescription', 'No description provided')}
                </Typography>
              </div>

              <Divider />

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Typography variant="subtitle2" className="text-gray-500">
                    {t('todolist:project.fields.createdAt', 'Created At')}
                  </Typography>
                  <Typography variant="body2">{formatDate(project.createdAt || 0)}</Typography>
                </div>
                <div>
                  <Typography variant="subtitle2" className="text-gray-500">
                    {t('todolist:project.fields.updatedAt', 'Updated At')}
                  </Typography>
                  <Typography variant="body2">{formatDate(project.updatedAt || 0)}</Typography>
                </div>
                <div>
                  <Typography variant="subtitle2" className="text-gray-500">
                    {t('todolist:project.fields.ownerId', 'Owner ID')}
                  </Typography>
                  <Typography variant="body2">{project.ownerId}</Typography>
                </div>
                <div>
                  <Typography variant="subtitle2" className="text-gray-500">
                    {t('todolist:project.fields.createdBy', 'Created By')}
                  </Typography>
                  <Typography variant="body2">{project.createdBy}</Typography>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'members' && (
            <div>
              <div className="flex justify-between items-center mb-4">
                <Typography variant="h6">
                  {t('todolist:project.members.title', 'Project Members')}
                </Typography>
                <Button size="sm" onClick={showMemberForm}>
                  {t('todolist:project.members.add', 'Add Member')}
                </Button>
              </div>

              {isLoadingMembers ? (
                <div className="flex justify-center items-center h-32">
                  <div className="text-gray-500">{t('common:loading', 'Loading...')}</div>
                </div>
              ) : membersData?.items && membersData.items.length > 0 ? (
                <Table
                  columns={[
                    {
                      key: 'userId',
                      title: t('todolist:project.members.userId', 'User ID'),
                      render: (_, row: ProjectMemberDto) => row.userId,
                    },
                    {
                      key: 'role',
                      title: t('todolist:project.members.role', 'Role'),
                      render: (_, row: ProjectMemberDto) => (
                        <Badge className={getRoleBadgeColor(row.role)}>
                          {getRoleText(row.role)}
                        </Badge>
                      ),
                    },
                    {
                      key: 'createdAt',
                      title: t('todolist:project.members.joinedAt', 'Joined At'),
                      render: (_, row: ProjectMemberDto) => formatDate(row.createdAt || 0),
                    },
                    {
                      key: 'actions',
                      title: t('common:actions', 'Actions'),
                      render: (_, row: ProjectMemberDto) => (
                        <div className="flex space-x-1">
                          <Tooltip content={t('common:edit', 'Edit')}>
                            <IconButton
                              icon="edit"
                              size="sm"
                              variant="outline"
                              onClick={() => console.log('Edit member', row.id)}
                            />
                          </Tooltip>
                          <Tooltip content={t('common:delete', 'Delete')}>
                            <IconButton
                              icon="trash"
                              size="sm"
                              variant="outline"
                              className="text-red-500"
                              onClick={() => console.log('Delete member', row.id)}
                            />
                          </Tooltip>
                        </div>
                      ),
                    },
                  ]}
                  data={membersData.items}
                />
              ) : (
                <div className="text-center py-8 text-gray-500">
                  {t('todolist:project.members.empty', 'No members found')}
                </div>
              )}
            </div>
          )}

          {activeTab === 'tasks' && (
            <div>
              <div className="flex justify-between items-center mb-4">
                <Typography variant="h6">
                  {t('todolist:project.tasks.title', 'Project Tasks')}
                </Typography>
                <Button size="sm" onClick={() => navigate('/todolist/tasks')}>
                  {t('todolist:project.tasks.add', 'Add Task')}
                </Button>
              </div>

              {isLoadingTasks ? (
                <div className="flex justify-center items-center h-32">
                  <div className="text-gray-500">{t('common:loading', 'Loading...')}</div>
                </div>
              ) : tasksData?.items && tasksData.items.length > 0 ? (
                <Table
                  columns={[
                    {
                      key: 'title',
                      title: t('todolist:task.fields.title', 'Title'),
                      render: (_, row: TaskDto) => (
                        <div
                          className="font-medium cursor-pointer"
                          onClick={() => handleViewTask(row.id)}
                        >
                          {row.title}
                        </div>
                      ),
                    },
                    {
                      key: 'status',
                      title: t('todolist:task.fields.status', 'Status'),
                      render: (_, row: TaskDto) => (
                        <Badge className={getStatusBadgeColor(row.status)}>
                          {getStatusText(row.status)}
                        </Badge>
                      ),
                    },
                    {
                      key: 'createdAt',
                      title: t('todolist:task.fields.createdAt', 'Created At'),
                      render: (_, row: TaskDto) => formatDate(row.createdAt || 0),
                    },
                    {
                      key: 'actions',
                      title: t('common:actions', 'Actions'),
                      render: (_, row: TaskDto) => (
                        <Button size="sm" variant="outline" onClick={() => handleViewTask(row.id)}>
                          {t('common:view', 'View')}
                        </Button>
                      ),
                    },
                  ]}
                  data={tasksData.items}
                />
              ) : (
                <div className="text-center py-8 text-gray-500">
                  {t('todolist:project.tasks.empty', 'No tasks found')}
                </div>
              )}
            </div>
          )}
        </div>
      </Card>

      {/* Edit Project Form */}
      <SlideInForm isVisible={isEditFormOpen}>
        <div className="p-4 bg-white rounded-lg shadow-lg">
          <h2 className="text-xl font-bold mb-4">
            {t('todolist:project.form.edit', 'Edit Project')}
          </h2>
          <ProjectForm project={project} onSubmit={handleEditSubmit} onCancel={hideEditForm} />
        </div>
      </SlideInForm>

      {/* Add Member Form */}
      <SlideInForm isVisible={isMemberFormOpen}>
        <div className="p-4 bg-white rounded-lg shadow-lg">
          <h2 className="text-xl font-bold mb-4">
            {t('todolist:project.members.add', 'Add Member')}
          </h2>
          <ProjectMemberForm
            projectId={projectId}
            onSubmit={handleMemberSubmit}
            onCancel={hideMemberForm}
          />
        </div>
      </SlideInForm>
    </div>
  );
};

export default ProjectDetailPage;
