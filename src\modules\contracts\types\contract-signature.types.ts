/**
 * Contract Signature Types for Frontend
 */

export enum SignatureStatus {
  PENDING = 'pending',
  SIGNED = 'signed',
  DECLINED = 'declined',
  EXPIRED = 'expired',
}

export enum SignatureType {
  ELECTRONIC = 'electronic',
  DIGITAL = 'digital',
  WET = 'wet',
}

export interface SignatureLocation {
  latitude?: number;
  longitude?: number;
  address?: string;
}

export interface ContractSignature {
  id: string;
  status: SignatureStatus;
  type: SignatureType;
  signerName: string;
  signerEmail: string;
  signerPhone?: string;
  signerTitle?: string;
  signerCompany?: string;
  signOrder: number;
  required: boolean;
  signatureData?: string;
  signatureHash?: string;
  ipAddress?: string;
  userAgent?: string;
  location?: SignatureLocation;
  signedAt?: string;
  sentAt?: string;
  viewedAt?: string;
  expiresAt?: string;
  declineReason?: string;
  notes?: string;
  externalSignatureId?: string;
  externalData?: Record<string, any>;
  metadata?: Record<string, any>;
  
  // Relations
  contractId: string;
  userId?: string;
  delegatedById?: string;
  
  // Timestamps
  createdAt: string;
  updatedAt: string;
  
  // Related data
  user?: {
    id: string;
    email: string;
    name: string;
  };
  delegatedBy?: {
    id: string;
    email: string;
    name: string;
  };
}

export interface SignatureQueryParams {
  page?: number;
  limit?: number;
  contractId?: string;
  status?: SignatureStatus[];
  type?: SignatureType[];
  signerEmail?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface SignatureCreateDto {
  contractId: string;
  signerName: string;
  signerEmail: string;
  signerPhone?: string;
  signerTitle?: string;
  signerCompany?: string;
  signOrder?: number;
  required?: boolean;
  type?: SignatureType;
  expiresAt?: string;
  notes?: string;
  metadata?: Record<string, any>;
}

export interface SignatureUpdateDto {
  signerName?: string;
  signerEmail?: string;
  signerPhone?: string;
  signerTitle?: string;
  signerCompany?: string;
  signOrder?: number;
  required?: boolean;
  expiresAt?: string;
  notes?: string;
  metadata?: Record<string, any>;
}

export interface SignatureSignDto {
  signatureData: string;
  ipAddress?: string;
  userAgent?: string;
  location?: SignatureLocation;
}

export interface SignatureDeclineDto {
  reason: string;
}

export interface SignatureDelegateDto {
  toUserId: string;
  message?: string;
}

export interface SignatureListResponse {
  items: ContractSignature[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface SignatureInfo {
  signer: string;
  signedAt: string | null;
  ipAddress: string | null;
  isValid: boolean;
}

export interface SignatureValidationResult {
  isValid: boolean;
  errors: string[];
  signatureInfo?: SignatureInfo;
}

export interface SignatureProgress {
  total: number;
  signed: number;
  pending: number;
  declined: number;
  expired: number;
  percentage: number;
  isComplete: boolean;
}
