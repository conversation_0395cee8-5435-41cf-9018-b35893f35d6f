import React, { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@/shared/contexts/theme';
import CalendarHeader from './CalendarHeader';
import CalendarGrid from './CalendarGrid';
import { useCalendar, useCalendarKeyboard, useCalendarTouch, useCalendarResponsive } from './hooks';

/**
 * Interface cho Calendar Event
 */
export interface CalendarEvent {
  id: string;
  date: Date;
  title: string;
  description?: string;
  color?: string;
  type?: 'dot' | 'badge' | 'highlight';
  priority?: 'low' | 'medium' | 'high';
  category?: string;
  allDay?: boolean;
  startTime?: string;
  endTime?: string;
}

/**
 * Props cho EventCalendar component
 */
export interface EventCalendarProps {
  /**
   * Ngày đã chọn
   */
  selectedDate?: Date | null;

  /**
   * Callback khi chọn ngày
   */
  onSelectDate?: (date: Date) => void;

  /**
   * Tháng hiển thị
   */
  month?: Date;

  /**
   * Callback khi tháng thay đổi
   */
  onMonthChange?: (date: Date) => void;

  /**
   * Danh sách events
   */
  events?: CalendarEvent[];

  /**
   * Callback khi click vào event
   */
  onEventClick?: (event: CalendarEvent) => void;

  /**
   * Callback khi click vào ngày có events
   */
  onDateWithEventsClick?: (date: Date, events: CalendarEvent[]) => void;

  /**
   * Số events tối đa hiển thị trên mỗi ngày
   */
  maxEventsPerDay?: number;

  /**
   * Hiển thị tooltip cho events
   */
  showEventTooltip?: boolean;

  /**
   * Các ngày không được phép chọn
   */
  disabledDates?: Date[] | ((date: Date) => boolean);

  /**
   * Ngày tối thiểu được phép chọn
   */
  minDate?: Date;

  /**
   * Ngày tối đa được phép chọn
   */
  maxDate?: Date;

  /**
   * Hiển thị ngày hiện tại
   */
  showToday?: boolean;

  /**
   * Hiển thị tuần
   */
  showWeekNumbers?: boolean;

  /**
   * Ngày đầu tiên của tuần (0: Chủ nhật, 1: Thứ 2, ...)
   */
  firstDayOfWeek?: 0 | 1 | 2 | 3 | 4 | 5 | 6;

  /**
   * Tên các ngày trong tuần
   */
  weekDayNames?: string[];

  /**
   * Tên các tháng
   */
  monthNames?: string[];

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Hiển thị button today
   */
  showTodayButton?: boolean;

  /**
   * Text cho button today
   */
  todayButtonText?: string;

  /**
   * Hiển thị legend cho event types
   */
  showEventLegend?: boolean;

  /**
   * Custom event renderer
   */
  renderEvent?: (event: CalendarEvent) => React.ReactNode;
}

/**
 * Calendar component với hỗ trợ hiển thị events
 */
const EventCalendar: React.FC<EventCalendarProps> = ({
  selectedDate,
  onSelectDate,
  month: propMonth,
  onMonthChange,
  events = [],
  onDateWithEventsClick,
  disabledDates,
  minDate,
  maxDate,
  showToday = true,
  showWeekNumbers = false,
  firstDayOfWeek = 1,
  weekDayNames,
  monthNames,
  className = '',
  showTodayButton = false,
  todayButtonText,
  showEventLegend = false,
}) => {
  const { t } = useTranslation();
  useTheme();



  // Responsive design hook
  const responsive = useCalendarResponsive();

  // Calendar logic hook
  const calendar = useCalendar({
    initialDate: propMonth,
    selectedDate,
    firstDayOfWeek,
    onSelectDate,
    onMonthChange,
  });

  // Keyboard navigation hook
  const keyboard = useCalendarKeyboard({
    calendar,
    onSelectDate: calendar.handleSelectDate,
    disabled: false,
    autoFocus: false,
  });

  // Touch gestures hook
  const touch = useCalendarTouch({
    calendar,
    disabled: false,
    enableSwipe: responsive.enableSwipe,
  });

  // Group events by date
  const eventsByDate = useMemo(() => {
    const grouped: Record<string, CalendarEvent[]> = {};
    
    events.forEach(event => {
      const dateKey = event.date.toISOString().split('T')[0];
      if (!grouped[dateKey]) {
        grouped[dateKey] = [];
      }
      grouped[dateKey].push(event);
    });

    // Sort events by priority and time
    Object.keys(grouped).forEach(dateKey => {
      grouped[dateKey].sort((a, b) => {
        // Sort by priority first
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        const aPriority = priorityOrder[a.priority || 'medium'];
        const bPriority = priorityOrder[b.priority || 'medium'];
        
        if (aPriority !== bPriority) {
          return bPriority - aPriority;
        }

        // Then by start time
        if (a.startTime && b.startTime) {
          return a.startTime.localeCompare(b.startTime);
        }

        return 0;
      });
    });

    return grouped;
  }, [events]);

  // Get events for a specific date
  const getEventsForDate = useCallback((date: Date): CalendarEvent[] => {
    const dateKey = date.toISOString().split('T')[0];
    return eventsByDate[dateKey] || [];
  }, [eventsByDate]);

  // Handle date click with events
  const handleDateClick = useCallback((date: Date) => {
    const dateEvents = getEventsForDate(date);
    
    if (dateEvents.length > 0 && onDateWithEventsClick) {
      onDateWithEventsClick(date, dateEvents);
    }
    
    calendar.handleSelectDate(date);
  }, [getEventsForDate, onDateWithEventsClick, calendar]);

  // Determine final showWeekNumbers based on responsive design
  const finalShowWeekNumbers = showWeekNumbers && responsive.showWeekNumbers;

  // Base classes with responsive adjustments
  const baseClasses = `
    bg-white dark:bg-dark-light rounded-lg shadow-lg p-2
    ${responsive.compactMode ? 'w-72' : 'w-80'}
    ${responsive.touchOptimized ? 'touch-manipulation' : ''}
  `.trim();

  // Combine all classes
  const calendarClasses = [baseClasses, className].join(' ');

  // Event legend data
  const eventTypes = useMemo(() => {
    const types = new Set(events.map(event => event.type || 'dot'));
    return Array.from(types);
  }, [events]);

  // Default event renderer
  const defaultEventRenderer = useCallback((event: CalendarEvent) => {
    const eventColor = event.color || '#3B82F6';
    
    switch (event.type) {
      case 'badge':
        return (
          <div
            className="text-xs px-1 rounded text-white truncate"
            style={{ backgroundColor: eventColor }}
            title={event.title}
          >
            {event.title}
          </div>
        );
      
      case 'highlight':
        return (
          <div
            className="absolute inset-0 opacity-20 rounded"
            style={{ backgroundColor: eventColor }}
            title={event.title}
          />
        );
      
      default: // dot
        return (
          <div
            className="w-2 h-2 rounded-full"
            style={{ backgroundColor: eventColor }}
            title={event.title}
          />
        );
    }
  }, []);

  return (
    <div className="relative">
      <div
        className={calendarClasses}
        ref={keyboard.calendarRef}
        tabIndex={0}
        onKeyDown={keyboard.handleKeyDown}
        onTouchStart={touch.handleTouchStart}
        onTouchMove={touch.handleTouchMove}
        onTouchEnd={touch.handleTouchEnd}
        role="grid"
        aria-label={t('datepicker.eventCalendar', 'Event Calendar')}
        style={{
          minHeight: responsive.minTouchTarget * 7,
        }}
      >
        <CalendarHeader 
          month={calendar.month} 
          onMonthChange={calendar.handleMonthChange} 
          monthNames={monthNames} 
        />

        <CalendarGrid
          month={calendar.month}
          selectedDate={selectedDate}
          onSelectDate={handleDateClick}
          disabledDates={disabledDates}
          minDate={minDate}
          maxDate={maxDate}
          showToday={showToday}
          showWeekNumbers={finalShowWeekNumbers}
          firstDayOfWeek={firstDayOfWeek}
          weekDayNames={weekDayNames}
          rangeMode={false}
          startDate={null}
          endDate={null}
          focusedDate={calendar.focusedDate}
          // Custom props for events - commented out as these props don't exist in CalendarGrid
          // events={events}
          // eventsByDate={eventsByDate}
          // getEventsForDate={getEventsForDate}
          // maxEventsPerDay={maxEventsPerDay}
          // onEventClick={handleEventClick}
          // onEventHover={handleEventHover}
          // renderEvent={renderEvent || defaultEventRenderer}
        />

        {/* Action buttons */}
        {showTodayButton && (
          <div className="mt-2 flex justify-center">
            <button
              type="button"
              className={`
                px-3 py-1 text-sm rounded transition-colors
                ${responsive.touchOptimized ? 'min-h-[44px] px-4' : ''}
                bg-gray-100 hover:bg-gray-200 
                dark:bg-gray-800 dark:hover:bg-gray-700
              `.trim()}
              onClick={calendar.handleTodayClick}
              style={{
                minHeight: responsive.touchOptimized ? responsive.minTouchTarget : undefined,
              }}
            >
              {todayButtonText || t('datepicker.today', 'Today')}
            </button>
          </div>
        )}

        {/* Swipe indicator for mobile */}
        {touch.isSwipeInProgress && responsive.isMobile && (
          <div className="absolute inset-0 pointer-events-none flex items-center justify-center">
            <div className="bg-black bg-opacity-50 text-white px-3 py-1 rounded text-sm">
              {touch.swipeDirection === 'left' ? '→' : '←'}
            </div>
          </div>
        )}
      </div>

      {/* Event Legend */}
      {showEventLegend && eventTypes.length > 0 && (
        <div className="mt-2 p-2 bg-gray-50 dark:bg-gray-800 rounded text-xs">
          <div className="font-medium mb-1">{t('datepicker.eventLegend', 'Event Types')}</div>
          <div className="flex flex-wrap gap-2">
            {eventTypes.map(type => (
              <div key={type} className="flex items-center gap-1">
                {defaultEventRenderer({
                  id: 'legend',
                  date: new Date(),
                  title: type,
                  type: type as CalendarEvent['type']
                })}
                <span className="capitalize">{type}</span>
              </div>
            ))}
          </div>
        </div>
      )}


    </div>
  );
};

export default EventCalendar;
