/**
 * Contract Template Types for Frontend
 */

export enum TemplateStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  ARCHIVED = 'archived',
}

export interface TemplateVariable {
  name: string;
  type: 'text' | 'number' | 'date' | 'boolean' | 'select';
  label: string;
  required: boolean;
  defaultValue?: any;
  options?: string[];
  placeholder?: string;
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
  };
}

export interface TemplateDefaultTerms {
  paymentTerms?: string;
  deliveryTerms?: string;
  cancellationPolicy?: string;
  disputeResolution?: string;
  governingLaw?: string;
}

export interface TemplateApprovalWorkflow {
  steps: {
    order: number;
    roleId?: string;
    userId?: string;
    required: boolean;
    description: string;
  }[];
  autoApprove?: boolean;
  approvalThreshold?: number;
}

export interface TemplateSignatureSettings {
  requiredSignatures: {
    party: 'party1' | 'party2' | 'both';
    roleId?: string;
    userId?: string;
    order: number;
  }[];
  signatureType: 'electronic' | 'digital' | 'wet';
  allowDelegation: boolean;
}

export interface ContractTemplate {
  id: string;
  name: string;
  description?: string;
  type: string;
  status: TemplateStatus;
  templateCode: string;
  content: string;
  variables?: TemplateVariable[];
  defaultTerms?: TemplateDefaultTerms;
  approvalWorkflow?: TemplateApprovalWorkflow;
  signatureSettings?: TemplateSignatureSettings;
  isPublic: boolean;
  usageCount: number;
  version: string;
  tags?: string[];
  metadata?: Record<string, any>;
  
  // Relations
  companyId: string;
  createdById: string;
  lastModifiedById?: string;
  
  // Timestamps
  createdAt: string;
  updatedAt: string;
  
  // Related data
  createdBy?: {
    id: string;
    email: string;
    name: string;
  };
  lastModifiedBy?: {
    id: string;
    email: string;
    name: string;
  };
}

export interface ContractTemplateQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: TemplateStatus[];
  type?: string[];
  isPublic?: boolean;
  tags?: string[];
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface ContractTemplateCreateDto {
  name: string;
  description?: string;
  type: string;
  content: string;
  variables?: TemplateVariable[];
  defaultTerms?: TemplateDefaultTerms;
  approvalWorkflow?: TemplateApprovalWorkflow;
  signatureSettings?: TemplateSignatureSettings;
  isPublic?: boolean;
  tags?: string[];
  metadata?: Record<string, any>;
}

export interface ContractTemplateUpdateDto extends Partial<ContractTemplateCreateDto> {
  status?: TemplateStatus;
  version?: string;
}

export interface ContractTemplateListResponse {
  items: ContractTemplate[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface TemplateValidationResult {
  isValid: boolean;
  errors: string[];
}

export interface TemplateGenerationResult {
  content: string;
  variables: Record<string, any>;
  validation: TemplateValidationResult;
}
