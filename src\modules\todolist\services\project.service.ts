import { apiClient } from '@/shared/api';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';

import {
  ProjectDto,
  ProjectQueryDto,
  CreateProjectDto,
  UpdateProjectDto,
  ProjectMemberDto,
  CreateProjectMemberDto,
  UpdateProjectMemberDto,
} from '../types/project.types';

/**
 * Service cho dự án (project)
 */
export const ProjectService = {
  /**
   * L<PERSON>y danh sách dự án
   * @param params Tham số truy vấn
   * @returns Promise với phản hồi API chứa danh sách dự án
   */
  getProjects: (params?: ProjectQueryDto) => {
    return apiClient.get<PaginatedResult<ProjectDto>>('/api/projects', { params });
  },

  /**
   * Lấy chi tiết dự án theo ID
   * @param id ID dự án
   * @returns Promise với phản hồi API chứa chi tiết dự án
   */
  getProject: (id: number) => {
    return apiClient.get<ProjectDto>(`/api/projects/${id}`);
  },

  /**
   * Tạo dự án mới
   * @param data Dữ liệu tạo dự án
   * @returns Promise với phản hồi API chứa dự án đã tạo
   */
  createProject: (data: CreateProjectDto) => {
    return apiClient.post<ProjectDto>('/api/projects', data);
  },

  /**
   * Cập nhật dự án
   * @param id ID dự án
   * @param data Dữ liệu cập nhật dự án
   * @returns Promise với phản hồi API chứa dự án đã cập nhật
   */
  updateProject: (id: number, data: UpdateProjectDto) => {
    return apiClient.patch<ProjectDto>(`/api/projects/${id}`, data);
  },

  /**
   * Xóa dự án
   * @param id ID dự án
   * @returns Promise với phản hồi API chứa kết quả xóa
   */
  deleteProject: (id: number) => {
    return apiClient.delete<boolean>(`/api/projects/${id}`);
  },

  /**
   * Lấy danh sách thành viên dự án
   * @param projectId ID dự án
   * @returns Promise với phản hồi API chứa danh sách thành viên
   */
  getProjectMembers: (projectId: number) => {
    return apiClient.get<PaginatedResult<ProjectMemberDto>>(`/api/projects/${projectId}/members`);
  },

  /**
   * Thêm thành viên vào dự án
   * @param projectId ID dự án
   * @param data Dữ liệu thành viên
   * @returns Promise với phản hồi API chứa thành viên đã thêm
   */
  addProjectMember: (projectId: number, data: CreateProjectMemberDto) => {
    return apiClient.post<ProjectMemberDto>(`/api/projects/${projectId}/members`, data);
  },

  /**
   * Cập nhật thành viên dự án
   * @param projectId ID dự án
   * @param memberId ID thành viên
   * @param data Dữ liệu cập nhật
   * @returns Promise với phản hồi API chứa thành viên đã cập nhật
   */
  updateProjectMember: (projectId: number, memberId: number, data: UpdateProjectMemberDto) => {
    return apiClient.patch<ProjectMemberDto>(
      `/api/projects/${projectId}/members/${memberId}`,
      data
    );
  },

  /**
   * Xóa thành viên khỏi dự án
   * @param projectId ID dự án
   * @param memberId ID thành viên
   * @returns Promise với phản hồi API chứa kết quả xóa
   */
  removeProjectMember: (projectId: number, memberId: number) => {
    return apiClient.delete<boolean>(`/api/projects/${projectId}/members/${memberId}`);
  },
};
