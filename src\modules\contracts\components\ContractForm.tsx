import React from 'react';
import { useTranslation } from 'react-i18next';

import { Card, Typography } from '@/shared/components/common';

/**
 * Contract Form Component
 * Component form tạo/chỉnh sửa hợp đồng
 */
const ContractForm: React.FC = () => {
  const { t } = useTranslation(['common', 'contracts']);

  return (
    <Card className="p-6">
      <Typography variant="h2" className="mb-4">
        {t('contracts:create_contract')}
      </Typography>
      <div className="text-center py-8">
        <Typography variant="body1" className="text-muted-foreground">
          Form tạo hợp đồng đang được phát triển...
        </Typography>
      </div>
    </Card>
  );
};

export default ContractForm;
