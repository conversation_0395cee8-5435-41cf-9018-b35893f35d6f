import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional } from 'class-validator';
import { QueryDto } from '@/common/dto/query.dto';

/**
 * DTO for querying key result updates
 */
export class KeyResultUpdateQueryDto extends QueryDto {
  /**
   * Filter by key result ID
   * @example 1
   */
  @ApiProperty({
    description: 'Lọc theo ID kết quả chính',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  keyResultId?: number;

  /**
   * Filter by user who performed the update
   * @example 1
   */
  @ApiProperty({
    description: '<PERSON>ọc theo ID người cập nhật',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  updateBy?: number;
}
