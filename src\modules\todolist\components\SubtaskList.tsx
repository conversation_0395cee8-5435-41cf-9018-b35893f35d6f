import React from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import { Table, Badge, IconButton, Tooltip, Loading } from '@/shared/components/common';
import { formatDate } from '@/shared/utils/date';
import { NotificationUtil } from '@/shared/utils/notification';

import { useDeleteTask, useUpdateTaskStatus } from '../hooks/useTasks';
import { TaskDto, TaskStatus, TaskPriority } from '../types/task.types';

interface SubtaskListProps {
  subtasks: TaskDto[];
  isLoading: boolean;
  onRefresh: () => void;
}

/**
 * Component to display a list of subtasks
 */
const SubtaskList: React.FC<SubtaskListProps> = ({ subtasks, isLoading, onRefresh }) => {
  const { t } = useTranslation(['common', 'todolist']);
  const navigate = useNavigate();
  const { mutateAsync: deleteTask } = useDeleteTask();
  const { mutateAsync: updateTaskStatus } = useUpdateTaskStatus();

  // Handle delete task
  const handleDelete = async (id: number) => {
    try {
      await deleteTask(id);
      NotificationUtil.success({
        message: t('todolist:task.notifications.deleteSuccess', 'Task deleted successfully'),
      });
      onRefresh();
    } catch (error) {
      console.error('Error deleting task:', error);
      NotificationUtil.error({
        message: t('todolist:task.notifications.deleteError', 'Error deleting task'),
      });
    }
  };

  // Handle status change
  const handleStatusChange = async (id: number, status: TaskStatus) => {
    try {
      await updateTaskStatus({ id, data: { status } });
      NotificationUtil.success({
        message: t(
          'todolist:task.notifications.statusUpdateSuccess',
          'Task status updated successfully'
        ),
      });
      onRefresh();
    } catch (error) {
      console.error('Error updating task status:', error);
      NotificationUtil.error({
        message: t('todolist:task.notifications.statusUpdateError', 'Error updating task status'),
      });
    }
  };

  // Get status badge color
  const getStatusBadgeColor = (status: TaskStatus | null) => {
    switch (status) {
      case TaskStatus.PENDING:
        return 'bg-gray-100 text-gray-800';
      case TaskStatus.IN_PROGRESS:
        return 'bg-blue-100 text-blue-800';
      case TaskStatus.COMPLETED:
      case TaskStatus.APPROVED:
        return 'bg-green-100 text-green-800';
      case TaskStatus.REJECTED:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get priority badge color
  const getPriorityBadgeColor = (priority: TaskPriority | null) => {
    switch (priority) {
      case TaskPriority.LOW:
        return 'bg-green-100 text-green-800';
      case TaskPriority.MEDIUM:
        return 'bg-blue-100 text-blue-800';
      case TaskPriority.HIGH:
        return 'bg-orange-100 text-orange-800';
      case TaskPriority.URGENT:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get status text
  const getStatusText = (status: TaskStatus | null) => {
    switch (status) {
      case TaskStatus.PENDING:
        return t('todolist:task.status.todo', 'To Do');
      case TaskStatus.IN_PROGRESS:
        return t('todolist:task.status.inProgress', 'In Progress');
      case TaskStatus.COMPLETED:
      case TaskStatus.APPROVED:
        return t('todolist:task.status.done', 'Done');
      case TaskStatus.REJECTED:
        return t('todolist:task.status.cancelled', 'Cancelled');
      default:
        return '';
    }
  };

  // Get priority text
  const getPriorityText = (priority: TaskPriority | null) => {
    switch (priority) {
      case TaskPriority.LOW:
        return t('todolist:task.priority.low', 'Low');
      case TaskPriority.MEDIUM:
        return t('todolist:task.priority.medium', 'Medium');
      case TaskPriority.HIGH:
        return t('todolist:task.priority.high', 'High');
      case TaskPriority.URGENT:
        return t('todolist:task.priority.urgent', 'Urgent');
      default:
        return '';
    }
  };

  // Define columns for the table
  const columns = [
    {
      id: 'title',
      header: t('todolist:task.fields.title', 'Title'),
      cell: (row: TaskDto) => (
        <div
          className="font-medium cursor-pointer"
          onClick={() => navigate(`/todolist/tasks/${row.id}`)}
        >
          {row.title}
        </div>
      ),
    },
    {
      id: 'status',
      header: t('todolist:task.fields.status', 'Status'),
      cell: (row: TaskDto) => (
        <Badge className={getStatusBadgeColor(row.status)}>{getStatusText(row.status)}</Badge>
      ),
    },
    {
      id: 'priority',
      header: t('todolist:task.fields.priority', 'Priority'),
      cell: (row: TaskDto) => (
        <Badge className={getPriorityBadgeColor(row.priority)}>
          {getPriorityText(row.priority)}
        </Badge>
      ),
    },
    {
      id: 'createdAt',
      header: t('todolist:task.fields.createdAt', 'Created At'),
      cell: (row: TaskDto) => formatDate(row.createdAt || 0),
    },
    {
      id: 'actions',
      header: t('common:actions', 'Actions'),
      cell: (row: TaskDto) => (
        <div className="flex space-x-1">
          <Tooltip content={t('todolist:task.status.todo', 'To Do')}>
            <IconButton
              icon="clock"
              size="sm"
              variant={row.status === TaskStatus.PENDING ? 'primary' : 'outline'}
              onClick={() => handleStatusChange(row.id, TaskStatus.PENDING)}
            />
          </Tooltip>
          <Tooltip content={t('todolist:task.status.inProgress', 'In Progress')}>
            <IconButton
              icon="loading"
              size="sm"
              variant={row.status === TaskStatus.IN_PROGRESS ? 'primary' : 'outline'}
              onClick={() => handleStatusChange(row.id, TaskStatus.IN_PROGRESS)}
            />
          </Tooltip>
          <Tooltip content={t('todolist:task.status.done', 'Done')}>
            <IconButton
              icon="check"
              size="sm"
              variant={row.status === TaskStatus.COMPLETED ? 'primary' : 'outline'}
              onClick={() => handleStatusChange(row.id, TaskStatus.COMPLETED)}
            />
          </Tooltip>
          <Tooltip content={t('common:delete', 'Delete')}>
            <IconButton
              icon="trash"
              size="sm"
              variant="outline"
              className="text-red-500"
              onClick={() => handleDelete(row.id)}
            />
          </Tooltip>
        </div>
      ),
    },
  ];

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-32">
        <Loading size="md" />
      </div>
    );
  }

  if (subtasks.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        {t('todolist:task.subtasks.empty', 'No subtasks found')}
      </div>
    );
  }

  return <Table columns={columns} data={subtasks} />;
};

export default SubtaskList;
