import { Gantt, Task, ViewMode, StylingOption, DisplayOption } from 'gantt-task-react';
import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import 'gantt-task-react/dist/index.css';

import { Typography } from '@/shared/components/common';
import { formatDate } from '@/shared/utils/date';

import { TaskDto } from '../../types/task.types';

interface EmployeeGanttChartProps {
  tasks: TaskDto[];
  employees: { id: number; name: string }[];
  startDate?: Date;
  endDate?: Date;
  height?: number;
  isLoading?: boolean;
}

/**
 * Biểu đồ Gantt hiển thị thời gian hoạt động và thời gian trống của nhân viên
 */
const EmployeeGanttChart: React.FC<EmployeeGanttChartProps> = ({
  tasks,
  employees,
  startDate,
  endDate,
  height = 500,
  isLoading = false,
}) => {
  const { t } = useTranslation(['todolist']);

  // Chuẩn bị dữ liệu cho biểu đồ Gantt
  const ganttTasks = useMemo(() => {
    if (!tasks || tasks.length === 0 || !employees || employees.length === 0) {return [];}

    // Xác định khoảng thời gian
    const start = startDate 
      ? startDate 
      : tasks.length > 0 
        ? new Date(Math.min(...tasks.filter(t => t.createdAt).map(t => t.createdAt || Date.now()))) 
        : new Date();
    
    const end = endDate 
      ? endDate 
      : tasks.length > 0 
        ? new Date(Math.max(...tasks.filter(t => t.completedAt).map(t => t.completedAt || Date.now()))) 
        : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // Mặc định 1 tuần

    // Tạo task cha cho mỗi nhân viên
    const employeeTasks: Task[] = employees.map((employee, index) => ({
      id: `employee-${employee.id}`,
      name: employee.name,
      start: new Date(start),
      end: new Date(end),
      progress: 0,
      type: 'project',
      hideChildren: false,
      displayOrder: index,
      styles: { progressColor: '#3b82f6', progressSelectedColor: '#2563eb' },
    }));

    // Tạo task con cho mỗi công việc của nhân viên
    const employeeWorkTasks: Task[] = [];

    for (const task of tasks) {
      if (!task.assigneeId) {continue;}

      const employee = employees.find(e => e.id === task.assigneeId);
      if (!employee) {continue;}

      const taskStart = task.createdAt ? new Date(task.createdAt) : new Date(start);
      const taskEnd = task.completedAt ? new Date(task.completedAt) : new Date(taskStart.getTime() + 24 * 60 * 60 * 1000); // Mặc định 1 ngày
      
      // Tính toán tiến độ
      let progress = 0;
      if (task.status === 'COMPLETED' || task.status === 'APPROVED') {
        progress = 100;
      } else if (task.status === 'IN_PROGRESS') {
        // Tính toán tiến độ dựa trên thời gian đã trôi qua
        const totalDuration = taskEnd.getTime() - taskStart.getTime();
        const elapsedDuration = Date.now() - taskStart.getTime();
        progress = Math.min(Math.max(Math.round((elapsedDuration / totalDuration) * 100), 0), 99);
      }

      employeeWorkTasks.push({
        id: `task-${task.id}`,
        name: task.title,
        start: taskStart,
        end: taskEnd,
        progress,
        type: 'task',
        project: `employee-${task.assigneeId}`,
        displayOrder: employeeWorkTasks.length,
        styles: {
          progressColor: progress === 100 ? '#22c55e' : '#f59e0b',
          progressSelectedColor: progress === 100 ? '#16a34a' : '#d97706',
          backgroundColor: progress === 100 ? '#dcfce7' : '#fff7ed',
          backgroundSelectedColor: progress === 100 ? '#bbf7d0' : '#ffedd5',
        },
      });
    }

    // Tìm khoảng thời gian trống giữa các công việc
    for (const employee of employees) {
      const employeeTasks = tasks
        .filter(task => task.assigneeId === employee.id)
        .sort((a, b) => {
          const aStart = a.createdAt || 0;
          const bStart = b.createdAt || 0;
          return aStart - bStart;
        });

      // Nếu không có công việc nào, thêm một khoảng trống từ start đến end
      if (employeeTasks.length === 0) {
        employeeWorkTasks.push({
          id: `free-${employee.id}-0`,
          name: t('todolist:statistics.charts.freeTime', 'Thời gian trống'),
          start: new Date(start),
          end: new Date(end),
          progress: 0,
          type: 'task',
          project: `employee-${employee.id}`,
          displayOrder: employeeWorkTasks.length,
          styles: {
            progressColor: '#94a3b8',
            progressSelectedColor: '#64748b',
            backgroundColor: '#f1f5f9',
            backgroundSelectedColor: '#e2e8f0',
          },
        });
        continue;
      }

      // Thêm khoảng trống từ start đến công việc đầu tiên
      const firstTask = employeeTasks[0];
      const firstTaskStart = firstTask.createdAt ? new Date(firstTask.createdAt) : new Date(start);
      
      if (firstTaskStart.getTime() > start.getTime()) {
        employeeWorkTasks.push({
          id: `free-${employee.id}-start`,
          name: t('todolist:statistics.charts.freeTime', 'Thời gian trống'),
          start: new Date(start),
          end: new Date(firstTaskStart),
          progress: 0,
          type: 'task',
          project: `employee-${employee.id}`,
          displayOrder: employeeWorkTasks.length,
          styles: {
            progressColor: '#94a3b8',
            progressSelectedColor: '#64748b',
            backgroundColor: '#f1f5f9',
            backgroundSelectedColor: '#e2e8f0',
          },
        });
      }

      // Thêm khoảng trống giữa các công việc
      for (let i = 0; i < employeeTasks.length - 1; i++) {
        const currentTask = employeeTasks[i];
        const nextTask = employeeTasks[i + 1];
        
        const currentTaskEnd = currentTask.completedAt 
          ? new Date(currentTask.completedAt) 
          : currentTask.createdAt 
            ? new Date(currentTask.createdAt.getTime() + 24 * 60 * 60 * 1000) 
            : new Date(start);
        
        const nextTaskStart = nextTask.createdAt 
          ? new Date(nextTask.createdAt) 
          : new Date(end);

        // Nếu có khoảng trống giữa hai công việc
        if (nextTaskStart.getTime() > currentTaskEnd.getTime()) {
          employeeWorkTasks.push({
            id: `free-${employee.id}-${i}`,
            name: t('todolist:statistics.charts.freeTime', 'Thời gian trống'),
            start: new Date(currentTaskEnd),
            end: new Date(nextTaskStart),
            progress: 0,
            type: 'task',
            project: `employee-${employee.id}`,
            displayOrder: employeeWorkTasks.length,
            styles: {
              progressColor: '#94a3b8',
              progressSelectedColor: '#64748b',
              backgroundColor: '#f1f5f9',
              backgroundSelectedColor: '#e2e8f0',
            },
          });
        }
      }

      // Thêm khoảng trống từ công việc cuối cùng đến end
      const lastTask = employeeTasks[employeeTasks.length - 1];
      const lastTaskEnd = lastTask.completedAt 
        ? new Date(lastTask.completedAt) 
        : lastTask.createdAt 
          ? new Date(lastTask.createdAt.getTime() + 24 * 60 * 60 * 1000) 
          : new Date(start);
      
      if (lastTaskEnd.getTime() < end.getTime()) {
        employeeWorkTasks.push({
          id: `free-${employee.id}-end`,
          name: t('todolist:statistics.charts.freeTime', 'Thời gian trống'),
          start: new Date(lastTaskEnd),
          end: new Date(end),
          progress: 0,
          type: 'task',
          project: `employee-${employee.id}`,
          displayOrder: employeeWorkTasks.length,
          styles: {
            progressColor: '#94a3b8',
            progressSelectedColor: '#64748b',
            backgroundColor: '#f1f5f9',
            backgroundSelectedColor: '#e2e8f0',
          },
        });
      }
    }

    return [...employeeTasks, ...employeeWorkTasks];
  }, [tasks, employees, startDate, endDate, t]);

  // Tùy chỉnh hiển thị
  const ganttOptions: DisplayOption = {
    viewMode: ViewMode.Day,
    viewDate: startDate || new Date(),
    preStepsCount: 1,
    locale: 'vi-VN',
  };

  // Tùy chỉnh style
  const ganttStyles: StylingOption = {
    headerHeight: 50,
    rowHeight: 50,
    barCornerRadius: 5,
    fontFamily: 'inherit',
    fontSize: '14px',
    arrowColor: '#64748b',
    arrowIndent: 10,
    todayColor: '#e2e8f0',
    rtl: false,
  };

  // Hiển thị tooltip khi hover
  const handleTaskTooltip = (task: Task) => {
    if (task.type === 'project') {
      return (
        <div className="p-2 bg-white shadow-md rounded-md border border-gray-200">
          <div className="font-semibold">{task.name}</div>
        </div>
      );
    }

    const originalTask = tasks.find(t => `task-${t.id}` === task.id);
    if (originalTask) {
      return (
        <div className="p-2 bg-white shadow-md rounded-md border border-gray-200">
          <div className="font-semibold">{task.name}</div>
          <div className="text-sm text-gray-500">
            {t('todolist:statistics.charts.startDate', 'Bắt đầu')}: {formatDate(task.start.getTime())}
          </div>
          <div className="text-sm text-gray-500">
            {t('todolist:statistics.charts.endDate', 'Kết thúc')}: {formatDate(task.end.getTime())}
          </div>
          <div className="text-sm text-gray-500">
            {t('todolist:statistics.charts.progress', 'Tiến độ')}: {task.progress}%
          </div>
        </div>
      );
    }

    // Hiển thị thông tin khoảng thời gian trống
    if (task.id.startsWith('free-')) {
      return (
        <div className="p-2 bg-white shadow-md rounded-md border border-gray-200">
          <div className="font-semibold">{task.name}</div>
          <div className="text-sm text-gray-500">
            {t('todolist:statistics.charts.startDate', 'Bắt đầu')}: {formatDate(task.start.getTime())}
          </div>
          <div className="text-sm text-gray-500">
            {t('todolist:statistics.charts.endDate', 'Kết thúc')}: {formatDate(task.end.getTime())}
          </div>
        </div>
      );
    }

    return null;
  };

  // Nếu đang tải hoặc không có dữ liệu, hiển thị thông báo
  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <Typography variant="body2" color="muted">
          {t('common:loading', 'Đang tải...')}
        </Typography>
      </div>
    );
  }

  if (ganttTasks.length === 0) {
    return (
      <div className="h-full flex items-center justify-center">
        <Typography variant="body2" color="muted">
          {t('todolist:statistics.noData', 'Không có dữ liệu')}
        </Typography>
      </div>
    );
  }

  return (
    <div className="gantt-chart-container" style={{ height }}>
      <Gantt
        tasks={ganttTasks}
        viewMode={ganttOptions.viewMode}
        viewDate={ganttOptions.viewDate}
        preStepsCount={ganttOptions.preStepsCount}
        locale={ganttOptions.locale}
        listCellWidth="auto"
        columnWidth={60}
        ganttHeight={height}
        headerHeight={ganttStyles.headerHeight}
        rowHeight={ganttStyles.rowHeight}
        barCornerRadius={ganttStyles.barCornerRadius}
        barFill={100}
        handleWidth={10}
        TooltipContent={handleTaskTooltip}
        arrowColor={ganttStyles.arrowColor}
        arrowIndent={ganttStyles.arrowIndent}
        todayColor={ganttStyles.todayColor}
        rtl={ganttStyles.rtl}
      />
    </div>
  );
};

export default EmployeeGanttChart;
