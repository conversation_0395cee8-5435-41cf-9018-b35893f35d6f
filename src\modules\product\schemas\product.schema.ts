import { z } from 'zod';

import { ProductStatus, ProductType } from '../types/product.types';

/**
 * Schema validation cho sản phẩm
 */
export const productSchema = z.object({
  name: z
    .string()
    .min(2, 'Tên sản phẩm phải có ít nhất 2 ký tự')
    .max(100, 'Tên sản phẩm không được vượt quá 100 ký tự'),
  description: z
    .string()
    .max(1000, '<PERSON>ô tả không được vượt quá 1000 ký tự')
    .optional()
    .or(z.literal('')),
  price: z
    .number({ invalid_type_error: 'Giá phải là số' })
    .min(0, 'Gi<PERSON> không được âm')
    .or(z.string().regex(/^\d+(\.\d{1,2})?$/, 'Gi<PERSON> không hợp lệ').transform(Number)),
  originalPrice: z
    .number({ invalid_type_error: '<PERSON><PERSON><PERSON> gố<PERSON> phải là số' })
    .min(0, '<PERSON><PERSON><PERSON> gố<PERSON> không được âm')
    .optional()
    .or(z.string().regex(/^\d+(\.\d{1,2})?$/, 'Giá gốc không hợp lệ').transform(Number))
    .or(z.literal('')),
  images: z.array(z.string().url('URL hình ảnh không hợp lệ')).optional().default([]),
  status: z.nativeEnum(ProductStatus).default(ProductStatus.DRAFT),
  type: z.nativeEnum(ProductType, {
    errorMap: () => ({ message: 'Loại sản phẩm là bắt buộc' }),
  }),
  categoryId: z.string().optional().or(z.literal('')),
  tags: z.array(z.string()).optional().default([]),
  stock: z
    .number({ invalid_type_error: 'Số lượng phải là số' })
    .int('Số lượng phải là số nguyên')
    .min(0, 'Số lượng không được âm')
    .optional()
    .or(z.string().regex(/^\d+$/, 'Số lượng phải là số nguyên').transform(Number))
    .or(z.literal('')),
});

/**
 * Schema validation cho cập nhật sản phẩm
 */
export const updateProductSchema = productSchema.partial().extend({
  id: z.string().min(1, 'ID sản phẩm là bắt buộc'),
});

/**
 * Type cho form tạo sản phẩm
 */
export type ProductFormValues = z.infer<typeof productSchema>;

/**
 * Type cho form cập nhật sản phẩm
 */
export type UpdateProductFormValues = z.infer<typeof updateProductSchema>;
