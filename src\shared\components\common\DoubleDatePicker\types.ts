export interface DoubleDatePickerProps {
  /**
   * Gi<PERSON> trị đã chọn [startDate, endDate]
   */
  value?: [Date | null, Date | null];

  /**
   * Callback khi giá trị thay đổi
   */
  onChange?: (dates: [Date | null, Date | null]) => void;

  /**
   * Icon hiển thị để mở dropdown
   */
  triggerIcon?: React.ReactNode;

  /**
   * <PERSON><PERSON>ch thước của icon trigger
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Disabled component
   */
  disabled?: boolean;

  /**
   * Custom className
   */
  className?: string;

  /**
   * Ẩn border xung quanh icon
   */
  noBorder?: boolean;

  /**
   * Placeholder text
   */
  placeholder?: string;
}
