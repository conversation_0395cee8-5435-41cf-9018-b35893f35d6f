import { Suspense } from 'react';
import { RouteObject } from 'react-router-dom';
import { Loading } from '@/shared/components';
import MainLayout from '@/shared/layouts/MainLayout';
import CalendarPage from './pages/CalendarPage';

/**
 * Calendar module routes
 */
const calendarRoutes: RouteObject[] = [
  {
    path: '/calendar',
    element: (
      <MainLayout title="Calendar">
        <Suspense fallback={<Loading />}>
          <CalendarPage />
        </Suspense>
      </MainLayout>
    ),
  },
];

export default calendarRoutes;
