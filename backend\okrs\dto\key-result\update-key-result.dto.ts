import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional, IsString, Max, Min } from 'class-validator';
import { CheckInFrequency } from '../../enum/check-in-frequency.enum';
import { KeyResultStatus } from '../../enum/key-result-status.enum';

/**
 * DTO for updating a key result
 */
export class UpdateKeyResultDto {
  /**
   * Title of the key result
   * @example "Tăng số lượng khách hàng mới lên 1200"
   */
  @ApiProperty({
    description: 'Tiêu đề kết quả chính',
    example: 'Tăng số lượng khách hàng mới lên 1200',
    required: false,
  })
  @IsString()
  @IsOptional()
  title?: string;

  /**
   * Detailed description of the key result
   * @example "Tăng số lượng khách hàng mới thông qua các chiến dịch marketing trên mạng xã hội và email"
   */
  @ApiProperty({
    description: '<PERSON><PERSON> tả chi tiết kết quả chính',
    example: 'Tăng số lượng khách hàng mới thông qua các chiến dịch marketing trên mạng xã hội và email',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  /**
   * Target value of the key result
   * @example 1200
   */
  @ApiProperty({
    description: 'Giá trị mục tiêu',
    example: 1200,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  targetValue?: number;

  /**
   * Current value of the key result
   * @example 800
   */
  @ApiProperty({
    description: 'Giá trị hiện tại',
    example: 800,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  currentValue?: number;

  /**
   * Initial value of the key result
   * @example 500
   */
  @ApiProperty({
    description: 'Giá trị ban đầu',
    example: 500,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  startValue?: number;

  /**
   * Unit of measurement for the key result
   * @example "khách hàng"
   */
  @ApiProperty({
    description: 'Đơn vị đo lường',
    example: 'khách hàng',
    required: false,
  })
  @IsString()
  @IsOptional()
  unit?: string;

  /**
   * Display format for the key result value
   * @example "number"
   */
  @ApiProperty({
    description: 'Định dạng hiển thị',
    example: 'number',
    required: false,
  })
  @IsString()
  @IsOptional()
  format?: string;

  /**
   * Status of the key result
   * @example "ACTIVE"
   */
  @ApiProperty({
    description: 'Trạng thái kết quả chính',
    enum: KeyResultStatus,
    example: KeyResultStatus.ACTIVE,
    required: false,
  })
  @IsEnum(KeyResultStatus)
  @IsOptional()
  status?: KeyResultStatus;

  /**
   * Method used to measure the key result
   * @example "Số lượng đăng ký mới"
   */
  @ApiProperty({
    description: 'Phương pháp đo lường',
    example: 'Số lượng đăng ký mới',
    required: false,
  })
  @IsString()
  @IsOptional()
  measurementMethod?: string;

  /**
   * Weight of the key result within its objective (0-100)
   * @example 30
   */
  @ApiProperty({
    description: 'Trọng số trong mục tiêu (0-100)',
    example: 30,
    required: false,
  })
  @IsNumber()
  @Min(0)
  @Max(100)
  @IsOptional()
  weight?: number;

  /**
   * Frequency of key result updates
   * @example "WEEKLY"
   */
  @ApiProperty({
    description: 'Tần suất cập nhật',
    enum: CheckInFrequency,
    example: CheckInFrequency.WEEKLY,
    required: false,
  })
  @IsEnum(CheckInFrequency)
  @IsOptional()
  checkInFrequency?: CheckInFrequency;
}
