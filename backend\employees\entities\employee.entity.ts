import { Entity, PrimaryGeneratedColumn, <PERSON>umn, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, ManyToOne } from 'typeorm';
import { EmployeeStatus } from '../enum/employee-status.enum';
import { EmploymentType } from '../enum/employment-type.enum';
import { MaritalStatus } from '../enum/marital-status.enum';

/**
 * Entity đại diện cho nhân viên trong hệ thống
 */
@Entity('employees')
export class Employee {
  /**
   * Định danh duy nhất cho nhân viên
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * ID của người dùng liên kết với nhân viên này
   * Mỗi nhân viên phải có một tài khoản người dùng tương ứng
   */
  @Column({ name: 'user_id', type: 'integer', nullable: false })
  userId: number;

  /**
   * <PERSON><PERSON> nhân viên (định danh duy nhất trong ngữ cảnh HR)
   * Th<PERSON>ờ<PERSON> được sử dụng trong các tài liệu chính thức và giao tiếp nội bộ
   */
  @Column({ name: 'employee_code', type: 'varchar', length: 50, nullable: false })
  employeeCode: string;

  /**
   * ID của phòng ban mà nhân viên thuộc về
   * Có thể null nếu nhân viên chưa được phân công vào phòng ban cụ thể
   */
  @Column({ name: 'department_id', type: 'integer', nullable: true })
  departmentId: number | null;

  /**
   * Chức danh công việc của nhân viên
   * Ví dụ: Kỹ sư phần mềm, Quản lý dự án, Giám đốc điều hành, v.v.
   */
  @Column({ name: 'job_title', type: 'varchar', length: 255, nullable: true })
  jobTitle: string | null;

  /**
   * Cấp bậc công việc của nhân viên
   * Ví dụ: Junior, Middle, Senior, Lead, v.v.
   */
  @Column({ name: 'job_level', type: 'varchar', length: 50, nullable: true })
  jobLevel: string | null;

  /**
   * ID của người quản lý trực tiếp của nhân viên
   * Tham chiếu đến một nhân viên khác trong hệ thống
   */
  @Column({ name: 'manager_id', type: 'integer', nullable: true })
  managerId: number | null;

  /**
   * Loại hình làm việc của nhân viên
   * Các giá trị có thể: full_time (toàn thời gian), part_time (bán thời gian),
   * contract (hợp đồng), temporary (tạm thời), intern (thực tập sinh), freelance (tự do)
   */
  @Column({ name: 'employment_type', type: 'enum', enum: EmploymentType, enumName: 'employment_type_enum', nullable: true })
  employmentType: EmploymentType | null;

  /**
   * Trạng thái của nhân viên
   * Các giá trị có thể: active (đang làm việc), inactive (không hoạt động),
   * on_leave (nghỉ phép), terminated (đã nghỉ việc), probation (thử việc), suspended (tạm ngưng)
   */
  @Column({ name: 'status', type: 'enum', enum: EmployeeStatus, enumName: 'employee_status_enum', nullable: false, default: EmployeeStatus.ACTIVE })
  status: EmployeeStatus;

  /**
   * Ngày nhân viên bắt đầu làm việc
   * Được sử dụng để tính thâm niên và các quyền lợi dựa trên thời gian làm việc
   */
  @Column({ name: 'hire_date', type: 'date', nullable: true })
  hireDate: Date | null;

  /**
   * Ngày nhân viên nghỉ việc (nếu có)
   * Chỉ được điền khi nhân viên đã nghỉ việc (status = terminated)
   */
  @Column({ name: 'termination_date', type: 'date', nullable: true })
  terminationDate: Date | null;

  /**
   * Lý do nghỉ việc (nếu có)
   * Ghi chú về lý do nhân viên rời khỏi công ty
   */
  @Column({ name: 'termination_reason', type: 'varchar', length: 500, nullable: true })
  terminationReason: string | null;

  /**
   * Ngày kết thúc thời gian thử việc của nhân viên
   * Được sử dụng để theo dõi quá trình thử việc và chuyển đổi sang nhân viên chính thức
   */
  @Column({ name: 'probation_end_date', type: 'date', nullable: true })
  probationEndDate: Date | null;

  /**
   * Tình trạng hôn nhân của nhân viên
   * Các giá trị có thể: single (độc thân), married (đã kết hôn),
   * divorced (đã ly hôn), widowed (góa), separated (ly thân)
   */
  @Column({ name: 'marital_status', type: 'enum', enum: MaritalStatus, enumName: 'marital_status_enum', nullable: true })
  maritalStatus: MaritalStatus | null;

  /**
   * Số người phụ thuộc
   * Được sử dụng cho mục đích tính thuế và phúc lợi
   */
  @Column({ name: 'number_of_dependents', type: 'integer', nullable: true })
  numberOfDependents: number | null;

  /**
   * Tên người liên hệ khẩn cấp
   * Người được liên hệ trong trường hợp khẩn cấp liên quan đến nhân viên
   */
  @Column({ name: 'emergency_contact_name', type: 'varchar', length: 255, nullable: true })
  emergencyContactName: string | null;

  /**
   * Số điện thoại của người liên hệ khẩn cấp
   * Dùng để liên lạc trong trường hợp khẩn cấp
   */
  @Column({ name: 'emergency_contact_phone', type: 'varchar', length: 20, nullable: true })
  emergencyContactPhone: string | null;

  /**
   * Mối quan hệ với người liên hệ khẩn cấp
   * Ví dụ: vợ/chồng, cha/mẹ, anh/chị/em, v.v.
   */
  @Column({ name: 'emergency_contact_relationship', type: 'varchar', length: 100, nullable: true })
  emergencyContactRelationship: string | null;

  /**
   * Ghi chú về nhân viên
   * Thông tin bổ sung không phù hợp với các trường khác
   */
  @Column({ name: 'notes', type: 'text', nullable: true })
  notes: string | null;

  /**
   * Thời điểm tạo bản ghi (tính bằng mili giây)
   * Lưu trữ timestamp Unix khi bản ghi được tạo
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number | null;

  /**
   * Thời điểm cập nhật bản ghi lần cuối (tính bằng mili giây)
   * Lưu trữ timestamp Unix khi bản ghi được cập nhật
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true })
  updatedAt: number | null;

  /**
   * ID của người dùng đã tạo bản ghi này
   * Dùng để theo dõi người thực hiện thao tác tạo
   */
  @Column({ name: 'created_by', type: 'integer', nullable: true })
  createdBy: number | null;

  /**
   * ID của người dùng đã cập nhật bản ghi này lần cuối
   * Dùng để theo dõi người thực hiện thao tác cập nhật
   */
  @Column({ name: 'updated_by', type: 'integer', nullable: true })
  updatedBy: number | null;

  /**
   * ID của công ty/tổ chức sở hữu bản ghi này
   * Dùng cho tính năng multi-tenant, phân tách dữ liệu giữa các công ty
   */
  @Column({ name: 'tenant_id', type: 'integer', nullable: true })
  tenantId: number | null;
}
