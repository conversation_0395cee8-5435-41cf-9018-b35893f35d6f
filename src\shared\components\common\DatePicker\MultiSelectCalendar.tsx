import React, { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@/shared/contexts/theme';
import CalendarHeader from './CalendarHeader';
import CalendarGrid from './CalendarGrid';
import { useCalendar, useCalendarKeyboard, useCalendarTouch, useCalendarResponsive } from './hooks';
import { isSameDayFn } from './utils';

/**
 * Props cho MultiSelectCalendar component
 */
export interface MultiSelectCalendarProps {
  /**
   * Các ngày đã chọn
   */
  selectedDates?: Date[];

  /**
   * Callback khi danh sách ngày thay đổi
   */
  onSelectDates?: (dates: Date[]) => void;

  /**
   * Tháng hiển thị
   */
  month?: Date;

  /**
   * Callback khi tháng thay đổi
   */
  onMonthChange?: (date: Date) => void;

  /**
   * <PERSON><PERSON><PERSON> ngày không được phép chọn
   */
  disabledDates?: Date[] | ((date: Date) => boolean);

  /**
   * <PERSON><PERSON>y tối thiểu được phép chọn
   */
  minDate?: Date;

  /**
   * Ngày tối đa được phép chọn
   */
  maxDate?: Date;

  /**
   * Số ngày tối đa được phép chọn
   */
  maxSelections?: number;

  /**
   * Hiển thị ngày hiện tại
   */
  showToday?: boolean;

  /**
   * Hiển thị tuần
   */
  showWeekNumbers?: boolean;

  /**
   * Ngày đầu tiên của tuần (0: Chủ nhật, 1: Thứ 2, ...)
   */
  firstDayOfWeek?: 0 | 1 | 2 | 3 | 4 | 5 | 6;

  /**
   * Tên các ngày trong tuần
   */
  weekDayNames?: string[];

  /**
   * Tên các tháng
   */
  monthNames?: string[];

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Hiển thị button today
   */
  showTodayButton?: boolean;

  /**
   * Text cho button today
   */
  todayButtonText?: string;

  /**
   * Hiển thị button clear all
   */
  showClearButton?: boolean;

  /**
   * Text cho button clear all
   */
  clearButtonText?: string;

  /**
   * Hiển thị số ngày đã chọn
   */
  showSelectedCount?: boolean;
}

/**
 * Calendar component hỗ trợ chọn nhiều ngày
 */
const MultiSelectCalendar: React.FC<MultiSelectCalendarProps> = ({
  selectedDates = [],
  onSelectDates,
  month: propMonth,
  onMonthChange,
  disabledDates,
  minDate,
  maxDate,
  maxSelections,
  showToday = true,
  showWeekNumbers = false,
  firstDayOfWeek = 1,
  weekDayNames,
  monthNames,
  className = '',
  showTodayButton = false,
  todayButtonText,
  showClearButton = false,
  clearButtonText,
  showSelectedCount = false,
}) => {
  const { t } = useTranslation();
  useTheme();

  // Responsive design hook
  const responsive = useCalendarResponsive();

  // Calendar logic hook
  const calendar = useCalendar({
    initialDate: propMonth,
    firstDayOfWeek,
    onMonthChange,
  });

  // Xử lý chọn ngày
  const handleSelectDate = useCallback(
    (date: Date) => {
      const isAlreadySelected = selectedDates.some(selectedDate => 
        isSameDayFn(selectedDate, date)
      );

      let newSelectedDates: Date[];

      if (isAlreadySelected) {
        // Bỏ chọn ngày
        newSelectedDates = selectedDates.filter(selectedDate => 
          !isSameDayFn(selectedDate, date)
        );
      } else {
        // Thêm ngày mới
        if (maxSelections && selectedDates.length >= maxSelections) {
          // Nếu đã đạt giới hạn, thay thế ngày cuối cùng
          newSelectedDates = [...selectedDates.slice(0, -1), date];
        } else {
          newSelectedDates = [...selectedDates, date];
        }
      }

      // Sắp xếp theo thứ tự thời gian
      newSelectedDates.sort((a, b) => a.getTime() - b.getTime());

      onSelectDates?.(newSelectedDates);
    },
    [selectedDates, onSelectDates, maxSelections]
  );

  // Keyboard navigation hook
  const keyboard = useCalendarKeyboard({
    calendar,
    onSelectDate: handleSelectDate,
    disabled: false,
    autoFocus: false,
  });

  // Touch gestures hook
  const touch = useCalendarTouch({
    calendar,
    disabled: false,
    enableSwipe: responsive.enableSwipe,
  });

  // Xử lý khi click vào button Today
  const handleTodayClick = useCallback(() => {
    const today = new Date();
    handleSelectDate(today);
    calendar.handleMonthChange(today);
    calendar.setFocusedDate(today);
  }, [handleSelectDate, calendar]);

  // Xử lý khi click vào button Clear All
  const handleClearAll = useCallback(() => {
    onSelectDates?.([]);
  }, [onSelectDates]);

  // Determine final showWeekNumbers based on responsive design
  const finalShowWeekNumbers = showWeekNumbers && responsive.showWeekNumbers;

  // Base classes with responsive adjustments
  const baseClasses = `
    bg-white dark:bg-dark-light rounded-lg shadow-lg p-2
    ${responsive.compactMode ? 'w-72' : 'w-80'}
    ${responsive.touchOptimized ? 'touch-manipulation' : ''}
  `.trim();

  // Combine all classes
  const calendarClasses = [baseClasses, className].join(' ');





  return (
    <div
      className={calendarClasses}
      ref={keyboard.calendarRef}
      tabIndex={0}
      onKeyDown={keyboard.handleKeyDown}
      onTouchStart={touch.handleTouchStart}
      onTouchMove={touch.handleTouchMove}
      onTouchEnd={touch.handleTouchEnd}
      role="grid"
      aria-label={t('datepicker.multiSelectCalendar', 'Multi-select Calendar')}
      aria-multiselectable="true"
      style={{
        minHeight: responsive.minTouchTarget * 7,
      }}
    >
      <CalendarHeader 
        month={calendar.month} 
        onMonthChange={calendar.handleMonthChange} 
        monthNames={monthNames} 
      />

      <CalendarGrid
        month={calendar.month}
        selectedDate={null} // Không sử dụng selectedDate cho multi-select
        onSelectDate={handleSelectDate}
        disabledDates={disabledDates}
        minDate={minDate}
        maxDate={maxDate}
        showToday={showToday}
        showWeekNumbers={finalShowWeekNumbers}
        firstDayOfWeek={firstDayOfWeek}
        weekDayNames={weekDayNames}
        rangeMode={false}
        startDate={null}
        endDate={null}
        focusedDate={calendar.focusedDate}
        // Custom props for multi-select - commented out as these props don't exist in CalendarGrid
        // multiSelect={true}
        // selectedDates={selectedDates}
        // isDateSelected={isDateSelected}
      />

      {/* Action buttons */}
      <div className="mt-2 flex justify-between items-center">
        <div className="flex gap-2">
          {showTodayButton && (
            <button
              type="button"
              className={`
                px-3 py-1 text-sm rounded transition-colors
                ${responsive.touchOptimized ? 'min-h-[44px] px-4' : ''}
                bg-gray-100 hover:bg-gray-200 
                dark:bg-gray-800 dark:hover:bg-gray-700
              `.trim()}
              onClick={handleTodayClick}
              style={{
                minHeight: responsive.touchOptimized ? responsive.minTouchTarget : undefined,
              }}
            >
              {todayButtonText || t('datepicker.today', 'Today')}
            </button>
          )}

          {showClearButton && selectedDates.length > 0 && (
            <button
              type="button"
              className={`
                px-3 py-1 text-sm rounded transition-colors
                ${responsive.touchOptimized ? 'min-h-[44px] px-4' : ''}
                bg-red-100 hover:bg-red-200 text-red-700
                dark:bg-red-900 dark:hover:bg-red-800 dark:text-red-300
              `.trim()}
              onClick={handleClearAll}
              style={{
                minHeight: responsive.touchOptimized ? responsive.minTouchTarget : undefined,
              }}
            >
              {clearButtonText || t('datepicker.clearAll', 'Clear All')}
            </button>
          )}
        </div>

        {/* Selected count */}
        {showSelectedCount && (
          <div className="text-sm text-gray-600 dark:text-gray-400">
            {t('datepicker.selectedCount', '{{count}} selected', { count: selectedDates.length })}
            {maxSelections && ` / ${maxSelections}`}
          </div>
        )}
      </div>

      {/* Swipe indicator for mobile */}
      {touch.isSwipeInProgress && responsive.isMobile && (
        <div className="absolute inset-0 pointer-events-none flex items-center justify-center">
          <div className="bg-black bg-opacity-50 text-white px-3 py-1 rounded text-sm">
            {touch.swipeDirection === 'left' ? '→' : '←'}
          </div>
        </div>
      )}
    </div>
  );
};

export default MultiSelectCalendar;
