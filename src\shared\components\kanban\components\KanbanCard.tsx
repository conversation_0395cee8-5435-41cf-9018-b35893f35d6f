import { formatDistanceToNow } from 'date-fns';
import { vi, enUS, zhCN } from 'date-fns/locale';
import React, { memo } from 'react';
import { useTranslation } from 'react-i18next';

import { Badge, Icon, Tooltip, Typography } from '@/shared/components/common';

import { useCardDragDrop } from '../hooks/useKanbanDragDrop';
import {
  KanbanCard as KanbanCardType,
  KanbanVariant,
  KanbanFunctionality,
  KanbanTheme,
} from '../types/kanban.types';

export interface KanbanCardProps {
  /**
   * Dữ liệu thẻ
   */
  card: KanbanCardType;

  /**
   * ID của cột chứa thẻ
   */
  columnId: string;

  /**
   * Vị trí của thẻ trong cột
   */
  index: number;

  /**
   * Callback khi kéo thả thẻ
   */
  onCardMove?: (
    cardId: string,
    sourceColumnId: string,
    targetColumnId: string,
    newIndex: number
  ) => void;

  /**
   * Callback khi click vào thẻ
   */
  onClick?: (card: KanbanCardType) => void;

  /**
   * Callback khi sửa thẻ
   */
  onEdit?: (card: KanbanCardType) => void;

  /**
   * Callback khi xóa thẻ
   */
  onDelete?: (card: KanbanCardType) => void;

  /**
   * Có cho phép kéo thả thẻ không
   * @default true
   */
  allowDrag?: boolean;

  /**
   * Biến thể giao diện của Kanban Card
   * @default KanbanVariant.DEFAULT
   */
  variant?: KanbanVariant;

  /**
   * Biến thể chức năng của Kanban Card
   * @default KanbanFunctionality.INTERACTIVE
   */
  functionality?: KanbanFunctionality;

  /**
   * Biến thể theme của Kanban Card
   * @default KanbanTheme.DEFAULT
   */
  theme?: KanbanTheme;

  /**
   * Custom class name
   */
  className?: string;
}

/**
 * Component hiển thị một thẻ trong Kanban Board
 */
const KanbanCard: React.FC<KanbanCardProps> = ({
  card,
  columnId,
  index,
  onCardMove,
  onClick,
  // onEdit, // Sẽ được sử dụng trong tương lai
  // onDelete, // Sẽ được sử dụng trong tương lai
  allowDrag = true,
  variant = KanbanVariant.DEFAULT,
  functionality = KanbanFunctionality.INTERACTIVE,
  theme = KanbanTheme.DEFAULT,
  className = '',
}) => {
  const { t, i18n } = useTranslation();

  // Xử lý kéo thả
  const handleCardMove = (result: {
    cardId: string;
    sourceColumnId: string;
    targetColumnId: string;
    newIndex: number;
  }) => {
    if (onCardMove) {
      onCardMove(result.cardId, result.sourceColumnId, result.targetColumnId, result.newIndex);
    }
  };

  const { ref, isDragging, isOver } = useCardDragDrop(
    card,
    columnId,
    index,
    handleCardMove,
    allowDrag
  );

  // Xác định màu cho mức độ ưu tiên
  const getPriorityColor = (): string => {
    const priorityColors: Record<string, string> = {
      low: 'blue',
      medium: 'green',
      high: 'orange',
      urgent: 'red',
    };

    return priorityColors[card.priority || 'medium'];
  };

  // Xác định locale cho date-fns
  const getLocale = () => {
    switch (i18n.language) {
      case 'vi':
        return vi;
      case 'zh':
        return zhCN;
      default:
        return enUS;
    }
  };

  // Format due date
  const formatDueDate = () => {
    if (!card.dueDate) {return '';}

    try {
      const date = new Date(card.dueDate);
      return formatDistanceToNow(date, { addSuffix: true, locale: getLocale() });
    } catch {
      return card.dueDate;
    }
  };

  // Xác định các class dựa trên biến thể
  const getVariantClasses = () => {
    switch (variant) {
      case KanbanVariant.COMPACT:
        return 'p-2 text-sm';
      case KanbanVariant.DETAILED:
        return 'p-4';
      default:
        return 'p-3';
    }
  };

  const getFunctionalityClasses = () => {
    if (functionality === KanbanFunctionality.READONLY) {
      return 'cursor-default pointer-events-none';
    }
    return '';
  };

  const getThemeClasses = () => {
    switch (theme) {
      case KanbanTheme.COLORFUL:
        return `bg-${getPriorityColor()}-50 dark:bg-${getPriorityColor()}-900/20 border-${getPriorityColor()}-200 dark:border-${getPriorityColor()}-800`;
      case KanbanTheme.MINIMAL:
        return 'shadow-none border-0 bg-gray-50 dark:bg-gray-900 hover:bg-white dark:hover:bg-gray-800';
      case KanbanTheme.BORDERED:
        return 'border-2 shadow-none';
      default:
        return 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700';
    }
  };

  // Kết hợp tất cả các class
  const cardClasses = `
    kanban-card
    rounded-md shadow-sm
    hover:shadow-md transition-all duration-200
    ${getVariantClasses()}
    ${getFunctionalityClasses()}
    ${getThemeClasses()}
    ${isDragging ? 'opacity-70 shadow-lg z-50 is-dragging' : 'opacity-100'}
    ${isOver ? 'ring-2 ring-primary ring-opacity-70 is-over' : ''}
    ${className}
  `;

  return (
    <div
      ref={ref}
      className={cardClasses}
      onClick={() => functionality !== KanbanFunctionality.READONLY && onClick?.(card)}
      style={{
        cursor: allowDrag && functionality !== KanbanFunctionality.READONLY ? 'grab' : 'pointer',
        transform: isDragging ? 'rotate(2deg) scale(1.05)' : 'rotate(0) scale(1)',
        transition: 'all 0.2s ease',
      }}
    >
      {/* Header */}
      <div className="flex justify-between items-start mb-2">
        <Typography variant="subtitle2" className="font-medium line-clamp-2">
          {card.title}
        </Typography>
        <div className="flex space-x-1">
          {card.priority && (
            <Tooltip content={t(`kanban.priority.${card.priority}`, card.priority)}>
              <Badge color={getPriorityColor()} size="sm" />
            </Tooltip>
          )}
        </div>
      </div>

      {/* Description */}
      {card.description && variant !== KanbanVariant.COMPACT && (
        <Typography variant="body2" className="text-gray-600 dark:text-gray-400 mb-2 line-clamp-2">
          {card.description}
        </Typography>
      )}

      {/* Labels */}
      {card.labels && card.labels.length > 0 && (
        <div className="flex flex-wrap gap-1 mb-2">
          {card.labels.map(label => (
            <Badge
              key={label.id}
              color={label.color || 'gray'}
              variant="subtle"
              size={variant === KanbanVariant.COMPACT ? 'xs' : 'sm'}
              className="text-xs"
            >
              {variant === KanbanVariant.COMPACT ? '' : label.name}
            </Badge>
          ))}
        </div>
      )}

      {/* Footer */}
      <div className="flex justify-between items-center mt-2 text-gray-500 dark:text-gray-400">
        {/* Left side */}
        <div className="flex items-center space-x-2">
          {card.assignee && (
            <Tooltip content={card.assignee.name}>
              <div
                className={`${variant === KanbanVariant.COMPACT ? 'w-5 h-5' : 'w-6 h-6'} rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center overflow-hidden`}
              >
                {card.assignee.avatar ? (
                  <img
                    src={card.assignee.avatar}
                    alt={card.assignee.name}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <Typography variant="body2" className="text-xs">
                    {card.assignee.name.charAt(0).toUpperCase()}
                  </Typography>
                )}
              </div>
            </Tooltip>
          )}

          {card.dueDate && variant !== KanbanVariant.COMPACT && (
            <Tooltip content={new Date(card.dueDate).toLocaleDateString()}>
              <div className="flex items-center text-xs">
                <Icon name="calendar" size="xs" className="mr-1" />
                <span>{formatDueDate()}</span>
              </div>
            </Tooltip>
          )}
        </div>

        {/* Right side */}
        <div className="flex items-center space-x-2">
          {variant === KanbanVariant.DETAILED && card.progress !== undefined && (
            <div className="w-full h-1 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden mr-2">
              <div
                className={`h-full bg-${getPriorityColor()}-500`}
                style={{ width: `${card.progress}%` }}
              ></div>
            </div>
          )}

          {card.commentsCount !== undefined && card.commentsCount > 0 && (
            <div className="flex items-center text-xs">
              <Icon name="comment" size="xs" className="mr-1" />
              <span>{card.commentsCount}</span>
            </div>
          )}

          {card.attachmentsCount !== undefined &&
            card.attachmentsCount > 0 &&
            variant !== KanbanVariant.COMPACT && (
              <div className="flex items-center text-xs">
                <Icon name="attachment" size="xs" className="mr-1" />
                <span>{card.attachmentsCount}</span>
              </div>
            )}

          {card.progress !== undefined && variant !== KanbanVariant.DETAILED && (
            <div className="flex items-center text-xs">
              <span>{card.progress}%</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default memo(KanbanCard);
