import React from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router-dom';

import { Card } from '@/shared/components/common';
import PageHeader from '@/shared/components/common/PageHeader';

import CreateUserForEmployeeForm from '../components/employee/CreateUserForEmployeeForm';

/**
 * Trang tạo tài khoản người dùng cho nhân viên
 */
const CreateUserForEmployeePage: React.FC = () => {
  const { t } = useTranslation(['hrm', 'common']);
  const navigate = useNavigate();
  const { employeeId } = useParams<{ employeeId?: string }>();

  // Xử lý khi tạo tài khoản thành công
  const handleSuccess = () => {
    // Hiển thị thông báo thành công
    // toast.success(t('hrm:employee.messages.userCreated', '<PERSON><PERSON><PERSON> khoản người dùng đã được tạo thành công'));

    // Chuyển hướng về trang danh sách nhân viên
    navigate('/hrm/employees');
  };

  // Xử lý khi hủy
  const handleCancel = () => {
    navigate('/hrm/employees');
  };

  return (
    <div className="container mx-auto px-4 py-6">
      <PageHeader
        title={t('hrm:employee.createUser.title', 'Tạo tài khoản người dùng')}
        breadcrumbs={[
          { label: t('common:home', 'Trang chủ'), path: '/' },
          { label: t('hrm:module', 'Quản lý nhân sự'), path: '/hrm' },
          { label: t('hrm:employees', 'Nhân viên'), path: '/hrm/employees' },
          { label: t('hrm:employee.createUser.title', 'Tạo tài khoản người dùng'), path: '' },
        ]}
      />

      <Card className="mt-6">
        <CreateUserForEmployeeForm
          onSuccess={handleSuccess}
          onCancel={handleCancel}
          employeeId={employeeId ? parseInt(employeeId, 10) : undefined}
        />
      </Card>
    </div>
  );
};

export default CreateUserForEmployeePage;
