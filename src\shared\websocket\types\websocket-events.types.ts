/**
 * WebSocket Events Types for Chat Communication
 */

import { ChatMessage, MessageContent, TypingIndicator } from './chat-message.types';

// Base event structure
export interface WebSocketEvent<T = unknown> {
  event: string;
  data: T;
  timestamp?: string;
  requestId?: string;
}

// Client to Server Events
export type ClientToServerEvents = {
  // Connection events
  join_room: (data: JoinRoomData) => void;
  leave_room: (data: LeaveRoomData) => void;
  
  // Message events
  send_message: (data: SendMessageData) => void;
  edit_message: (data: EditMessageData) => void;
  delete_message: (data: DeleteMessageData) => void;
  react_to_message: (data: ReactToMessageData) => void;
  
  // Typing events
  typing_start: (data: TypingData) => void;
  typing_stop: (data: TypingData) => void;
  
  // Form events
  form_submit: (data: FormSubmitData) => void;
  form_field_update: (data: FormFieldUpdateData) => void;
  form_validate: (data: FormValidateData) => void;
  
  // Navigation events
  navigation_confirm: (data: NavigationConfirmData) => void;
  
  // Stream events
  stream_acknowledge: (data: StreamAcknowledgeData) => void;
  
  // File events
  file_upload_start: (data: FileUploadStartData) => void;
  file_upload_chunk: (data: FileUploadChunkData) => void;
  file_upload_complete: (data: FileUploadCompleteData) => void;
  
  // Search events
  search_messages: (data: SearchMessagesData) => void;
  
  // History events
  load_message_history: (data: LoadMessageHistoryData) => void;
};

// Server to Client Events
export type ServerToClientEvents = {
  // Connection events
  connection_status: (data: ConnectionStatusData) => void;
  room_joined: (data: RoomJoinedData) => void;
  room_left: (data: RoomLeftData) => void;
  user_joined: (data: UserJoinedData) => void;
  user_left: (data: UserLeftData) => void;
  
  // Message events
  message_received: (data: MessageReceivedData) => void;
  message_updated: (data: MessageUpdatedData) => void;
  message_deleted: (data: MessageDeletedData) => void;
  message_reaction_added: (data: MessageReactionData) => void;
  message_reaction_removed: (data: MessageReactionData) => void;
  
  // Typing events
  user_typing: (data: TypingIndicator) => void;
  
  // Stream events
  stream_start: (data: StreamStartData) => void;
  stream_chunk: (data: StreamChunkData) => void;
  stream_complete: (data: StreamCompleteData) => void;
  stream_error: (data: StreamErrorData) => void;
  
  // Form events
  form_prefill: (data: FormPrefillData) => void;
  form_field_sync: (data: FormFieldSyncData) => void;
  form_validation_result: (data: FormValidationResultData) => void;
  form_submit_result: (data: FormSubmitResultData) => void;
  
  // File events
  file_upload_progress: (data: FileUploadProgressData) => void;
  file_upload_result: (data: FileUploadResultData) => void;
  
  // Search events
  search_results: (data: SearchResultsData) => void;
  
  // History events
  message_history: (data: MessageHistoryData) => void;
  
  // Error events
  error: (data: ErrorData) => void;
  
  // System events
  system_notification: (data: SystemNotificationData) => void;
};

// Event Data Types

// Connection Events
export interface JoinRoomData {
  roomId: string;
  userId: string;
  userInfo?: {
    name: string;
    avatar?: string;
  };
}

export interface LeaveRoomData {
  roomId: string;
  userId: string;
}

export interface ConnectionStatusData {
  status: 'connected' | 'disconnected' | 'reconnecting' | 'error';
  timestamp: string;
  reason?: string;
}

export interface RoomJoinedData {
  roomId: string;
  userId: string;
  activeUsers: Array<{
    id: string;
    name: string;
    avatar?: string;
    joinedAt: string;
  }>;
}

export interface RoomLeftData {
  roomId: string;
  userId: string;
}

export interface UserJoinedData {
  roomId: string;
  user: {
    id: string;
    name: string;
    avatar?: string;
    joinedAt: string;
  };
}

export interface UserLeftData {
  roomId: string;
  userId: string;
  leftAt: string;
}

// Message Events
export interface SendMessageData {
  roomId: string;
  content: MessageContent;
  replyTo?: string;
  tempId?: string;
}

export interface EditMessageData {
  messageId: string;
  content: MessageContent;
  roomId: string;
}

export interface DeleteMessageData {
  messageId: string;
  roomId: string;
}

export interface ReactToMessageData {
  messageId: string;
  roomId: string;
  emoji: string;
  action: 'add' | 'remove';
}

export interface MessageReceivedData {
  message: ChatMessage;
  tempId?: string;
}

export interface MessageUpdatedData {
  messageId: string;
  content: MessageContent;
  editedAt: string;
  roomId: string;
}

export interface MessageDeletedData {
  messageId: string;
  roomId: string;
  deletedAt: string;
}

export interface MessageReactionData {
  messageId: string;
  roomId: string;
  emoji: string;
  userId: string;
  userName: string;
  action: 'add' | 'remove';
}

// Typing Events
export interface TypingData {
  roomId: string;
  userId: string;
}

// Form Events
export interface FormSubmitData {
  formId: string;
  messageId: string;
  formData: Record<string, unknown>;
  validation?: boolean;
  roomId: string;
}

export interface FormFieldUpdateData {
  formId: string;
  messageId: string;
  fieldName: string;
  fieldValue: unknown;
  roomId: string;
}

export interface FormValidateData {
  formId: string;
  messageId: string;
  formData: Record<string, unknown>;
  fieldName?: string;
  roomId: string;
}

export interface FormPrefillData {
  formId: string;
  messageId: string;
  data: Record<string, unknown>;
  roomId: string;
}

export interface FormFieldSyncData {
  formId: string;
  messageId: string;
  fieldName: string;
  fieldValue: unknown;
  source: 'server' | 'user';
  roomId: string;
}

export interface FormValidationResultData {
  formId: string;
  messageId: string;
  isValid: boolean;
  errors: Record<string, string>;
  fieldName?: string;
  roomId: string;
}

export interface FormSubmitResultData {
  formId: string;
  messageId: string;
  success: boolean;
  data?: unknown;
  error?: string;
  roomId: string;
}

// Navigation Events
export interface NavigationConfirmData {
  messageId: string;
  confirmed: boolean;
  roomId: string;
}

// Stream Events
export interface StreamAcknowledgeData {
  streamId: string;
  messageId: string;
  chunkIndex: number;
}

export interface StreamStartData {
  streamId: string;
  messageId: string;
  totalChunks?: number;
  contentType: string;
}

export interface StreamChunkData {
  streamId: string;
  messageId: string;
  chunk: string;
  chunkIndex: number;
  isComplete: boolean;
  totalChunks?: number;
}

export interface StreamCompleteData {
  streamId: string;
  messageId: string;
  finalContent: string;
  totalChunks: number;
}

export interface StreamErrorData {
  streamId: string;
  messageId: string;
  error: string;
  chunkIndex?: number;
}

// File Events
export interface FileUploadStartData {
  fileName: string;
  fileSize: number;
  fileType: string;
  roomId: string;
  chunkSize?: number;
}

export interface FileUploadChunkData {
  uploadId: string;
  chunkIndex: number;
  chunkData: string; // Base64 encoded
  isLastChunk: boolean;
}

export interface FileUploadCompleteData {
  uploadId: string;
  roomId: string;
}

export interface FileUploadProgressData {
  uploadId: string;
  progress: number;
  chunkIndex: number;
  totalChunks: number;
}

export interface FileUploadResultData {
  uploadId: string;
  success: boolean;
  fileUrl?: string;
  fileName?: string;
  error?: string;
}

// Search Events
export interface SearchMessagesData {
  roomId: string;
  query: string;
  filters?: {
    contentType?: string;
    senderId?: string;
    dateFrom?: string;
    dateTo?: string;
  };
  limit?: number;
  offset?: number;
}

export interface SearchResultsData {
  query: string;
  results: Array<{
    message: ChatMessage;
    highlights: string[];
    score: number;
  }>;
  total: number;
  hasMore: boolean;
}

// History Events
export interface LoadMessageHistoryData {
  roomId: string;
  before?: string;
  after?: string;
  limit?: number;
}

export interface MessageHistoryData {
  roomId: string;
  messages: ChatMessage[];
  hasMore: boolean;
  nextCursor?: string;
  prevCursor?: string;
}

// Error Events
export interface ErrorData {
  code: string;
  message: string;
  details?: Record<string, unknown>;
  timestamp: string;
}

// System Events
export interface SystemNotificationData {
  type: 'info' | 'warning' | 'error' | 'success';
  title: string;
  message: string;
  duration?: number;
  actions?: Array<{
    label: string;
    action: string;
    data?: Record<string, unknown>;
  }>;
}

// Event acknowledgment
export interface EventAcknowledgment {
  success: boolean;
  error?: string;
  data?: unknown;
}

// Export all types
export type {
  WebSocketEvent,
  ClientToServerEvents,
  ServerToClientEvents,
  EventAcknowledgment,
};
