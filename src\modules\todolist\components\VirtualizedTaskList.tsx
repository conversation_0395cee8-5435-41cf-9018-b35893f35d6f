import React from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import { Badge, IconButton, Tooltip, Loading, Typography } from '@/shared/components/common';
import { NotificationUtil } from '@/shared/utils/notification';

import { useDeleteTask, useUpdateTaskStatus } from '../hooks/useTasks';
import { useVirtualizedList } from '../hooks/useVirtualizedList';
import { TaskDto, TaskStatus, TaskPriority } from '../types/task.types';

interface VirtualizedTaskListProps {
  tasks: TaskDto[];
  isLoading: boolean;
  onRefresh: () => void;
  itemHeight?: number;
  maxHeight?: number;
}

/**
 * Virtualized task list component for improved performance with large datasets
 */
const VirtualizedTaskList: React.FC<VirtualizedTaskListProps> = ({
  tasks,
  isLoading,
  onRefresh,
  itemHeight = 80,
  maxHeight = 600,
}) => {
  const { t } = useTranslation(['common', 'todolist']);
  const navigate = useNavigate();
  const { mutateAsync: deleteTask } = useDeleteTask();
  const { mutateAsync: updateTaskStatus } = useUpdateTaskStatus();

  // Set up virtualized list
  const { virtualItems, totalHeight, containerRef, isScrolling } = useVirtualizedList({
    items: tasks,
    itemHeight,
  });

  // Get status badge color
  const getStatusBadgeColor = (status: TaskStatus | null) => {
    switch (status) {
      case TaskStatus.PENDING:
        return 'bg-gray-100 text-gray-800';
      case TaskStatus.IN_PROGRESS:
        return 'bg-blue-100 text-blue-800';
      case TaskStatus.COMPLETED:
      case TaskStatus.APPROVED:
        return 'bg-green-100 text-green-800';
      case TaskStatus.REJECTED:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get priority badge color
  const getPriorityBadgeColor = (priority: TaskPriority | null) => {
    switch (priority) {
      case TaskPriority.LOW:
        return 'bg-green-100 text-green-800';
      case TaskPriority.MEDIUM:
        return 'bg-blue-100 text-blue-800';
      case TaskPriority.HIGH:
        return 'bg-orange-100 text-orange-800';
      case TaskPriority.URGENT:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get status text
  const getStatusText = (status: TaskStatus | null) => {
    switch (status) {
      case TaskStatus.PENDING:
        return t('todolist:task.status.todo', 'To Do');
      case TaskStatus.IN_PROGRESS:
        return t('todolist:task.status.inProgress', 'In Progress');
      case TaskStatus.COMPLETED:
      case TaskStatus.APPROVED:
        return t('todolist:task.status.done', 'Done');
      case TaskStatus.REJECTED:
        return t('todolist:task.status.cancelled', 'Cancelled');
      default:
        return '';
    }
  };

  // Get priority text
  const getPriorityText = (priority: TaskPriority | null) => {
    switch (priority) {
      case TaskPriority.LOW:
        return t('todolist:task.priority.low', 'Low');
      case TaskPriority.MEDIUM:
        return t('todolist:task.priority.medium', 'Medium');
      case TaskPriority.HIGH:
        return t('todolist:task.priority.high', 'High');
      case TaskPriority.URGENT:
        return t('todolist:task.priority.urgent', 'Urgent');
      default:
        return '';
    }
  };

  // Handle delete task
  const handleDeleteTask = async (id: number, e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await deleteTask(id);
      NotificationUtil.success({
        message: t('todolist:task.notifications.deleteSuccess', 'Task deleted successfully'),
      });
      onRefresh();
    } catch (error) {
      console.error('Error deleting task:', error);
      NotificationUtil.error({
        message: t('todolist:task.notifications.deleteError', 'Error deleting task'),
      });
    }
  };

  // Handle status change
  const handleStatusChange = async (id: number, status: TaskStatus, e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await updateTaskStatus({ id, data: { status } });
      NotificationUtil.success({
        message: t(
          'todolist:task.notifications.statusUpdateSuccess',
          'Task status updated successfully'
        ),
      });
      onRefresh();
    } catch (error) {
      console.error('Error updating task status:', error);
      NotificationUtil.error({
        message: t('todolist:task.notifications.statusUpdateError', 'Error updating task status'),
      });
    }
  };

  // Handle task click
  const handleTaskClick = (id: number) => {
    navigate(`/todolist/tasks/${id}`);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-32">
        <Loading size="md" />
      </div>
    );
  }

  if (tasks.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        {t('todolist:task.empty', 'No tasks found')}
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className="overflow-auto"
      style={{ height: Math.min(totalHeight, maxHeight), maxHeight }}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        {virtualItems.map(({ item: task, offsetTop }) => (
          <div
            key={task.id}
            className="absolute w-full border-b border-gray-100 hover:bg-gray-50 cursor-pointer transition-colors"
            style={{
              height: itemHeight,
              top: offsetTop,
              transform: isScrolling ? 'translateZ(0)' : undefined, // Force GPU acceleration during scrolling
            }}
            onClick={() => handleTaskClick(task.id)}
          >
            <div className="p-4 h-full flex justify-between items-center">
              <div className="flex-1 min-w-0">
                <Typography variant="subtitle1" className="font-medium truncate">
                  {task.title}
                </Typography>
                {task.description && (
                  <Typography variant="body2" className="text-gray-500 truncate">
                    {task.description}
                  </Typography>
                )}
              </div>

              <div className="flex items-center space-x-2 ml-4">
                <Badge className={getStatusBadgeColor(task.status)}>
                  {getStatusText(task.status)}
                </Badge>

                {task.priority && (
                  <Badge className={getPriorityBadgeColor(task.priority)}>
                    {getPriorityText(task.priority)}
                  </Badge>
                )}

                <div className="flex space-x-1 ml-2">
                  <Tooltip content={t('todolist:task.status.todo', 'To Do')}>
                    <IconButton
                      icon="clock"
                      size="sm"
                      variant={task.status === TaskStatus.PENDING ? 'primary' : 'outline'}
                      onClick={() =>
                        handleStatusChange(task.id, TaskStatus.PENDING, {} as React.MouseEvent)
                      }
                    />
                  </Tooltip>
                  <Tooltip content={t('todolist:task.status.inProgress', 'In Progress')}>
                    <IconButton
                      icon="loading"
                      size="sm"
                      variant={task.status === TaskStatus.IN_PROGRESS ? 'primary' : 'outline'}
                      onClick={() =>
                        handleStatusChange(task.id, TaskStatus.IN_PROGRESS, {} as React.MouseEvent)
                      }
                    />
                  </Tooltip>
                  <Tooltip content={t('todolist:task.status.done', 'Done')}>
                    <IconButton
                      icon="check"
                      size="sm"
                      variant={task.status === TaskStatus.COMPLETED ? 'primary' : 'outline'}
                      onClick={() =>
                        handleStatusChange(task.id, TaskStatus.COMPLETED, {} as React.MouseEvent)
                      }
                    />
                  </Tooltip>
                  <Tooltip content={t('common:delete', 'Delete')}>
                    <IconButton
                      icon="trash"
                      size="sm"
                      variant="outline"
                      className="text-red-500"
                      onClick={() => handleDeleteTask(task.id, {} as React.MouseEvent)}
                    />
                  </Tooltip>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default VirtualizedTaskList;
