import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

import { AttachmentService } from '../services/attachment.service';
import {
  AttachmentQueryDto,
  CreateAttachmentDto,
  UpdateAttachmentDto,
} from '../types/attachment.types';

// Key for React Query
const ATTACHMENTS_QUERY_KEY = 'attachments';

/**
 * Hook to get attachments for a task
 * @param taskId Task ID
 * @param params Query parameters
 * @returns Query result with attachments
 */
export const useAttachments = (taskId: number, params?: AttachmentQueryDto) => {
  return useQuery({
    queryKey: [ATTACHMENTS_QUERY_KEY, taskId, params],
    queryFn: () => AttachmentService.getAttachments(taskId, params),
    select: data => data.result,
    enabled: !!taskId,
  });
};

/**
 * Hook to get an attachment by ID
 * @param taskId Task ID
 * @param attachmentId Attachment ID
 * @returns Query result with the attachment
 */
export const useAttachment = (taskId: number, attachmentId: number) => {
  return useQuery({
    queryKey: [ATTACHMENTS_QUERY_KEY, taskId, attachmentId],
    queryFn: () => AttachmentService.getAttachment(taskId, attachmentId),
    select: data => data.result,
    enabled: !!taskId && !!attachmentId,
  });
};

/**
 * Hook to create a new attachment
 * @returns Mutation result for creating an attachment
 */
export const useCreateAttachment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateAttachmentDto) => AttachmentService.createAttachment(data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [ATTACHMENTS_QUERY_KEY, variables.taskId] });
    },
  });
};

/**
 * Hook to update an attachment
 * @returns Mutation result for updating an attachment
 */
export const useUpdateAttachment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      taskId,
      attachmentId,
      data,
    }: {
      taskId: number;
      attachmentId: number;
      data: UpdateAttachmentDto;
    }) => AttachmentService.updateAttachment(taskId, attachmentId, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [ATTACHMENTS_QUERY_KEY, variables.taskId] });
      queryClient.invalidateQueries({
        queryKey: [ATTACHMENTS_QUERY_KEY, variables.taskId, variables.attachmentId],
      });
    },
  });
};

/**
 * Hook to delete an attachment
 * @returns Mutation result for deleting an attachment
 */
export const useDeleteAttachment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ taskId, attachmentId }: { taskId: number; attachmentId: number }) =>
      AttachmentService.deleteAttachment(taskId, attachmentId),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [ATTACHMENTS_QUERY_KEY, variables.taskId] });
    },
  });
};

/**
 * Hook to get upload URL for an attachment
 * @returns Mutation result for getting an upload URL
 */
export const useGetUploadUrl = () => {
  return useMutation({
    mutationFn: ({
      taskId,
      fileName,
      fileType,
    }: {
      taskId: number;
      fileName: string;
      fileType: string;
    }) => AttachmentService.getUploadUrl(taskId, fileName, fileType),
  });
};
