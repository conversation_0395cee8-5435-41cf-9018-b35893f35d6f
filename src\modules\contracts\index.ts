/**
 * Contracts Module Exports
 */

// Types
export * from './types/contract.types';
export * from './types/contract-template.types';
export * from './types/contract-signature.types';
export * from './types/contract-approval.types';

// Services
export * from './services/contract.service';
export * from './services/contract-template.service';
export * from './services/contract-signature.service';
export * from './services/contract-approval.service';

// Hooks
export * from './hooks/useContracts';
export * from './hooks/useContractTemplates';
export * from './hooks/useContractSignatures';
export * from './hooks/useContractApprovals';

// Components
export { default as ContractCard } from './components/ContractCard';
export { default as ContractForm } from './components/ContractForm';
export { default as ContractViewer } from './components/ContractViewer';
export { default as SignaturePanel } from './components/SignaturePanel';
export { default as ApprovalWorkflow } from './components/ApprovalWorkflow';
export { default as ContractTimeline } from './components/ContractTimeline';
export { default as ContractStats } from './components/ContractStats';
export { default as TemplateBuilder } from './components/TemplateBuilder';

// Pages
export { default as ContractsHomePage } from './pages/ContractsHomePage';
export { default as ContractListPage } from './pages/ContractListPage';
export { default as ContractCreatePage } from './pages/ContractCreatePage';
export { default as ContractDetailPage } from './pages/ContractDetailPage';
export { default as ContractEditPage } from './pages/ContractEditPage';
export { default as ContractSignPage } from './pages/ContractSignPage';
export { default as ContractTemplatesPage } from './pages/ContractTemplatesPage';
export { default as ContractApprovalsPage } from './pages/ContractApprovalsPage';
export { default as ContractReportsPage } from './pages/ContractReportsPage';

// Routes
export { default as contractRoutes } from './routers/contractRoutes';

// Locales
export { default as contractResources } from './locales';
