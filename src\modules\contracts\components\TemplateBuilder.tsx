import React from 'react';
import { useTranslation } from 'react-i18next';

import { Card, Typography } from '@/shared/components/common';

/**
 * Template Builder Component
 * Component xây dựng mẫu hợp đồng
 */
const TemplateBuilder: React.FC = () => {
  const { t } = useTranslation(['common', 'contracts']);

  return (
    <Card className="p-6">
      <Typography variant="h2" className="mb-4">
        {t('contracts:templates')}
      </Typography>
      <div className="text-center py-8">
        <Typography variant="body1" className="text-muted-foreground">
          Trình xây dựng mẫu hợp đồng đang được phát triển...
        </Typography>
      </div>
    </Card>
  );
};

export default TemplateBuilder;
