// Import locale từ thư viện FullCalendar
// Trong FullCalendar 6.x, bạn nên sử dụng các locale có sẵn từ thư viện
// Xem: https://fullcalendar.io/docs/locale

/**
 * Locale tiếng Việt cho FullCalendar
 *
 * Để sử dụng locale này, bạn cần import nó trong component:
 * ```
 * import viLocale from '@/modules/calendar/locales/vi.fullcalendar';
 *
 * const Calendar = () => {
 *   return (
 *     <FullCalendar
 *       locale={viLocale}
 *       // ...
 *     />
 *   );
 * };
 * ```
 */
const viLocale = {
  code: 'vi',
  buttonText: {
    prev: 'Trước',
    next: 'Tiếp',
    today: 'Hôm nay',
    month: 'Tháng',
    week: 'Tuần',
    day: 'Ngày',
    list: 'Danh sách',
  },
  weekText: 'Tu',
  allDayText: '<PERSON><PERSON> ngày',
  moreLinkText: 'thêm',
  noEventsText: 'Không có sự kiện để hiển thị',
  firstDay: 1, // Thứ 2 là ngày đầu tuần
};

export default viLocale;
