import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';

import { User } from '../../auth/entities/user.entity';
import { Contract } from './contract.entity';

export enum CommentType {
  GENERAL = 'general',
  REVIEW = 'review',
  APPROVAL = 'approval',
  REJECTION = 'rejection',
  QUESTION = 'question',
  SUGGESTION = 'suggestion',
  SYSTEM = 'system',
}

/**
 * Entity bình luận hợp đồng
 */
@Entity('contract_comments')
export class ContractComment {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'text' })
  content: string;

  @Column({ type: 'enum', enum: CommentType, default: CommentType.GENERAL })
  type: CommentType;

  @Column({ type: 'boolean', default: false })
  isInternal: boolean; // Internal comments not visible to external parties

  @Column({ type: 'boolean', default: false })
  isResolved: boolean;

  @Column({ type: 'timestamp', nullable: true })
  resolvedAt: Date;

  @Column({ type: 'json', nullable: true })
  mentions: {
    userId: string;
    userName: string;
    email: string;
  }[];

  @Column({ type: 'json', nullable: true })
  attachments: {
    fileName: string;
    fileUrl: string;
    fileSize: number;
    mimeType: string;
  }[];

  @Column({ type: 'json', nullable: true })
  position: {
    page?: number;
    x?: number;
    y?: number;
    width?: number;
    height?: number;
    text?: string; // Highlighted text
  };

  @Column({ type: 'varchar', length: 50, nullable: true })
  priority: 'low' | 'medium' | 'high' | 'urgent';

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  // Relations
  @ManyToOne(() => Contract, (contract) => contract.comments, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'contractId' })
  contract: Contract;

  @Column({ type: 'uuid' })
  contractId: string;

  @ManyToOne(() => User, { nullable: false })
  @JoinColumn({ name: 'authorId' })
  author: User;

  @Column({ type: 'uuid' })
  authorId: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'resolvedById' })
  resolvedBy: User;

  @Column({ type: 'uuid', nullable: true })
  resolvedById: string;

  @ManyToOne(() => ContractComment, { nullable: true })
  @JoinColumn({ name: 'parentCommentId' })
  parentComment: ContractComment;

  @Column({ type: 'uuid', nullable: true })
  parentCommentId: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Methods
  resolve(userId: string): void {
    this.isResolved = true;
    this.resolvedAt = new Date();
    this.resolvedById = userId;
  }

  unresolve(): void {
    this.isResolved = false;
    this.resolvedAt = null;
    this.resolvedById = null;
  }

  addMention(userId: string, userName: string, email: string): void {
    if (!this.mentions) {
      this.mentions = [];
    }

    // Check if user is already mentioned
    const existingMention = this.mentions.find(m => m.userId === userId);
    if (!existingMention) {
      this.mentions.push({ userId, userName, email });
    }
  }

  removeMention(userId: string): void {
    if (this.mentions) {
      this.mentions = this.mentions.filter(m => m.userId !== userId);
    }
  }

  addAttachment(fileName: string, fileUrl: string, fileSize: number, mimeType: string): void {
    if (!this.attachments) {
      this.attachments = [];
    }

    this.attachments.push({
      fileName,
      fileUrl,
      fileSize,
      mimeType,
    });
  }

  removeAttachment(fileName: string): void {
    if (this.attachments) {
      this.attachments = this.attachments.filter(a => a.fileName !== fileName);
    }
  }

  setPosition(page: number, x: number, y: number, width?: number, height?: number, text?: string): void {
    this.position = {
      page,
      x,
      y,
      width,
      height,
      text,
    };
  }

  isVisibleTo(userId: string, userRoles: string[]): boolean {
    // System comments are always visible
    if (this.type === CommentType.SYSTEM) {
      return true;
    }

    // Internal comments are only visible to internal users
    if (this.isInternal) {
      // Check if user has internal role (implementation depends on role system)
      return userRoles.includes('internal') || userRoles.includes('admin');
    }

    return true;
  }

  getCommentInfo(): {
    id: string;
    content: string;
    type: CommentType;
    author: string;
    createdAt: Date;
    isResolved: boolean;
    hasAttachments: boolean;
    mentionCount: number;
  } {
    return {
      id: this.id,
      content: this.content,
      type: this.type,
      author: this.author?.email || 'Unknown',
      createdAt: this.createdAt,
      isResolved: this.isResolved,
      hasAttachments: Boolean(this.attachments?.length),
      mentionCount: this.mentions?.length || 0,
    };
  }

  extractMentions(content: string): string[] {
    // Extract @mentions from content
    const mentionRegex = /@(\w+)/g;
    const mentions: string[] = [];
    let match;

    while ((match = mentionRegex.exec(content)) !== null) {
      mentions.push(match[1]);
    }

    return mentions;
  }

  formatContent(): string {
    let formattedContent = this.content;

    // Replace mentions with formatted links
    if (this.mentions) {
      this.mentions.forEach(mention => {
        const mentionText = `@${mention.userName}`;
        const mentionLink = `<span class="mention" data-user-id="${mention.userId}">${mentionText}</span>`;
        formattedContent = formattedContent.replace(mentionText, mentionLink);
      });
    }

    return formattedContent;
  }
}
