import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';

import { User } from '../../auth/entities/user.entity';
import { Contract } from './contract.entity';

export enum VersionType {
  INITIAL = 'initial',
  REVISION = 'revision',
  AMENDMENT = 'amendment',
  RENEWAL = 'renewal',
}

/**
 * Entity phiên bản hợp đồng
 */
@Entity('contract_versions')
export class ContractVersion {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 20 })
  version: string; // e.g., "1.0", "1.1", "2.0"

  @Column({ type: 'enum', enum: VersionType, default: VersionType.REVISION })
  type: VersionType;

  @Column({ type: 'text' })
  content: string;

  @Column({ type: 'text', nullable: true })
  changeLog: string;

  @Column({ type: 'text', nullable: true })
  changeReason: string;

  @Column({ type: 'boolean', default: false })
  isActive: boolean;

  @Column({ type: 'boolean', default: false })
  isFinal: boolean;

  @Column({ type: 'json', nullable: true })
  changes: {
    field: string;
    oldValue: any;
    newValue: any;
    changeType: 'added' | 'modified' | 'removed';
  }[];

  @Column({ type: 'varchar', length: 255, nullable: true })
  documentUrl: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  documentHash: string;

  @Column({ type: 'int', nullable: true })
  documentSize: number;

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  // Relations
  @ManyToOne(() => Contract, (contract) => contract.versions, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'contractId' })
  contract: Contract;

  @Column({ type: 'uuid' })
  contractId: string;

  @ManyToOne(() => User, { nullable: false })
  @JoinColumn({ name: 'createdById' })
  createdBy: User;

  @Column({ type: 'uuid' })
  createdById: string;

  @CreateDateColumn()
  createdAt: Date;

  // Methods
  activate(): void {
    this.isActive = true;
  }

  deactivate(): void {
    this.isActive = false;
  }

  finalize(): void {
    this.isFinal = true;
  }

  generateNextVersion(type: VersionType = VersionType.REVISION): string {
    const [major, minor] = this.version.split('.').map(Number);
    
    switch (type) {
      case VersionType.AMENDMENT:
      case VersionType.RENEWAL:
        return `${major + 1}.0`;
      case VersionType.REVISION:
      default:
        return `${major}.${minor + 1}`;
    }
  }

  compareWith(otherVersion: ContractVersion): {
    differences: {
      field: string;
      thisValue: any;
      otherValue: any;
      changeType: 'added' | 'modified' | 'removed';
    }[];
    similarity: number;
  } {
    // Simple comparison implementation
    // In real implementation, use proper diff algorithms
    const differences: any[] = [];
    
    if (this.content !== otherVersion.content) {
      differences.push({
        field: 'content',
        thisValue: this.content,
        otherValue: otherVersion.content,
        changeType: 'modified',
      });
    }

    const similarity = differences.length === 0 ? 100 : 
      Math.max(0, 100 - (differences.length * 10));

    return {
      differences,
      similarity,
    };
  }

  getVersionInfo(): {
    version: string;
    type: VersionType;
    isActive: boolean;
    isFinal: boolean;
    createdAt: Date;
    createdBy: string;
  } {
    return {
      version: this.version,
      type: this.type,
      isActive: this.isActive,
      isFinal: this.isFinal,
      createdAt: this.createdAt,
      createdBy: this.createdBy?.email || 'Unknown',
    };
  }
}
