import React from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';

import { Card, Typography } from '@/shared/components/common';

/**
 * Contract Edit Page
 * Trang chỉnh sửa hợp đồng
 */
const ContractEditPage: React.FC = () => {
  const { t } = useTranslation(['common', 'contracts']);
  const { id } = useParams<{ id: string }>();

  return (
    <div className="w-full bg-background text-foreground">
      <div className="mb-6">
        <Typography variant="h1" className="mb-2">
          Chỉnh sửa hợp đồng #{id}
        </Typography>
        <Typography variant="body1" className="text-muted-foreground">
          Cập nhật thông tin hợp đồng
        </Typography>
      </div>

      <Card className="p-6">
        <div className="text-center py-8">
          <Typography variant="body1" className="text-muted-foreground">
            Trang chỉnh sửa hợp đồng đang được phát triển...
          </Typography>
        </div>
      </Card>
    </div>
  );
};

export default ContractEditPage;
