# WebSocket Chat Panel Implementation Plan

## 📋 Tổng Quan Dự Án

### 🎯 <PERSON><PERSON>c Tiêu

Triển khai hệ thống chat real-time với WebSocket hỗ trợ:

- Hiển thị đa dạng nội dung: text, markdown, hình ảnh, video, links
- Stream tin nhắn real-time
- Hiển thị và điền form động từ backend
- <PERSON><PERSON>ều hướng trang thông qua tin nhắn
- Tích hợp với hệ thống form hiện có

### 🏗️ Kiến Trúc Tổng Thể

```
Frontend (React + TypeScript)
├── WebSocket Client (Socket.IO)
├── Message Renderer System
├── Form Integration System
├── Navigation Handler
└── Media Handler

Backend (Node.js + Socket.IO)
├── WebSocket Server
├── Message Broadcasting
├── Form Data Streaming
└── Navigation Commands
```

## 🔧 Cấu Trúc JSON WebSocket

### 1. Message Schema (Frontend ↔ Backend)

```json
{
  "id": "msg_123456789",
  "type": "message",
  "timestamp": "2024-01-15T10:30:00Z",
  "sender": {
    "id": "user_123",
    "name": "<PERSON>",
    "type": "user|ai|system",
    "avatar": "/path/to/avatar.jpg"
  },
  "content": {
    "type": "text|markdown|image|video|link|form|navigation|stream",
    "data": {
      // Nội dung cụ thể theo type
    }
  },
  "metadata": {
    "roomId": "room_123",
    "conversationId": "conv_456",
    "isStreaming": false,
    "streamComplete": true,
    "priority": "normal|high|urgent"
  }
}
```

### 2. Content Types Schema

#### Text Message

```json
{
  "type": "text",
  "data": {
    "text": "Hello, how can I help you?",
    "formatting": {
      "bold": [[0, 5]],
      "italic": [[7, 12]],
      "code": [[14, 20]]
    }
  }
}
```

#### Markdown Message

```json
{
  "type": "markdown",
  "data": {
    "markdown": "## Welcome\n\nThis is **bold** text with [link](https://example.com)",
    "allowHtml": false,
    "sanitize": true
  }
}
```

#### Image Message

```json
{
  "type": "image",
  "data": {
    "url": "https://example.com/image.jpg",
    "alt": "Description",
    "caption": "Image caption",
    "thumbnail": "https://example.com/thumb.jpg",
    "width": 800,
    "height": 600,
    "size": 1024000,
    "format": "jpg|png|gif|webp"
  }
}
```

#### Video Message

```json
{
  "type": "video",
  "data": {
    "url": "https://example.com/video.mp4",
    "thumbnail": "https://example.com/video-thumb.jpg",
    "title": "Video Title",
    "duration": 120,
    "autoplay": false,
    "controls": true,
    "muted": false,
    "loop": false
  }
}
```

#### Link Message

```json
{
  "type": "link",
  "data": {
    "url": "https://example.com",
    "title": "Page Title",
    "description": "Page description",
    "image": "https://example.com/og-image.jpg",
    "domain": "example.com",
    "favicon": "https://example.com/favicon.ico"
  }
}
```

#### Form Message

```json
{
  "type": "form",
  "data": {
    "formId": "user_registration",
    "title": "User Registration",
    "description": "Please fill out the form below",
    "schema": {
      "type": "object",
      "properties": {
        "name": {
          "type": "string",
          "title": "Full Name",
          "required": true,
          "validation": {
            "minLength": 2,
            "maxLength": 50
          }
        },
        "email": {
          "type": "string",
          "format": "email",
          "title": "Email Address",
          "required": true
        },
        "department": {
          "type": "select",
          "title": "Department",
          "options": [
            { "value": "it", "label": "IT Department" },
            { "value": "hr", "label": "HR Department" }
          ]
        }
      }
    },
    "submitEndpoint": "/api/forms/user_registration",
    "submitMethod": "POST",
    "prefillData": {
      "name": "John Doe",
      "email": "<EMAIL>"
    },
    "validation": {
      "realtime": true,
      "showErrors": true
    }
  }
}
```

#### Navigation Message

```json
{
  "type": "navigation",
  "data": {
    "action": "redirect|open_modal|open_sidebar|open_tab",
    "url": "/dashboard/users",
    "title": "Go to Users Page",
    "description": "Click to navigate to users management",
    "target": "_self|_blank|modal|sidebar",
    "params": {
      "userId": "123",
      "tab": "profile"
    },
    "confirmation": {
      "required": true,
      "message": "Are you sure you want to navigate away?"
    }
  }
}
```

#### Stream Message

```json
{
  "type": "stream",
  "data": {
    "streamId": "stream_123",
    "chunk": "This is a chunk of streamed text...",
    "isComplete": false,
    "totalChunks": 10,
    "currentChunk": 3,
    "contentType": "text|markdown|json",
    "encoding": "utf-8"
  }
}
```

### 3. WebSocket Events Schema

#### Client → Server Events

```json
// Send Message
{
  "event": "send_message",
  "data": {
    "roomId": "room_123",
    "content": {
      "type": "text",
      "data": { "text": "Hello!" }
    },
    "replyTo": "msg_456"
  }
}

// Join Room
{
  "event": "join_room",
  "data": {
    "roomId": "room_123",
    "userId": "user_123"
  }
}

// Typing Indicators
{
  "event": "typing_start",
  "data": {
    "roomId": "room_123",
    "userId": "user_123"
  }
}

// Form Submission
{
  "event": "form_submit",
  "data": {
    "formId": "user_registration",
    "messageId": "msg_123",
    "formData": {
      "name": "John Doe",
      "email": "<EMAIL>"
    },
    "validation": true
  }
}
```

#### Server → Client Events

```json
// Message Received
{
  "event": "message_received",
  "data": {
    // Message schema như trên
  }
}

// Stream Chunk
{
  "event": "stream_chunk",
  "data": {
    "messageId": "msg_123",
    "streamId": "stream_123",
    "chunk": "text chunk...",
    "isComplete": false,
    "chunkIndex": 3
  }
}

// Form Prefill
{
  "event": "form_prefill",
  "data": {
    "formId": "user_registration",
    "messageId": "msg_123",
    "data": {
      "name": "John Doe",
      "email": "<EMAIL>"
    }
  }
}
```

## 📁 Cấu Trúc File Implementation

### Phase 1: WebSocket Infrastructure

#### 1.1 Types & Interfaces

```
src/shared/websocket/types/
├── chat-message.types.ts       # Message schemas
├── content.types.ts           # Content type definitions
├── websocket-events.types.ts  # Event schemas
└── form-sync.types.ts         # Form synchronization types
```

#### 1.2 WebSocket Services

```
src/shared/services/
├── chat-websocket.service.ts   # Main WebSocket service
├── message-parser.service.ts   # Message parsing logic
├── form-sync.service.ts        # Form synchronization
└── navigation.service.ts       # Navigation handling
```

#### 1.3 WebSocket Hooks

```
src/shared/websocket/hooks/
├── useChatWebSocket.ts         # Main chat WebSocket hook
├── useMessageStream.ts         # Streaming messages
├── useFormSync.ts              # Form synchronization
├── useTypingIndicator.ts       # Typing indicators
└── useNavigationHandler.ts     # Navigation handling
```

## 🚀 Implementation Phases

### Phase 1: WebSocket Infrastructure (Days 1-2)

- [ ] Create message type definitions
- [ ] Implement WebSocket service
- [ ] Create basic WebSocket hooks
- [ ] Set up connection management

### Phase 2: Message Rendering (Days 3-4)

- [ ] Create MessageRenderer component
- [ ] Implement basic content types (text, markdown)
- [ ] Add message streaming support
- [ ] Create message container and styling

### Phase 3: Form Integration (Days 5-6)

- [ ] Create FormMessage component
- [ ] Implement form schema parsing
- [ ] Add real-time form synchronization
- [ ] Create form validation system

### Phase 4: Advanced Content Types (Days 7-8)

- [ ] Implement media messages (image, video)
- [ ] Add link preview functionality
- [ ] Create navigation message handling
- [ ] Add file upload support

### Phase 5: Enhanced Features (Days 9-10)

- [ ] Add typing indicators
- [ ] Implement message search
- [ ] Create message history
- [ ] Add error handling and recovery

### Phase 6: Testing & Optimization (Days 11-12)

- [ ] Unit tests for components
- [ ] Integration tests for WebSocket
- [ ] Performance optimization
- [ ] Cross-browser testing

## 🎉 Implementation Status

### ✅ Phase 1: WebSocket Infrastructure (COMPLETED)

- [x] Create message type definitions
- [x] Implement WebSocket service
- [x] Create basic WebSocket hooks
- [x] Set up connection management

### ✅ Phase 2: Message Rendering (COMPLETED)

- [x] Create MessageRenderer component
- [x] Implement basic content types (text, markdown)
- [x] Add message streaming support
- [x] Create message container and styling

### ✅ Phase 3: Form Integration (COMPLETED)

- [x] Create FormMessage component
- [x] Implement form schema parsing
- [x] Add real-time form synchronization
- [x] Create form validation system

### ✅ Phase 4: Advanced Content Types (COMPLETED)

- [x] Implement media messages (image, video, audio)
- [x] Add link preview functionality
- [x] Create navigation message handling
- [x] Add file upload support

### 🚀 Ready for Testing

- [x] WebSocket Chat Demo page created
- [x] All components exported and ready to use
- [x] Documentation completed

## 📖 Usage Guide

### Basic Usage

```tsx
import { ChatPanelWebSocket } from '@/shared/components/layout/chat-panel';

function App() {
  return (
    <ChatPanelWebSocket
      onClose={() => console.log('Chat closed')}
      roomId="my-room"
      userId="user-123"
      userInfo={{ name: 'John Doe' }}
      websocketConfig={{
        url: 'ws://localhost:3001',
        auth: { token: 'your-auth-token' },
      }}
    />
  );
}
```

### Advanced Usage with Hooks

```tsx
import { useChatWebSocket, useFormSync } from '@/shared/websocket';

function CustomChat() {
  const { messages, sendMessage, isConnected, typingUsers } = useChatWebSocket({
    config: { url: 'ws://localhost:3001' },
    roomId: 'room-123',
    userId: 'user-456',
  });

  const handleSendMessage = async () => {
    await sendMessage({
      type: 'text',
      data: { text: 'Hello World!' },
    });
  };

  return <div>{/* Your custom chat UI */}</div>;
}
```

### Form Integration

```tsx
import { FormMessage } from '@/shared/components/layout/chat-panel';

const formData = {
  formId: 'user-registration',
  title: 'User Registration',
  schema: {
    type: 'object',
    properties: {
      name: { type: 'string', title: 'Full Name', required: true },
      email: { type: 'string', format: 'email', title: 'Email' },
    },
  },
};

<FormMessage data={formData} messageId="msg-123" roomId="room-456" />;
```

## 🧪 Testing

1. **Start the demo page**:

   ```bash
   npm start
   ```

2. **Navigate to**: `/components/websocket-chat-demo`

3. **Configure WebSocket settings** and start chatting

4. **Test different message types**:
   - Text messages
   - Markdown content
   - Forms with real-time sync
   - Navigation buttons
   - Streaming content

## 🔧 Backend Integration

The frontend expects a Socket.IO server with the following events:

### Server Events (Backend → Frontend)

- `message_received` - New message
- `stream_chunk` - Streaming content chunk
- `form_prefill` - Form data prefill
- `user_typing` - Typing indicators

### Client Events (Frontend → Backend)

- `send_message` - Send new message
- `join_room` - Join chat room
- `form_submit` - Submit form data
- `typing_start/stop` - Typing indicators

## 📦 Dependencies Added

```json
{
  "socket.io-client": "^4.7.4",
  "react-markdown": "^9.0.1",
  "react-syntax-highlighter": "^15.5.0",
  "dompurify": "^3.0.5"
}
```

---

**Ngày tạo**: 2024-01-15
**Phiên bản**: 1.0
**Tác giả**: Development Team
**Trạng thái**: ✅ COMPLETED - Ready for Testing
