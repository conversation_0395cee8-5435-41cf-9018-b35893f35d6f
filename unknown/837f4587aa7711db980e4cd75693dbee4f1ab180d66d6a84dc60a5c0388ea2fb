/**
 * Contract Types for Frontend
 */

export enum ContractStatus {
  DRAFT = 'draft',
  UNDER_REVIEW = 'under_review',
  PENDING_APPROVAL = 'pending_approval',
  PENDING_SIGNATURE = 'pending_signature',
  ACTIVE = 'active',
  EXPIRED = 'expired',
  TERMINATED = 'terminated',
  CANCELLED = 'cancelled',
}

export enum ContractType {
  EMPLOYMENT = 'employment',
  SERVICE = 'service',
  PURCHASE = 'purchase',
  LEASE = 'lease',
  NDA = 'nda',
  PARTNERSHIP = 'partnership',
  CUSTOM = 'custom',
}

export enum ContractPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

export interface ContractParty {
  name: string;
  email: string;
  address?: string;
  phone?: string;
  representative?: string;
}

export interface ContractParties {
  party1: ContractParty;
  party2: ContractParty;
}

export interface Contract {
  id: string;
  title: string;
  description?: string;
  type: ContractType;
  status: ContractStatus;
  priority: ContractPriority;
  contractNumber: string;
  value?: number;
  currency?: string;
  startDate?: string;
  endDate?: string;
  signedDate?: string;
  autoRenewal: boolean;
  renewalPeriodDays?: number;
  notificationDaysBefore?: number;
  terms?: string;
  customFields?: Record<string, any>;
  parties?: ContractParties;
  metadata?: Record<string, any>;
  
  // Relations
  companyId: string;
  createdById: string;
  assignedToId?: string;
  templateId?: string;
  
  // Timestamps
  createdAt: string;
  updatedAt: string;
  
  // Virtual fields
  isExpired?: boolean;
  daysUntilExpiry?: number;
  isNearExpiry?: boolean;
  
  // Related data (populated when needed)
  createdBy?: {
    id: string;
    email: string;
    name: string;
  };
  assignedTo?: {
    id: string;
    email: string;
    name: string;
  };
  template?: {
    id: string;
    name: string;
    type: ContractType;
  };
  versions?: ContractVersion[];
  signatures?: ContractSignature[];
  approvals?: ContractApproval[];
  attachments?: ContractAttachment[];
  comments?: ContractComment[];
}

export interface ContractVersion {
  id: string;
  version: string;
  type: 'initial' | 'revision' | 'amendment' | 'renewal';
  content: string;
  changeLog?: string;
  changeReason?: string;
  isActive: boolean;
  isFinal: boolean;
  changes?: {
    field: string;
    oldValue: any;
    newValue: any;
    changeType: 'added' | 'modified' | 'removed';
  }[];
  documentUrl?: string;
  documentHash?: string;
  documentSize?: number;
  metadata?: Record<string, any>;
  contractId: string;
  createdById: string;
  createdAt: string;
  createdBy?: {
    id: string;
    email: string;
    name: string;
  };
}

export interface ContractSignature {
  id: string;
  status: 'pending' | 'signed' | 'declined' | 'expired';
  type: 'electronic' | 'digital' | 'wet';
  signerName: string;
  signerEmail: string;
  signerPhone?: string;
  signerTitle?: string;
  signerCompany?: string;
  signOrder: number;
  required: boolean;
  signatureData?: string;
  signatureHash?: string;
  ipAddress?: string;
  userAgent?: string;
  location?: {
    latitude?: number;
    longitude?: number;
    address?: string;
  };
  signedAt?: string;
  sentAt?: string;
  viewedAt?: string;
  expiresAt?: string;
  declineReason?: string;
  notes?: string;
  externalSignatureId?: string;
  externalData?: Record<string, any>;
  metadata?: Record<string, any>;
  contractId: string;
  userId?: string;
  delegatedById?: string;
  createdAt: string;
  updatedAt: string;
}

export interface ContractApproval {
  id: string;
  status: 'pending' | 'approved' | 'rejected' | 'delegated' | 'expired';
  type: 'legal_review' | 'financial_review' | 'technical_review' | 'management_approval' | 'compliance_check' | 'custom';
  stepOrder: number;
  stepName: string;
  stepDescription?: string;
  required: boolean;
  requiredApprovals: number;
  currentApprovals: number;
  requestedAt?: string;
  respondedAt?: string;
  expiresAt?: string;
  comments?: string;
  rejectionReason?: string;
  conditions?: {
    minContractValue?: number;
    maxContractValue?: number;
    contractTypes?: string[];
    departments?: string[];
    customRules?: Record<string, any>;
  };
  approvalData?: {
    approvedBy: {
      userId: string;
      userName: string;
      approvedAt: string;
      comments?: string;
    }[];
    rejectedBy?: {
      userId: string;
      userName: string;
      rejectedAt: string;
      reason: string;
    };
  };
  autoApproved: boolean;
  autoApprovalRule?: string;
  metadata?: Record<string, any>;
  contractId: string;
  requestedById: string;
  assignedToId?: string;
  delegatedToId?: string;
  createdAt: string;
  updatedAt: string;
}

export interface ContractAttachment {
  id: string;
  fileName: string;
  originalName: string;
  mimeType: string;
  fileSize: number;
  filePath: string;
  fileUrl?: string;
  type: 'document' | 'image' | 'signature' | 'supporting_doc' | 'amendment' | 'other';
  description?: string;
  fileHash?: string;
  isPublic: boolean;
  isRequired: boolean;
  downloadCount: number;
  lastDownloadedAt?: string;
  metadata?: {
    pageCount?: number;
    dimensions?: { width: number; height: number };
    duration?: number;
    extractedText?: string;
    thumbnailUrl?: string;
    [key: string]: any;
  };
  accessControl?: {
    allowedRoles?: string[];
    allowedUsers?: string[];
    requiresApproval?: boolean;
    expiresAt?: string;
  };
  contractId: string;
  uploadedById: string;
  createdAt: string;
}

export interface ContractComment {
  id: string;
  content: string;
  type: 'general' | 'review' | 'approval' | 'rejection' | 'question' | 'suggestion' | 'system';
  isInternal: boolean;
  isResolved: boolean;
  resolvedAt?: string;
  mentions?: {
    userId: string;
    userName: string;
    email: string;
  }[];
  attachments?: {
    fileName: string;
    fileUrl: string;
    fileSize: number;
    mimeType: string;
  }[];
  position?: {
    page?: number;
    x?: number;
    y?: number;
    width?: number;
    height?: number;
    text?: string;
  };
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  metadata?: Record<string, any>;
  contractId: string;
  authorId: string;
  resolvedById?: string;
  parentCommentId?: string;
  createdAt: string;
  updatedAt: string;
  author?: {
    id: string;
    email: string;
    name: string;
  };
}

// Query and mutation types
export interface ContractQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: ContractStatus[];
  type?: ContractType[];
  priority?: ContractPriority[];
  assignedToId?: string;
  createdById?: string;
  startDate?: string;
  endDate?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface ContractCreateDto {
  title: string;
  description?: string;
  type: ContractType;
  priority?: ContractPriority;
  value?: number;
  currency?: string;
  startDate?: string;
  endDate?: string;
  autoRenewal?: boolean;
  renewalPeriodDays?: number;
  notificationDaysBefore?: number;
  terms?: string;
  customFields?: Record<string, any>;
  parties?: ContractParties;
  assignedToId?: string;
  templateId?: string;
  metadata?: Record<string, any>;
}

export interface ContractUpdateDto extends Partial<ContractCreateDto> {
  status?: ContractStatus;
}

export interface ContractListResponse {
  items: Contract[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface ContractStats {
  total: number;
  byStatus: Record<ContractStatus, number>;
  byType: Record<ContractType, number>;
  byPriority: Record<ContractPriority, number>;
  totalValue: number;
  averageValue: number;
  expiringThisMonth: number;
  pendingApprovals: number;
  pendingSignatures: number;
}
