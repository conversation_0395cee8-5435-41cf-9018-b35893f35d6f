import React from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';

import { Card, Typography } from '@/shared/components/common';

/**
 * Contract Detail Page
 * Trang chi tiết hợp đồng
 */
const ContractDetailPage: React.FC = () => {
  const { t } = useTranslation(['common', 'contracts']);
  const { id } = useParams<{ id: string }>();

  return (
    <div className="w-full bg-background text-foreground">
      <div className="mb-6">
        <Typography variant="h1" className="mb-2">
          Chi tiết hợp đồng #{id}
        </Typography>
        <Typography variant="body1" className="text-muted-foreground">
          Xem thông tin chi tiết và quản lý hợp đồng
        </Typography>
      </div>

      <Card className="p-6">
        <div className="text-center py-8">
          <Typography variant="body1" className="text-muted-foreground">
            Trang chi tiết hợp đồng đang được phát triển...
          </Typography>
        </div>
      </Card>
    </div>
  );
};

export default ContractDetailPage;
