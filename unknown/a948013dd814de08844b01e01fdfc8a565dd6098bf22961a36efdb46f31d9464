import React from 'react';
import { useTranslation } from 'react-i18next';

import { Card, Typography } from '@/shared/components/common';

/**
 * Contract Approvals Page
 * Trang phê duyệt hợp đồng
 */
const ContractApprovalsPage: React.FC = () => {
  const { t } = useTranslation(['common', 'contracts']);

  return (
    <div className="w-full bg-background text-foreground">
      <div className="mb-6">
        <Typography variant="h1" className="mb-2">
          {t('contracts:pending_approvals')}
        </Typography>
        <Typography variant="body1" className="text-muted-foreground">
          {t('contracts:pending_approvals_desc')}
        </Typography>
      </div>

      <Card className="p-6">
        <div className="text-center py-8">
          <Typography variant="body1" className="text-muted-foreground">
            Trang phê duyệt hợp đồng đang được phát triển...
          </Typography>
        </div>
      </Card>
    </div>
  );
};

export default ContractApprovalsPage;
