import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';

import { User } from '../../auth/entities/user.entity';
import { Contract } from './contract.entity';

export enum AttachmentType {
  DOCUMENT = 'document',
  IMAGE = 'image',
  SIGNATURE = 'signature',
  SUPPORTING_DOC = 'supporting_doc',
  AMENDMENT = 'amendment',
  OTHER = 'other',
}

/**
 * Entity tài liệu đính kèm hợp đồng
 */
@Entity('contract_attachments')
export class ContractAttachment {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  fileName: string;

  @Column({ type: 'varchar', length: 255 })
  originalName: string;

  @Column({ type: 'varchar', length: 100 })
  mimeType: string;

  @Column({ type: 'int' })
  fileSize: number;

  @Column({ type: 'varchar', length: 500 })
  filePath: string;

  @Column({ type: 'varchar', length: 500, nullable: true })
  fileUrl: string;

  @Column({ type: 'enum', enum: AttachmentType, default: AttachmentType.DOCUMENT })
  type: AttachmentType;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  fileHash: string;

  @Column({ type: 'boolean', default: false })
  isPublic: boolean;

  @Column({ type: 'boolean', default: false })
  isRequired: boolean;

  @Column({ type: 'int', default: 0 })
  downloadCount: number;

  @Column({ type: 'timestamp', nullable: true })
  lastDownloadedAt: Date;

  @Column({ type: 'json', nullable: true })
  metadata: {
    pageCount?: number;
    dimensions?: { width: number; height: number };
    duration?: number; // for video/audio files
    extractedText?: string;
    thumbnailUrl?: string;
    [key: string]: any;
  };

  @Column({ type: 'json', nullable: true })
  accessControl: {
    allowedRoles?: string[];
    allowedUsers?: string[];
    requiresApproval?: boolean;
    expiresAt?: Date;
  };

  // Relations
  @ManyToOne(() => Contract, (contract) => contract.attachments, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'contractId' })
  contract: Contract;

  @Column({ type: 'uuid' })
  contractId: string;

  @ManyToOne(() => User, { nullable: false })
  @JoinColumn({ name: 'uploadedById' })
  uploadedBy: User;

  @Column({ type: 'uuid' })
  uploadedById: string;

  @CreateDateColumn()
  createdAt: Date;

  // Methods
  incrementDownloadCount(): void {
    this.downloadCount += 1;
    this.lastDownloadedAt = new Date();
  }

  isAccessibleBy(userId: string, userRoles: string[]): boolean {
    if (this.isPublic) {
      return true;
    }

    if (!this.accessControl) {
      return true; // No restrictions
    }

    const { allowedRoles, allowedUsers } = this.accessControl;

    // Check if user is explicitly allowed
    if (allowedUsers && allowedUsers.includes(userId)) {
      return true;
    }

    // Check if user has required role
    if (allowedRoles && userRoles.some(role => allowedRoles.includes(role))) {
      return true;
    }

    return false;
  }

  isExpired(): boolean {
    return this.accessControl?.expiresAt && new Date() > this.accessControl.expiresAt;
  }

  getFileInfo(): {
    name: string;
    size: string;
    type: string;
    uploadedAt: Date;
    downloadCount: number;
  } {
    return {
      name: this.originalName,
      size: this.formatFileSize(this.fileSize),
      type: this.mimeType,
      uploadedAt: this.createdAt,
      downloadCount: this.downloadCount,
    };
  }

  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  generateThumbnail(): Promise<string> {
    // Implementation for generating thumbnails
    // This would typically use image processing libraries
    return Promise.resolve('thumbnail-url');
  }

  extractText(): Promise<string> {
    // Implementation for extracting text from documents
    // This would typically use OCR or PDF text extraction libraries
    return Promise.resolve('extracted-text');
  }

  validateFile(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check file size (example: max 50MB)
    const maxSize = 50 * 1024 * 1024; // 50MB
    if (this.fileSize > maxSize) {
      errors.push('File size exceeds maximum limit of 50MB');
    }

    // Check file type
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'image/jpeg',
      'image/png',
      'image/gif',
      'text/plain',
    ];

    if (!allowedTypes.includes(this.mimeType)) {
      errors.push('File type not allowed');
    }

    // Check file hash for integrity
    if (this.fileHash) {
      // Implementation would verify file hash
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}
