import { Suspense, lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import { Loading } from '@/shared/components';
import MainLayout from '@/shared/layouts/MainLayout';

// Import pages with lazy loading
const ContractsHomePage = lazy(() => import('@/modules/contracts/pages/ContractsHomePage'));
const ContractListPage = lazy(() => import('@/modules/contracts/pages/ContractListPage'));
const ContractCreatePage = lazy(() => import('@/modules/contracts/pages/ContractCreatePage'));
const ContractDetailPage = lazy(() => import('@/modules/contracts/pages/ContractDetailPage'));
const ContractEditPage = lazy(() => import('@/modules/contracts/pages/ContractEditPage'));
const ContractSignPage = lazy(() => import('@/modules/contracts/pages/ContractSignPage'));
const ContractTemplatesPage = lazy(() => import('@/modules/contracts/pages/ContractTemplatesPage'));
const ContractApprovalsPage = lazy(() => import('@/modules/contracts/pages/ContractApprovalsPage'));
const ContractReportsPage = lazy(() => import('@/modules/contracts/pages/ContractReportsPage'));

/**
 * Contract module routes
 */
const contractRoutes: RouteObject[] = [
  {
    path: '/contracts',
    element: (
      <MainLayout title="Quản lý hợp đồng">
        <Suspense fallback={<Loading />}>
          <ContractsHomePage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/contracts/list',
    element: (
      <MainLayout title="Danh sách hợp đồng">
        <Suspense fallback={<Loading />}>
          <ContractListPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/contracts/create',
    element: (
      <MainLayout title="Tạo hợp đồng mới">
        <Suspense fallback={<Loading />}>
          <ContractCreatePage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/contracts/templates',
    element: (
      <MainLayout title="Mẫu hợp đồng">
        <Suspense fallback={<Loading />}>
          <ContractTemplatesPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/contracts/approvals',
    element: (
      <MainLayout title="Phê duyệt hợp đồng">
        <Suspense fallback={<Loading />}>
          <ContractApprovalsPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/contracts/reports',
    element: (
      <MainLayout title="Báo cáo hợp đồng">
        <Suspense fallback={<Loading />}>
          <ContractReportsPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/contracts/:id',
    element: (
      <MainLayout title="Chi tiết hợp đồng">
        <Suspense fallback={<Loading />}>
          <ContractDetailPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/contracts/:id/edit',
    element: (
      <MainLayout title="Chỉnh sửa hợp đồng">
        <Suspense fallback={<Loading />}>
          <ContractEditPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/contracts/:id/sign',
    element: (
      <MainLayout title="Ký hợp đồng">
        <Suspense fallback={<Loading />}>
          <ContractSignPage />
        </Suspense>
      </MainLayout>
    ),
  },
];

export default contractRoutes;
