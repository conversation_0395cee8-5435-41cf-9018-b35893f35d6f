import React from 'react';
import { useTranslation } from 'react-i18next';

import { Card, Typography } from '@/shared/components/common';

/**
 * Contract Stats Component
 * Component thống kê hợp đồng
 */
const ContractStats: React.FC = () => {
  const { t } = useTranslation(['common', 'contracts']);

  return (
    <Card className="p-6">
      <Typography variant="h2" className="mb-4">
        {t('contracts:quick_stats')}
      </Typography>
      <div className="text-center py-8">
        <Typography variant="body1" className="text-muted-foreground">
          Thống kê hợp đồng đang được phát triển...
        </Typography>
      </div>
    </Card>
  );
};

export default ContractStats;
