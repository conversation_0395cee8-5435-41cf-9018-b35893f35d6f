import React from 'react';
import { useTranslation } from 'react-i18next';

import { Card, Typography } from '@/shared/components/common';

/**
 * Contract Create Page
 * Trang tạo hợp đồng mới
 */
const ContractCreatePage: React.FC = () => {
  const { t } = useTranslation(['common', 'contracts']);

  return (
    <div className="w-full bg-background text-foreground">
      <div className="mb-6">
        <Typography variant="h1" className="mb-2">
          {t('contracts:create_contract')}
        </Typography>
        <Typography variant="body1" className="text-muted-foreground">
          {t('contracts:create_contract_desc')}
        </Typography>
      </div>

      <Card className="p-6">
        <div className="text-center py-8">
          <Typography variant="body1" className="text-muted-foreground">
            Trang tạo hợp đồng đang được phát triển...
          </Typography>
        </div>
      </Card>
    </div>
  );
};

export default ContractCreatePage;
