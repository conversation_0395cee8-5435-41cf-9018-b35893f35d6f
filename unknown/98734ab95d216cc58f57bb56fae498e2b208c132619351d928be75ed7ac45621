import React, { useMemo } from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import {
  startOfDay,
  endOfDay,
  startOfWeek,
  endOfWeek,
  startOfMonth,
  endOfMonth,
  startOfYear,
  endOfYear,
  subDays,
  subWeeks,
  subMonths,
  subYears,
  addWeeks,
  addMonths,
} from 'date-fns';

/**
 * Interface cho preset range
 */
export interface PresetRange {
  id: string;
  label: string;
  value: [Date, Date];
  shortcut?: string;
  category?: string;
  icon?: React.ReactNode;
}

/**
 * Props cho PresetRanges component
 */
export interface PresetRangesProps {
  /**
   * Callback khi chọn preset range
   */
  onSelectRange: (range: [Date, Date]) => void;
  
  /**
   * Range hiện tại đã chọn
   */
  selectedRange?: [Date | null, Date | null];
  
  /**
   * Custom preset ranges
   */
  customPresets?: PresetRange[];
  
  /**
   * Hiển thị preset ranges mặc định
   */
  showDefaultPresets?: boolean;
  
  /**
   * Hiển thị theo categories
   */
  showCategories?: boolean;
  
  /**
   * Layout direction
   */
  direction?: 'vertical' | 'horizontal';
  
  /**
   * Compact mode
   */
  compact?: boolean;
  
  /**
   * Enable animations
   */
  enableAnimations?: boolean;
  
  /**
   * Custom className
   */
  className?: string;
}

/**
 * Default preset ranges
 */
const getDefaultPresets = (t: (key: string, fallback: string) => string): PresetRange[] => {
  const today = new Date();
  
  return [
    // Today & Yesterday
    {
      id: 'today',
      label: t('dateRange.today', 'Today'),
      value: [startOfDay(today), endOfDay(today)],
      shortcut: 'T',
      category: 'recent',
    },
    {
      id: 'yesterday',
      label: t('dateRange.yesterday', 'Yesterday'),
      value: [startOfDay(subDays(today, 1)), endOfDay(subDays(today, 1))],
      shortcut: 'Y',
      category: 'recent',
    },
    
    // This Week & Month
    {
      id: 'thisWeek',
      label: t('dateRange.thisWeek', 'This Week'),
      value: [startOfWeek(today, { weekStartsOn: 1 }), endOfWeek(today, { weekStartsOn: 1 })],
      shortcut: 'W',
      category: 'current',
    },
    {
      id: 'thisMonth',
      label: t('dateRange.thisMonth', 'This Month'),
      value: [startOfMonth(today), endOfMonth(today)],
      shortcut: 'M',
      category: 'current',
    },
    {
      id: 'thisYear',
      label: t('dateRange.thisYear', 'This Year'),
      value: [startOfYear(today), endOfYear(today)],
      shortcut: 'Shift+Y',
      category: 'current',
    },
    
    // Last periods
    {
      id: 'lastWeek',
      label: t('dateRange.lastWeek', 'Last Week'),
      value: [
        startOfWeek(subWeeks(today, 1), { weekStartsOn: 1 }),
        endOfWeek(subWeeks(today, 1), { weekStartsOn: 1 })
      ],
      category: 'previous',
    },
    {
      id: 'lastMonth',
      label: t('dateRange.lastMonth', 'Last Month'),
      value: [startOfMonth(subMonths(today, 1)), endOfMonth(subMonths(today, 1))],
      category: 'previous',
    },
    {
      id: 'lastYear',
      label: t('dateRange.lastYear', 'Last Year'),
      value: [startOfYear(subYears(today, 1)), endOfYear(subYears(today, 1))],
      category: 'previous',
    },
    
    // Last N days
    {
      id: 'last7Days',
      label: t('dateRange.last7Days', 'Last 7 Days'),
      value: [startOfDay(subDays(today, 6)), endOfDay(today)],
      shortcut: '7',
      category: 'rolling',
    },
    {
      id: 'last30Days',
      label: t('dateRange.last30Days', 'Last 30 Days'),
      value: [startOfDay(subDays(today, 29)), endOfDay(today)],
      shortcut: '3',
      category: 'rolling',
    },
    {
      id: 'last90Days',
      label: t('dateRange.last90Days', 'Last 90 Days'),
      value: [startOfDay(subDays(today, 89)), endOfDay(today)],
      shortcut: '9',
      category: 'rolling',
    },
    
    // Next periods
    {
      id: 'nextWeek',
      label: t('dateRange.nextWeek', 'Next Week'),
      value: [
        startOfWeek(addWeeks(today, 1), { weekStartsOn: 1 }),
        endOfWeek(addWeeks(today, 1), { weekStartsOn: 1 })
      ],
      category: 'future',
    },
    {
      id: 'nextMonth',
      label: t('dateRange.nextMonth', 'Next Month'),
      value: [startOfMonth(addMonths(today, 1)), endOfMonth(addMonths(today, 1))],
      category: 'future',
    },
  ];
};

/**
 * Category labels
 */
const getCategoryLabels = (t: (key: string, fallback: string) => string) => ({
  recent: t('dateRange.categories.recent', 'Recent'),
  current: t('dateRange.categories.current', 'Current Period'),
  previous: t('dateRange.categories.previous', 'Previous Period'),
  rolling: t('dateRange.categories.rolling', 'Rolling Period'),
  future: t('dateRange.categories.future', 'Future'),
  custom: t('dateRange.categories.custom', 'Custom'),
});

/**
 * PresetRanges component
 */
const PresetRanges: React.FC<PresetRangesProps> = ({
  onSelectRange,
  selectedRange,
  customPresets = [],
  showDefaultPresets = true,
  showCategories = true,
  direction = 'vertical',
  compact = false,
  enableAnimations = true,
  className = '',
}) => {
  const { t } = useTranslation();

  // Combine default and custom presets
  const allPresets = useMemo(() => {
    const presets: PresetRange[] = [];
    
    if (showDefaultPresets) {
      presets.push(...getDefaultPresets(t));
    }
    
    if (customPresets.length > 0) {
      presets.push(...customPresets.map(preset => ({
        ...preset,
        category: preset.category || 'custom',
      })));
    }
    
    return presets;
  }, [showDefaultPresets, customPresets, t]);

  // Group presets by category
  const presetsByCategory = useMemo(() => {
    if (!showCategories) {
      return { all: allPresets };
    }

    const grouped: Record<string, PresetRange[]> = {};
    
    allPresets.forEach(preset => {
      const category = preset.category || 'custom';
      if (!grouped[category]) {
        grouped[category] = [];
      }
      grouped[category].push(preset);
    });
    
    return grouped;
  }, [allPresets, showCategories]);

  // Check if a preset is selected
  const isPresetSelected = (preset: PresetRange): boolean => {
    if (!selectedRange || !selectedRange[0] || !selectedRange[1]) return false;
    
    const [start, end] = preset.value;
    const [selectedStart, selectedEnd] = selectedRange;
    
    return (
      start.getTime() === selectedStart.getTime() &&
      end.getTime() === selectedEnd.getTime()
    );
  };

  // Handle preset selection
  const handlePresetSelect = (preset: PresetRange) => {
    onSelectRange(preset.value);
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.05,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, x: -10 },
    visible: { opacity: 1, x: 0 },
  };

  // Container classes
  const containerClasses = `
    ${direction === 'horizontal' ? 'flex flex-wrap gap-2' : 'space-y-1'}
    ${compact ? 'text-sm' : ''}
    ${className}
  `.trim();

  // Category labels
  const categoryLabels = getCategoryLabels(t);

  // Render preset button
  const renderPresetButton = (preset: PresetRange) => {
    const isSelected = isPresetSelected(preset);
    
    const buttonClasses = `
      w-full text-left px-3 py-2 rounded-md transition-colors duration-200
      ${compact ? 'px-2 py-1 text-sm' : ''}
      ${isSelected 
        ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300' 
        : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'
      }
      ${direction === 'horizontal' ? 'whitespace-nowrap' : ''}
    `.trim();

    const ButtonComponent = enableAnimations ? motion.button : 'button';
    const buttonProps = enableAnimations ? {
      variants: itemVariants,
      whileHover: { scale: 1.02 },
      whileTap: { scale: 0.98 },
    } : {};

    return (
      <ButtonComponent
        key={preset.id}
        className={buttonClasses}
        onClick={() => handlePresetSelect(preset)}
        title={preset.shortcut ? `Shortcut: ${preset.shortcut}` : undefined}
        {...buttonProps}
      >
        <div className="flex items-center justify-between">
          <span className="flex items-center gap-2">
            {preset.icon}
            {preset.label}
          </span>
          {preset.shortcut && !compact && (
            <span className="text-xs text-gray-400 dark:text-gray-500">
              {preset.shortcut}
            </span>
          )}
        </div>
      </ButtonComponent>
    );
  };

  // Render content
  const renderContent = () => {
    if (!showCategories) {
      return allPresets.map(renderPresetButton);
    }

    return Object.entries(presetsByCategory).map(([category, presets]) => (
      <div key={category} className={direction === 'horizontal' ? 'flex flex-col' : ''}>
        {showCategories && (
          <h4 className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-2 mt-4 first:mt-0">
            {(categoryLabels as Record<string, string>)[category] || category}
          </h4>
        )}
        <div className={direction === 'horizontal' ? 'flex flex-wrap gap-1' : 'space-y-1'}>
          {presets.map(renderPresetButton)}
        </div>
      </div>
    ));
  };

  const ContainerComponent = enableAnimations ? motion.div : 'div';
  const containerProps = enableAnimations ? {
    variants: containerVariants,
    initial: "hidden",
    animate: "visible",
  } : {};

  return (
    <ContainerComponent
      className={containerClasses}
      {...containerProps}
    >
      {renderContent()}
    </ContainerComponent>
  );
};

export default PresetRanges;
