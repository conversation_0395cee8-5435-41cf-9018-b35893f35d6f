/**
 * Utility functions for handling date conversions in forms
 */

/**
 * Convert Date object to string in YYYY-MM-DD format for API
 * @param date Date object or string
 * @returns String in YYYY-MM-DD format or original value if not a Date
 */
export const dateToApiString = (date: Date | string | null | undefined): string | null | undefined => {
  if (!date) return date as null | undefined;
  if (typeof date === 'string') return date;
  if (date instanceof Date) {
    try {
      return date.toISOString().split('T')[0];
    } catch {
      return null;
    }
  }
  return null;
};

/**
 * Convert string date to Date object for form display
 * @param dateString String date or Date object
 * @returns Date object or original value if not a string
 */
export const stringToFormDate = (dateString: string | Date | null | undefined): Date | string | null | undefined => {
  if (!dateString) return dateString as null | undefined;
  if (dateString instanceof Date) return dateString;
  if (typeof dateString === 'string') {
    try {
      const date = new Date(dateString);
      // Check if date is valid
      if (isNaN(date.getTime())) return null;
      return date;
    } catch {
      return null;
    }
  }
  return null;
};

/**
 * Transform form data with date fields for API submission
 * @param data Form data object
 * @param dateFields Array of field names that contain dates
 * @returns Transformed data with dates as strings
 */
export const transformFormDatesForApi = <T extends Record<string, any>>(
  data: T,
  dateFields: (keyof T)[]
): T => {
  const transformed = { ...data };
  
  dateFields.forEach(field => {
    if (transformed[field]) {
      transformed[field] = dateToApiString(transformed[field]) as T[keyof T];
    }
  });
  
  return transformed;
};

/**
 * Transform API data with date fields for form display
 * @param data API data object
 * @param dateFields Array of field names that contain dates
 * @returns Transformed data with dates as Date objects
 */
export const transformApiDatesForForm = <T extends Record<string, any>>(
  data: T,
  dateFields: (keyof T)[]
): T => {
  const transformed = { ...data };
  
  dateFields.forEach(field => {
    if (transformed[field]) {
      transformed[field] = stringToFormDate(transformed[field]) as T[keyof T];
    }
  });
  
  return transformed;
};

/**
 * Employee-specific date field names
 */
export const EMPLOYEE_DATE_FIELDS = ['hireDate', 'probationEndDate', 'dateOfBirth'] as const;

/**
 * Transform employee form data for API submission
 * @param data Employee form data
 * @returns Transformed data with dates as strings
 */
export const transformEmployeeFormForApi = <T extends Record<string, any>>(data: T): T => {
  return transformFormDatesForApi(data, EMPLOYEE_DATE_FIELDS);
};

/**
 * Transform employee API data for form display
 * @param data Employee API data
 * @returns Transformed data with dates as Date objects
 */
export const transformEmployeeApiForForm = <T extends Record<string, any>>(data: T): T => {
  return transformApiDatesForForm(data, EMPLOYEE_DATE_FIELDS);
};
