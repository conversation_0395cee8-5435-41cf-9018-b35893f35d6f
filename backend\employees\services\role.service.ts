import { plainToInstance } from 'class-transformer';

import { AppException } from '@/common/exceptions/app.exception';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { Injectable, Logger } from '@nestjs/common';

import { HRM_ERROR_CODES } from '../../errors/hrm-error.code';
import { RoleQueryDto, RoleListItemDto, RoleDetailDto } from '../dto/role/role-list.dto';
import { RoleRepository } from '../repositories/role.repository';

/**
 * Service quản lý vai trò
 */
@Injectable()
export class RoleService {
  private readonly logger = new Logger(RoleService.name);

  constructor(
    private readonly roleRepository: RoleRepository,
  ) {}

  /**
   * L<PERSON>y danh sách vai trò với phân trang và tìm kiếm
   * @param query Tham số truy vấn
   * @returns Danh sách vai trò phân trang
   */
  async findAll(query: RoleQueryDto): Promise<PaginatedResult<RoleListItemDto>> {
    try {
      const result = await this.roleRepository.findAll(query);
      
      // Chuyển đổi sang DTO
      const items = result.items.map(role => plainToInstance(
        RoleListItemDto, 
        {
          id: role.id,
          name: role.name,
          description: role.description,
          type: role.type,
          createdAt: role.createdAt,
        },
        { excludeExtraneousValues: true }
      ));
      
      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(`Error finding roles: ${error.message}`, error.stack);
      throw new AppException(
        HRM_ERROR_CODES.ROLE_FETCH_FAILED,
        `Không thể lấy danh sách vai trò: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thông tin chi tiết của vai trò bao gồm danh sách quyền
   * @param id ID của vai trò
   * @returns Thông tin chi tiết của vai trò
   */
  async findById(id: number): Promise<RoleDetailDto> {
    try {
      // Lấy thông tin vai trò
      const role = await this.roleRepository.findById(id);
      if (!role) {
        throw new AppException(
          HRM_ERROR_CODES.ROLE_NOT_FOUND,
          `Không tìm thấy vai trò với ID ${id}`,
        );
      }
      
      // Lấy danh sách quyền của vai trò
      const { permissionIds, permissions } = await this.roleRepository.getRolePermissions(id);
      
      // Tạo DTO
      return plainToInstance(
        RoleDetailDto,
        {
          id: role.id,
          name: role.name,
          description: role.description,
          type: role.type,
          createdAt: role.createdAt,
          permissionIds,
          permissions,
        },
        { excludeExtraneousValues: true }
      );
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error finding role: ${error.message}`, error.stack);
      throw new AppException(
        HRM_ERROR_CODES.ROLE_FETCH_FAILED,
        `Không thể lấy thông tin vai trò: ${error.message}`,
      );
    }
  }
}
