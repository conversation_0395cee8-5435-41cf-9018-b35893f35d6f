import React from 'react';
import { CalendarThemeProvider, CalendarThemeProviderProps } from '../CalendarThemeProvider';

/**
 * HOC để wrap component với CalendarThemeProvider
 */
export const withCalendarTheme = <P extends object>(
  Component: React.ComponentType<P>,
  themeOptions?: Omit<CalendarThemeProviderProps, 'children'>
) => {
  const WrappedComponent = (props: P) => (
    <CalendarThemeProvider {...themeOptions}>
      <Component {...props} />
    </CalendarThemeProvider>
  );

  WrappedComponent.displayName = `withCalendarTheme(${Component.displayName || Component.name})`;

  return WrappedComponent;
};
