import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

/**
 * DTO cho cập nhật avatar của người dùng
 */
export class UpdateUserAvatarDto {
  /**
   * Key của avatar trên S3/Cloudflare R2
   * @example "users/123/avatar/1234567890.jpg"
   */
  @ApiProperty({
    description: 'Key của avatar trên S3/Cloudflare R2',
    example: 'users/123/avatar/1234567890.jpg',
  })
  @IsNotEmpty({ message: 'Key avatar không được để trống' })
  @IsString({ message: 'Key avatar phải là chuỗi' })
  avatarKey: string;
}

/**
 * DTO cho phản hồi khi tạo URL upload avatar
 */
export class AvatarUploadUrlResponseDto {
  /**
   * URL tạm thời để upload avatar
   * @example "https://storage.example.com/users/123/avatar/1234567890.jpg?signature=abc123&expires=1234567890"
   */
  @ApiProperty({
    description: 'URL tạm thời để upload avatar',
    example:
      'https://storage.example.com/users/123/avatar/1234567890.jpg?signature=abc123&expires=1234567890',
  })
  uploadUrl: string;

  /**
   * Key của avatar trên S3/Cloudflare R2 (cần lưu lại để cập nhật vào database sau khi upload thành công)
   * @example "users/123/avatar/1234567890.jpg"
   */
  @ApiProperty({
    description:
      'Key của avatar trên S3/Cloudflare R2 (cần lưu lại để cập nhật vào database sau khi upload thành công)',
    example: 'users/123/avatar/1234567890.jpg',
  })
  key: string;
}
