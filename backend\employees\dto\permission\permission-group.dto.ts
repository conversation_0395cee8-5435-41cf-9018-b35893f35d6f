import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO đại diện cho một quyền trong hệ thống
 */
export class PermissionDto {
  /**
   * Định danh duy nhất của quyền
   */
  @ApiProperty({
    description: 'Định danh duy nhất của quyền',
    example: 1,
  })
  id: number;

  /**
   * Tên module của quyền
   */
  @ApiProperty({
    description: 'Tên module của quyền',
    example: 'user',
  })
  module: string;

  /**
   * Hành động của quyền
   */
  @ApiProperty({
    description: 'Hành động của quyền',
    example: 'view_list',
  })
  action: string;

  /**
   * Mô tả chi tiết về quyền
   */
  @ApiProperty({
    description: '<PERSON>ô tả chi tiết về quyền',
    example: 'Xem danh sách người dùng',
    nullable: true,
  })
  description: string | null;

  /**
   * Chuỗi đại diện cho quyền theo định dạng 'module:action'
   */
  @ApiProperty({
    description: 'Chuỗi đại diện cho quyền theo định dạng module:action',
    example: 'user:view_list',
  })
  key: string;
}

/**
 * DTO đại diện cho một nhóm quyền theo module
 */
export class PermissionGroupDto {
  /**
   * Tên module
   */
  @ApiProperty({
    description: 'Tên module',
    example: 'user',
  })
  module: string;

  /**
   * Danh sách các quyền thuộc module
   */
  @ApiProperty({
    description: 'Danh sách các quyền thuộc module',
    type: [PermissionDto],
  })
  permissions: PermissionDto[];
}

/**
 * DTO đại diện cho danh sách các nhóm quyền
 */
export class PermissionGroupsResponseDto {
  /**
   * Danh sách các nhóm quyền theo module
   */
  @ApiProperty({
    description: 'Danh sách các nhóm quyền theo module',
    type: [PermissionGroupDto],
  })
  groups: PermissionGroupDto[];
}

/**
 * DTO đại diện cho danh sách quyền của người dùng
 */
export class UserPermissionsResponseDto {
  /**
   * ID của người dùng
   */
  @ApiProperty({
    description: 'ID của người dùng',
    example: 1,
  })
  userId: number;

  /**
   * Danh sách các quyền của người dùng theo định dạng 'module:action'
   */
  @ApiProperty({
    description: 'Danh sách các quyền của người dùng theo định dạng module:action',
    example: ['user:view_list', 'user:view_detail'],
    type: [String],
  })
  permissions: string[];
}
