/**
 * Service cho module Recruitment
 */
import { apiClient } from '@/shared/api/axios';
import { ApiResponse } from '@/shared/types/api.types';

import {
  JobPosition,
  Candidate,
  RecruitmentProcess,
  RecruitmentStats,
  CreateJobPositionDto,
  UpdateJobPositionDto,
  CreateCandidateDto,
  UpdateCandidateDto,
  JobPositionQueryDto,
  CandidateQueryDto,
  JobPositionResponse,
  CandidateResponse,
} from '../types/recruitment.types';

/**
 * Service cho quản lý vị trí tuyển dụng
 */
export class JobPositionService {
  private static readonly BASE_URL = '/hrm/job-positions';

  /**
   * <PERSON><PERSON>y danh sách vị trí tuyển dụng
   */
  static async getJobPositions(params?: JobPositionQueryDto): Promise<ApiResponse<JobPositionResponse>> {
    const response = await apiClient.get<ApiResponse<JobPositionResponse>>(this.BASE_URL, {
      params,
    });
    return response.data;
  }

  /**
   * Lấy chi tiết vị trí tuyển dụng
   */
  static async getJobPosition(id: string): Promise<ApiResponse<JobPosition>> {
    const response = await apiClient.get<ApiResponse<JobPosition>>(`${this.BASE_URL}/${id}`);
    return response.data;
  }

  /**
   * Tạo vị trí tuyển dụng mới
   */
  static async createJobPosition(data: CreateJobPositionDto): Promise<ApiResponse<JobPosition>> {
    const response = await apiClient.post<ApiResponse<JobPosition>>(this.BASE_URL, data);
    return response.data;
  }

  /**
   * Cập nhật vị trí tuyển dụng
   */
  static async updateJobPosition(id: string, data: UpdateJobPositionDto): Promise<ApiResponse<JobPosition>> {
    const response = await apiClient.put<ApiResponse<JobPosition>>(`${this.BASE_URL}/${id}`, data);
    return response.data;
  }

  /**
   * Xóa vị trí tuyển dụng
   */
  static async deleteJobPosition(id: string): Promise<ApiResponse<void>> {
    const response = await apiClient.delete<ApiResponse<void>>(`${this.BASE_URL}/${id}`);
    return response.data;
  }

  /**
   * Publish vị trí tuyển dụng
   */
  static async publishJobPosition(id: string): Promise<ApiResponse<JobPosition>> {
    const response = await apiClient.post<ApiResponse<JobPosition>>(`${this.BASE_URL}/${id}/publish`);
    return response.data;
  }

  /**
   * Close vị trí tuyển dụng
   */
  static async closeJobPosition(id: string): Promise<ApiResponse<JobPosition>> {
    const response = await apiClient.post<ApiResponse<JobPosition>>(`${this.BASE_URL}/${id}/close`);
    return response.data;
  }
}

/**
 * Service cho quản lý ứng viên
 */
export class CandidateService {
  private static readonly BASE_URL = '/hrm/candidates';

  /**
   * Lấy danh sách ứng viên
   */
  static async getCandidates(params?: CandidateQueryDto): Promise<ApiResponse<CandidateResponse>> {
    const response = await apiClient.get<ApiResponse<CandidateResponse>>(this.BASE_URL, {
      params,
    });
    return response.data;
  }

  /**
   * Lấy chi tiết ứng viên
   */
  static async getCandidate(id: string): Promise<ApiResponse<Candidate>> {
    const response = await apiClient.get<ApiResponse<Candidate>>(`${this.BASE_URL}/${id}`);
    return response.data;
  }

  /**
   * Tạo ứng viên mới
   */
  static async createCandidate(data: CreateCandidateDto): Promise<ApiResponse<Candidate>> {
    const response = await apiClient.post<ApiResponse<Candidate>>(this.BASE_URL, data);
    return response.data;
  }

  /**
   * Cập nhật ứng viên
   */
  static async updateCandidate(id: string, data: UpdateCandidateDto): Promise<ApiResponse<Candidate>> {
    const response = await apiClient.put<ApiResponse<Candidate>>(`${this.BASE_URL}/${id}`, data);
    return response.data;
  }

  /**
   * Xóa ứng viên
   */
  static async deleteCandidate(id: string): Promise<ApiResponse<void>> {
    const response = await apiClient.delete<ApiResponse<void>>(`${this.BASE_URL}/${id}`);
    return response.data;
  }

  /**
   * Cập nhật trạng thái ứng viên
   */
  static async updateCandidateStatus(id: string, status: string): Promise<ApiResponse<Candidate>> {
    const response = await apiClient.patch<ApiResponse<Candidate>>(`${this.BASE_URL}/${id}/status`, {
      status,
    });
    return response.data;
  }

  /**
   * Upload CV cho ứng viên
   */
  static async uploadResume(id: string, file: File): Promise<ApiResponse<{ resumeUrl: string }>> {
    const formData = new FormData();
    formData.append('resume', file);

    const response = await apiClient.post<ApiResponse<{ resumeUrl: string }>>(
      `${this.BASE_URL}/${id}/resume`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    return response.data;
  }
}

/**
 * Service cho quản lý quy trình tuyển dụng
 */
export class RecruitmentProcessService {
  private static readonly BASE_URL = '/hrm/recruitment-processes';

  /**
   * Lấy danh sách quy trình tuyển dụng
   */
  static async getRecruitmentProcesses(): Promise<ApiResponse<RecruitmentProcess[]>> {
    const response = await apiClient.get<ApiResponse<RecruitmentProcess[]>>(this.BASE_URL);
    return response.data;
  }

  /**
   * Lấy chi tiết quy trình tuyển dụng
   */
  static async getRecruitmentProcess(id: string): Promise<ApiResponse<RecruitmentProcess>> {
    const response = await apiClient.get<ApiResponse<RecruitmentProcess>>(`${this.BASE_URL}/${id}`);
    return response.data;
  }

  /**
   * Tạo quy trình tuyển dụng mới
   */
  static async createRecruitmentProcess(data: Partial<RecruitmentProcess>): Promise<ApiResponse<RecruitmentProcess>> {
    const response = await apiClient.post<ApiResponse<RecruitmentProcess>>(this.BASE_URL, data);
    return response.data;
  }

  /**
   * Cập nhật quy trình tuyển dụng
   */
  static async updateRecruitmentProcess(id: string, data: Partial<RecruitmentProcess>): Promise<ApiResponse<RecruitmentProcess>> {
    const response = await apiClient.put<ApiResponse<RecruitmentProcess>>(`${this.BASE_URL}/${id}`, data);
    return response.data;
  }

  /**
   * Xóa quy trình tuyển dụng
   */
  static async deleteRecruitmentProcess(id: string): Promise<ApiResponse<void>> {
    const response = await apiClient.delete<ApiResponse<void>>(`${this.BASE_URL}/${id}`);
    return response.data;
  }
}

/**
 * Service cho thống kê tuyển dụng
 */
export class RecruitmentStatsService {
  private static readonly BASE_URL = '/hrm/recruitment/stats';

  /**
   * Lấy thống kê tổng quan
   */
  static async getRecruitmentStats(): Promise<ApiResponse<RecruitmentStats>> {
    const response = await apiClient.get<ApiResponse<RecruitmentStats>>(this.BASE_URL);
    return response.data;
  }

  /**
   * Lấy thống kê theo khoảng thời gian
   */
  static async getRecruitmentStatsByDateRange(startDate: string, endDate: string): Promise<ApiResponse<RecruitmentStats>> {
    const response = await apiClient.get<ApiResponse<RecruitmentStats>>(`${this.BASE_URL}/date-range`, {
      params: { startDate, endDate },
    });
    return response.data;
  }
}
