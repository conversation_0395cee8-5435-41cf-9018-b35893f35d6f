import { IsOptional, IsString } from 'class-validator';

import { QueryDto } from '@/common/dto/query.dto';
import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho truy vấn danh sách vai trò
 */
export class RoleQueryDto extends QueryDto {
  @ApiProperty({
    description: 'Tên vai trò',
    required: false,
    example: 'Admin',
  })
  @IsOptional()
  @IsString()
  name?: string;
}

/**
 * DTO đại diện cho thông tin vai trò trong danh sách
 */
export class RoleListItemDto {
  /**
   * ID của vai trò
   */
  @ApiProperty({
    description: 'ID của vai trò',
    example: 1,
  })
  id: number;

  /**
   * Tên vai trò
   */
  @ApiProperty({
    description: 'Tên vai trò',
    example: 'Admin',
  })
  name: string;

  /**
   * <PERSON>ô tả vai trò
   */
  @ApiProperty({
    description: '<PERSON><PERSON> tả vai trò',
    example: 'Quản trị viên với tất cả quyền',
    nullable: true,
  })
  description: string | null;

  /**
   * Loại vai trò
   */
  @ApiProperty({
    description: 'Loại vai trò',
    example: 'ADMIN',
    nullable: true,
  })
  type: string | null;

  /**
   * Thời gian tạo vai trò (timestamp)
   */
  @ApiProperty({
    description: 'Thời gian tạo vai trò (timestamp)',
    example: 1625097600000,
    nullable: true,
  })
  createdAt: number | null;
}

/**
 * DTO đại diện cho thông tin chi tiết của vai trò bao gồm danh sách quyền
 */
export class RoleDetailDto extends RoleListItemDto {
  /**
   * Danh sách ID của các quyền thuộc vai trò
   */
  @ApiProperty({
    description: 'Danh sách ID của các quyền thuộc vai trò',
    example: [1, 2, 3],
    type: [Number],
  })
  permissionIds: number[];

  /**
   * Danh sách các quyền của vai trò theo định dạng 'module:action'
   */
  @ApiProperty({
    description: 'Danh sách các quyền của vai trò theo định dạng module:action',
    example: ['user:view_list', 'user:view_detail'],
    type: [String],
  })
  permissions: string[];
}
