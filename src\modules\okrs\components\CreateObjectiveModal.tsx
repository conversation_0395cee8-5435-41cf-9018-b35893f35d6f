import React from 'react';
import { useTranslation } from 'react-i18next';

import { Modal } from '@/shared/components/common';

import ObjectiveForm from './ObjectiveForm';

export interface CreateObjectiveModalProps {
  /**
   * Trạng thái hiển thị của modal
   */
  isOpen: boolean;

  /**
   * Callback khi đóng modal
   */
  onClose: () => void;

  /**
   * Callback khi tạo objective thành công
   */
  onSuccess?: () => void;

  /**
   * ID của chu kỳ OKR mặc định (nếu có)
   */
  defaultCycleId?: string;
}

/**
 * Modal tạo mới Objective
 */
const CreateObjectiveModal: React.FC<CreateObjectiveModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  defaultCycleId,
}) => {
  const { t } = useTranslation();

  // Xử lý khi tạo objective thành công
  const handleSuccess = () => {
    // Đóng modal
    onClose();

    // Gọi callback onSuccess nếu có
    if (onSuccess) {
      onSuccess();
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={t('okrs:objective.create.title', 'Tạo mục tiêu mới')}
      size="lg"
    >
      <ObjectiveForm
        onSuccess={handleSuccess}
        onCancel={onClose}
        defaultCycleId={defaultCycleId}
      />
    </Modal>
  );
};

export default CreateObjectiveModal;
