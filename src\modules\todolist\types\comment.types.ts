/**
 * Interface for Comment
 */
export interface CommentDto {
  id: number;
  taskId: number;
  userId: number;
  content: string;
  createdAt: number | null;
  updatedAt: number | null;
  userName?: string;
  userAvatar?: string;
}

/**
 * Interface for Comment Query
 */
export interface CommentQueryDto {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
  taskId?: number;
}

/**
 * Interface for Create Comment
 */
export interface CreateCommentDto {
  taskId: number;
  content: string;
}

/**
 * Interface for Update Comment
 */
export interface UpdateCommentDto {
  content: string;
}
