import { Country, getCountrySvgFlagVector } from '@/shared/data/countries';
import React, { useState } from 'react';

export interface CountryFlagProps {
  country: Country;
  className?: string;
  style?: React.CSSProperties;
  size?: 'sm' | 'md' | 'lg';
  showFallback?: boolean;
  useSvg?: boolean; // Ưu tiên sử dụng SVG thay vì emoji
}

/**
 * Component hiển thị flag quốc gia với SVG fallback
 */
const CountryFlag: React.FC<CountryFlagProps> = ({
  country,
  className = '',
  style = {},
  size = 'md',
  showFallback = true,
  useSvg = true, // Mặc định sử dụng SVG
}) => {
  const [svgError, setSvgError] = useState(false);

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'w-4 h-4';
      case 'lg':
        return 'w-6 h-6';
      default:
        return 'w-5 h-5';
    }
  };

  const getEmojiSize = () => {
    switch (size) {
      case 'sm':
        return '14px';
      case 'lg':
        return '20px';
      default:
        return '18px';
    }
  };

  // Xử lý lỗi SVG
  const handleSvgError = () => {
    setSvgError(true);
  };

  // Nếu sử dụng SVG và chưa có lỗi
  if (useSvg && !svgError) {
    const svgUrl = country.svgFlag || getCountrySvgFlagVector(country.code);
    return (
      <img
        src={svgUrl}
        alt={`${country.name} flag`}
        className={`inline-block object-cover rounded-sm ${getSizeClasses()} ${className}`}
        style={style}
        title={`${country.name} ${country.dialCode}`}
        onError={handleSvgError}
        loading="lazy"
      />
    );
  }

  // Fallback về emoji
  if (country.flag) {
    return (
      <span
        className={`inline-flex items-center justify-center select-none ${getSizeClasses()} ${className}`}
        style={{
          fontFamily:
            'Apple Color Emoji, Segoe UI Emoji, Noto Color Emoji, Twemoji Mozilla, EmojiOne, sans-serif',
          fontSize: getEmojiSize(),
          lineHeight: '1',
          ...style,
        }}
        title={`${country.name} ${country.dialCode}`}
      >
        {country.flag}
      </span>
    );
  }

  // Fallback cuối cùng - hiển thị mã quốc gia
  if (showFallback) {
    return (
      <span
        className={`inline-flex items-center justify-center bg-muted text-muted-foreground text-xs font-medium rounded ${getSizeClasses()} ${className}`}
        style={style}
        title={country.name}
      >
        {country.code}
      </span>
    );
  }

  return null;
};

export default CountryFlag;
