import React, { ReactNode } from 'react';
import { useTranslation } from 'react-i18next';

import { ResponsiveGrid, Typography } from '@/shared/components/common';

export interface InfiniteScrollGridProps<T = any> {
  /**
   * Dữ liệu để hiển thị
   */
  data: T[];

  /**
   * Function render cho mỗi item
   */
  renderItem: (item: T, index: number) => ReactNode;

  /**
   * Key function để tạo unique key cho mỗi item
   */
  keyExtractor: (item: T, index: number) => string | number;

  /**
   * Metadata phân trang
   */
  meta?: {
    currentPage: number;
    totalPages: number;
    totalItems?: number;
  };

  /**
   * Trạng thái loading
   */
  isLoading?: boolean;

  /**
   * Ref cho load more trigger
   */
  loadMoreRef?: React.RefObject<HTMLDivElement>;

  /**
   * Cấu hình responsive grid
   */
  maxColumns?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };

  /**
   * Cấu hình responsive grid khi có chat panel
   */
  maxColumnsWithChatPanel?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };

  /**
   * Gap giữa các items
   */
  gap?:
    | number
    | {
        xs?: number;
        sm?: number;
        md?: number;
        lg?: number;
        xl?: number;
      };

  /**
   * Class name bổ sung
   */
  className?: string;

  /**
   * Text hiển thị khi loading
   */
  loadingText?: string;

  /**
   * Text hiển thị khi có thể load more
   */
  loadMoreText?: string;

  /**
   * Ẩn load more indicator
   */
  hideLoadMoreIndicator?: boolean;

  /**
   * Custom loading component
   */
  loadingComponent?: ReactNode;

  /**
   * Custom load more component
   */
  loadMoreComponent?: ReactNode;

  /**
   * Component hiển thị khi không có dữ liệu
   */
  emptyComponent?: ReactNode;

  /**
   * Text hiển thị khi không có dữ liệu
   */
  emptyText?: string;

  /**
   * Ẩn empty state
   */
  hideEmptyState?: boolean;
}

/**
 * Component hiển thị grid với infinite scroll
 */
const InfiniteScrollGrid = <T,>({
  data,
  renderItem,
  keyExtractor,
  meta,
  isLoading = false,
  loadMoreRef,
  maxColumns = { xs: 1, sm: 2, md: 2, lg: 3, xl: 4 },
  maxColumnsWithChatPanel,
  gap,
  className = '',
  loadingText,
  loadMoreText,
  hideLoadMoreIndicator = false,
  loadingComponent,
  loadMoreComponent,
  emptyComponent,
  emptyText,
  hideEmptyState = false,
}: InfiniteScrollGridProps<T>) => {
  const { t } = useTranslation(['common']);

  // Default texts
  const defaultLoadingText = loadingText || t('common:loading', 'Đang tải...');
  const defaultLoadMoreText = loadMoreText || t('common:loadMore', 'Cuộn để tải thêm');
  const defaultEmptyText = emptyText || t('common:noData', 'Không có dữ liệu');

  // Default loading component
  const defaultLoadingComponent = loadingComponent || (
    <div className="flex items-center space-x-2">
      <div className="animate-spin rounded-full h-6 w-6 border-2 border-primary border-t-transparent"></div>
      <span className="text-gray-500">{defaultLoadingText}</span>
    </div>
  );

  // Default load more component
  const defaultLoadMoreComponent = loadMoreComponent || (
    <span className="text-gray-500">{defaultLoadMoreText}</span>
  );

  // Default empty component
  const defaultEmptyComponent = emptyComponent || (
    <div className="flex flex-col items-center justify-center py-12 text-center">
      <div className="text-gray-400 mb-4">
        <svg
          className="w-16 h-16 mx-auto"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={1}
            d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2 2v-5m16 0h-2M4 13h2m0 0V9a2 2 0 012-2h8a2 2 0 012 2v4M6 13h12"
          />
        </svg>
      </div>
      <Typography variant="h6" className="text-gray-500 font-medium">
        {defaultEmptyText}
      </Typography>
      <Typography variant="body2" className="text-gray-400 mt-2">
        {t('common:noDataDescription', 'Chưa có dữ liệu để hiển thị')}
      </Typography>
    </div>
  );

  // Kiểm tra có thể load more không
  const canLoadMore = meta && meta.currentPage < meta.totalPages;

  // Kiểm tra có dữ liệu không
  const isEmpty = !data || data.length === 0;

  return (
    <div className={className}>
      {/* Hiển thị empty state nếu không có dữ liệu */}
      {isEmpty && !hideEmptyState ? (
        defaultEmptyComponent
      ) : (
        <>
          <ResponsiveGrid
            maxColumns={maxColumns}
            maxColumnsWithChatPanel={maxColumnsWithChatPanel}
            gap={gap}
          >
            {data.map((item, index) => (
              <div key={keyExtractor(item, index)}>{renderItem(item, index)}</div>
            ))}
          </ResponsiveGrid>

          {/* Load more indicator cho infinite scroll */}
          {!hideLoadMoreIndicator && canLoadMore && (
            <div ref={loadMoreRef} className="flex justify-center py-4">
              {isLoading ? defaultLoadingComponent : defaultLoadMoreComponent}
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default InfiniteScrollGrid;
