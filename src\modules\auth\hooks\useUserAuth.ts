import { useMutation } from '@tanstack/react-query';

import { UserAuthService } from '../services';
import { UserLoginRequest } from '../types/user-auth.types';

/**
 * Hook xử lý các thao tác xác thực người dùng
 */
export const useUserAuth = () => {
  /**
   * Đăng nhập tài khoản người dùng
   */
  const loginUser = useMutation({
    mutationFn: (data: UserLoginRequest) => {
      return UserAuthService.login(data);
    },
  });

  /**
   * L<PERSON>y thông tin người dùng hiện tại
   */
  const getUserProfile = useMutation({
    mutationFn: () => {
      return UserAuthService.getProfile();
    },
  });

  return {
    loginUser,
    getUserProfile,
  };
};
