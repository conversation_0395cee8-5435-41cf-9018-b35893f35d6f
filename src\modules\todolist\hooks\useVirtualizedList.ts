import { useState, useEffect, useCallback, useRef } from 'react';

interface UseVirtualizedListOptions<T> {
  items: T[];
  itemHeight: number;
  overscan?: number;
  scrollingDelay?: number;
}

interface UseVirtualizedListResult<T> {
  virtualItems: Array<{ index: number; item: T; offsetTop: number }>;
  totalHeight: number;
  startIndex: number;
  endIndex: number;
  containerRef: React.RefObject<HTMLDivElement>;
  isScrolling: boolean;
}

/**
 * Custom hook for virtualized lists to improve performance with large datasets
 * @param options Options for the virtualized list
 * @returns Result with virtualized items and container ref
 */
export function useVirtualizedList<T>({
  items,
  itemHeight,
  overscan = 3,
  scrollingDelay = 150,
}: UseVirtualizedListOptions<T>): UseVirtualizedListResult<T> {
  const containerRef = useRef<HTMLDivElement>(null);
  const [scrollTop, setScrollTop] = useState(0);
  const [containerHeight, setContainerHeight] = useState(0);
  const [isScrolling, setIsScrolling] = useState(false);
  const scrollingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Calculate the visible range of items
  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
  const endIndex = Math.min(
    items.length - 1,
    Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
  );

  // Create the virtualized items
  const virtualItems = items.slice(startIndex, endIndex + 1).map((item, index) => ({
    index: startIndex + index,
    item,
    offsetTop: (startIndex + index) * itemHeight,
  }));

  // Calculate the total height of all items
  const totalHeight = items.length * itemHeight;

  // Handle scroll events
  const handleScroll = useCallback(() => {
    if (!containerRef.current) {return;}

    const { scrollTop, clientHeight } = containerRef.current;
    setScrollTop(scrollTop);
    setContainerHeight(clientHeight);

    // Set scrolling state
    setIsScrolling(true);

    // Clear previous timeout
    if (scrollingTimeoutRef.current) {
      clearTimeout(scrollingTimeoutRef.current);
    }

    // Set new timeout to detect when scrolling stops
    scrollingTimeoutRef.current = setTimeout(() => {
      setIsScrolling(false);
    }, scrollingDelay);
  }, [scrollingDelay]);

  // Set up scroll event listener
  useEffect(() => {
    const container = containerRef.current;
    if (!container) {return;}

    container.addEventListener('scroll', handleScroll);

    // Initialize container height
    setContainerHeight(container.clientHeight);

    return () => {
      container.removeEventListener('scroll', handleScroll);
      if (scrollingTimeoutRef.current) {
        clearTimeout(scrollingTimeoutRef.current);
      }
    };
  }, [handleScroll]);

  return {
    virtualItems,
    totalHeight,
    startIndex,
    endIndex,
    containerRef,
    isScrolling,
  };
}

/**
 * Custom hook for virtualized grid to improve performance with large datasets
 * @param options Options for the virtualized grid
 * @returns Result with virtualized items and container ref
 */
export function useVirtualizedGrid<T>({
  items,
  itemWidth,
  itemHeight,
  columnCount,
  overscan = 1,
  scrollingDelay = 150,
}: {
  items: T[];
  itemWidth: number;
  itemHeight: number;
  columnCount: number;
  overscan?: number;
  scrollingDelay?: number;
}) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [scrollTop, setScrollTop] = useState(0);
  const [containerHeight, setContainerHeight] = useState(0);
  const [isScrolling, setIsScrolling] = useState(false);
  const scrollingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Calculate the number of rows
  const rowCount = Math.ceil(items.length / columnCount);

  // Calculate the visible range of rows
  const startRowIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
  const endRowIndex = Math.min(
    rowCount - 1,
    Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
  );

  // Create the virtualized items
  const virtualItems: Array<{
    index: number;
    item: T;
    row: number;
    column: number;
    left: number;
    top: number;
  }> = [];

  for (let rowIndex = startRowIndex; rowIndex <= endRowIndex; rowIndex++) {
    for (let columnIndex = 0; columnIndex < columnCount; columnIndex++) {
      const itemIndex = rowIndex * columnCount + columnIndex;
      if (itemIndex < items.length) {
        virtualItems.push({
          index: itemIndex,
          item: items[itemIndex],
          row: rowIndex,
          column: columnIndex,
          left: columnIndex * itemWidth,
          top: rowIndex * itemHeight,
        });
      }
    }
  }

  // Calculate the total height of all rows
  const totalHeight = rowCount * itemHeight;

  // Handle scroll events
  const handleScroll = useCallback(() => {
    if (!containerRef.current) {return;}

    const { scrollTop, clientHeight } = containerRef.current;
    setScrollTop(scrollTop);
    setContainerHeight(clientHeight);

    // Set scrolling state
    setIsScrolling(true);

    // Clear previous timeout
    if (scrollingTimeoutRef.current) {
      clearTimeout(scrollingTimeoutRef.current);
    }

    // Set new timeout to detect when scrolling stops
    scrollingTimeoutRef.current = setTimeout(() => {
      setIsScrolling(false);
    }, scrollingDelay);
  }, [scrollingDelay]);

  // Set up scroll event listener
  useEffect(() => {
    const container = containerRef.current;
    if (!container) {return;}

    container.addEventListener('scroll', handleScroll);

    // Initialize container height
    setContainerHeight(container.clientHeight);

    return () => {
      container.removeEventListener('scroll', handleScroll);
      if (scrollingTimeoutRef.current) {
        clearTimeout(scrollingTimeoutRef.current);
      }
    };
  }, [handleScroll]);

  return {
    virtualItems,
    totalHeight,
    startRowIndex,
    endRowIndex,
    containerRef,
    isScrolling,
  };
}
