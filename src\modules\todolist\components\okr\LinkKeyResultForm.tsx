import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, FormItem, Select } from '@/shared/components/common';
import { useAuth } from '@/shared/hooks/useAuth';
import { NotificationUtil } from '@/shared/utils/notification';

import {
  useActiveCycles,
  useObjectives,
  useKeyResults,
  useLinkTaskToKeyResult,
  useUserKeyResults,
} from '../../hooks/useOkrIntegration';

interface LinkKeyResultFormProps {
  taskId: number;
  onSubmit: () => void;
  onCancel: () => void;
}

/**
 * Form for linking a key result to a task
 */
const LinkKeyResultForm: React.FC<LinkKeyResultFormProps> = ({ taskId, onSubmit, onCancel }) => {
  const { t } = useTranslation(['common', 'todolist']);
  const { user } = useAuth();

  // State for selected values
  const [selectedCycleId, setSelectedCycleId] = useState<string>('');
  const [selectedObjectiveId, setSelectedObjectiveId] = useState<string>('');
  const [selectedKeyResultId, setSelectedKeyResultId] = useState<string>('');
  const [selectionMode, setSelectionMode] = useState<'hierarchy' | 'myKeyResults'>('hierarchy');

  // Fetch data
  const { data: cycles, isLoading: isLoadingCycles } = useActiveCycles();
  const { data: objectives, isLoading: isLoadingObjectives } = useObjectives(
    selectedCycleId ? parseInt(selectedCycleId, 10) : 0
  );
  const { data: keyResults, isLoading: isLoadingKeyResults } = useKeyResults(
    selectedObjectiveId ? parseInt(selectedObjectiveId, 10) : 0
  );
  const { data: myKeyResults, isLoading: isLoadingMyKeyResults } = useUserKeyResults(user?.id || 0);

  // Mutation to link key result
  const { mutateAsync: linkKeyResult, isPending: isLinking } = useLinkTaskToKeyResult();

  // Handle cycle change
  const handleCycleChange = (value: string) => {
    setSelectedCycleId(value);
    setSelectedObjectiveId('');
    setSelectedKeyResultId('');
  };

  // Handle objective change
  const handleObjectiveChange = (value: string) => {
    setSelectedObjectiveId(value);
    setSelectedKeyResultId('');
  };

  // Handle key result change
  const handleKeyResultChange = (value: string) => {
    setSelectedKeyResultId(value);
  };

  // Handle selection mode change
  const handleSelectionModeChange = (value: string) => {
    setSelectionMode(value as 'hierarchy' | 'myKeyResults');
    setSelectedCycleId('');
    setSelectedObjectiveId('');
    setSelectedKeyResultId('');
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!selectedKeyResultId) {
      NotificationUtil.error(
        t('todolist:okr.notifications.selectKeyResult', 'Please select a key result')
      );
      return;
    }

    try {
      await linkKeyResult({
        taskId,
        keyResultId: parseInt(selectedKeyResultId, 10),
      });

      NotificationUtil.success(
        t('todolist:okr.notifications.linkSuccess', 'Key result linked successfully')
      );

      onSubmit();
    } catch (error) {
      console.error('Error linking key result:', error);
      NotificationUtil.error(t('todolist:okr.notifications.linkError', 'Error linking key result'));
    }
  };

  return (
    <div className="space-y-6">
      <FormItem label={t('todolist:okr.form.selectionMode', 'Selection Mode')}>
        <Select
          value={selectionMode}
          onChange={handleSelectionModeChange}
          options={[
            {
              value: 'hierarchy',
              label: t('todolist:okr.form.browseHierarchy', 'Browse OKR Hierarchy'),
            },
            {
              value: 'myKeyResults',
              label: t('todolist:okr.form.myKeyResults', 'My Key Results'),
            },
          ]}
        />
      </FormItem>

      {selectionMode === 'hierarchy' ? (
        <>
          <FormItem label={t('todolist:okr.form.cycle', 'OKR Cycle')}>
            <Select
              value={selectedCycleId}
              onChange={handleCycleChange}
              placeholder={t('todolist:okr.form.selectCycle', 'Select OKR Cycle')}
              options={
                cycles?.items.map(cycle => ({
                  value: cycle.id.toString(),
                  label: cycle.name,
                })) || []
              }
              isLoading={isLoadingCycles}
            />
          </FormItem>

          {selectedCycleId && (
            <FormItem label={t('todolist:okr.form.objective', 'Objective')}>
              <Select
                value={selectedObjectiveId}
                onChange={handleObjectiveChange}
                placeholder={t('todolist:okr.form.selectObjective', 'Select Objective')}
                options={
                  objectives?.items.map(objective => ({
                    value: objective.id.toString(),
                    label: objective.title,
                  })) || []
                }
                isLoading={isLoadingObjectives}
              />
            </FormItem>
          )}

          {selectedObjectiveId && (
            <FormItem label={t('todolist:okr.form.keyResult', 'Key Result')}>
              <Select
                value={selectedKeyResultId}
                onChange={handleKeyResultChange}
                placeholder={t('todolist:okr.form.selectKeyResult', 'Select Key Result')}
                options={
                  keyResults?.items.map(keyResult => ({
                    value: keyResult.id.toString(),
                    label: keyResult.title,
                  })) || []
                }
                isLoading={isLoadingKeyResults}
              />
            </FormItem>
          )}
        </>
      ) : (
        <FormItem label={t('todolist:okr.form.myKeyResults', 'My Key Results')}>
          <Select
            value={selectedKeyResultId}
            onChange={handleKeyResultChange}
            placeholder={t('todolist:okr.form.selectKeyResult', 'Select Key Result')}
            options={
              myKeyResults?.items.map(keyResult => ({
                value: keyResult.id.toString(),
                label: keyResult.title,
              })) || []
            }
            isLoading={isLoadingMyKeyResults}
          />
        </FormItem>
      )}

      <div className="flex justify-end space-x-3 pt-4">
        <Button variant="outline" onClick={onCancel} disabled={isLinking}>
          {t('common:cancel', 'Cancel')}
        </Button>
        <Button onClick={handleSubmit} loading={isLinking} disabled={!selectedKeyResultId}>
          {t('todolist:okr.actions.link', 'Link Key Result')}
        </Button>
      </div>
    </div>
  );
};

export default LinkKeyResultForm;
