import React, { useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import { ActiveFilters } from '@/modules/components/filters';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { Card, Table, IconCard, Tooltip } from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import { TableColumn, SortOrder } from '@/shared/components/common/Table/types';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { useActiveFilters } from '@/shared/hooks/filters';
import { useDataTableConfig, useDataTable } from '@/shared/hooks/table';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { NotificationUtil } from '@/shared/utils/notification';

import ProjectForm from '../components/ProjectForm';
import { useProjects, useDeleteProject } from '../hooks/useProjects';
import { ProjectDto, ProjectQueryDto } from '../types/project.types';

/**
 * Trang quản lý dự án sử dụng các hooks tối ưu
 */
const ProjectListPage: React.FC = () => {
  const { t } = useTranslation(['todolist', 'common']);
  const navigate = useNavigate();

  // Sử dụng hook animation cho form
  const { isVisible, showForm, hideForm } = useSlideForm();

  // Sử dụng hook xóa dự án
  const deleteProjectMutation = useDeleteProject();

  // Xử lý xóa dự án
  const handleDelete = useCallback(
    (id: number) => {
      if (
        window.confirm(t('todolist:project.confirmDelete', 'Bạn có chắc chắn muốn xóa dự án này?'))
      ) {
        deleteProjectMutation.mutate(id, {
          onSuccess: () => {
            NotificationUtil.success({
              message: t('todolist:project.deleteSuccess', 'Xóa dự án thành công'),
            });
          },
          onError: error => {
            console.error('Error deleting project:', error);
            NotificationUtil.error({
              message: t('todolist:project.deleteError', 'Xóa dự án thất bại'),
            });
          },
        });
      }
    },
    [deleteProjectMutation, t]
  );

  // Định nghĩa columns cho bảng
  const columns = useMemo<TableColumn<ProjectDto>[]>(
    () => [
      { key: 'id', title: 'ID', dataIndex: 'id', width: '5%', sortable: true },
      {
        key: 'title',
        title: t('todolist:project.fields.title', 'Tên dự án'),
        dataIndex: 'title',
        width: '20%',
        sortable: true,
      },
      {
        key: 'description',
        title: t('todolist:project.fields.description', 'Mô tả'),
        dataIndex: 'description',
        width: '30%',
        sortable: true,
      },
      {
        key: 'isActive',
        title: t('todolist:project.fields.status', 'Trạng thái'),
        dataIndex: 'isActive',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          const isActive = value as boolean;
          return (
            <div
              className={`px-2 py-1 rounded-full text-center text-xs font-medium ${
                isActive
                  ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
              }`}
            >
              {isActive ? t('common:active') : t('common:inactive')}
            </div>
          );
        },
      },
      {
        key: 'createdAt',
        title: t('common:createdAt', 'Ngày tạo'),
        dataIndex: 'createdAt',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          if (!value) {return <div>-</div>;}
          const date = new Date(value as number);
          return <div>{date.toLocaleDateString()}</div>;
        },
      },
      {
        key: 'actions',
        title: t('common:actions'),
        width: '15%',
        render: (_: unknown, record: ProjectDto) => (
          <div className="flex space-x-2">
            <Tooltip content={t('common:view')} position="top">
              <IconCard
                icon="eye"
                variant="default"
                size="sm"
                onClick={() => navigate(`/todolist/projects/${record.id}`)}
              />
            </Tooltip>
            <Tooltip content={t('common:edit')} position="top">
              <IconCard
                icon="edit"
                variant="default"
                size="sm"
                onClick={() => navigate(`/todolist/projects/${record.id}`)}
              />
            </Tooltip>
            <Tooltip content={t('common:delete')} position="top">
              <IconCard
                icon="trash"
                variant="default"
                size="sm"
                onClick={() => handleDelete(record.id)}
              />
            </Tooltip>
          </div>
        ),
      },
    ],
    [t, handleDelete, navigate]
  );

  // Sử dụng hook tạo filterOptions
  const filterOptions = useMemo(
    () => [
      { id: 'all', label: t('common:all'), icon: 'list', value: 'all' },
      { id: 'active', label: t('common:active'), icon: 'check', value: 'true' },
      {
        id: 'inactive',
        label: t('common:inactive'),
        icon: 'eye-off',
        value: 'false',
      },
    ],
    [t]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): ProjectQueryDto => {
    const queryParams: ProjectQueryDto = {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
    };

    if (params.filterValue !== 'all') {
      queryParams.isActive = params.filterValue === 'true';
    }

    // Thêm xử lý dateRange nếu có
    if (params.dateRange && params.dateRange[0] && params.dateRange[1]) {
      queryParams.startDate = params.dateRange[0].getTime();
      queryParams.endDate = params.dateRange[1].getTime();
    }

    return queryParams;
  };

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<ProjectDto, ProjectQueryDto>({
      columns,
      filterOptions,
      showDateFilter: true,
      createQueryParams,
    })
  );

  // Lấy danh sách dự án từ API
  const { data: projectsData, isLoading } = useProjects(dataTable.queryParams);

  // Xử lý thêm mới
  const handleAdd = () => {
    showForm();
  };

  // Xử lý submit form
  const handleSubmit = () => {
    hideForm();
    // Refresh data by refetching the projects
    dataTable.tableData.handlePageChange(1, dataTable.tableData.pageSize);
  };

  // Xử lý hủy form
  const handleCancel = () => {
    hideForm();
  };

  // Tạo hàm wrapper để chuyển đổi kiểu dữ liệu của handleSortChange
  const handleSortChangeWrapper = useCallback(
    (sortBy: string | null, sortDirection: SortOrder) => {
      dataTable.tableData.handleSortChange(sortBy, sortDirection);
    },
    [dataTable.tableData]
  );

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearDateRange,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: handleSortChangeWrapper,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {
      true: t('common:active'),
      false: t('common:inactive'),
    },
    t,
  });

  return (
    <div>
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={handleAdd}
        items={dataTable.menuItems}
        onDateRangeChange={dataTable.dateRange.setDateRange}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={true}
        showColumnFilter={true}
      />

      {/* Thêm component ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={handleClearDateRange}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      <SlideInForm isVisible={isVisible}>
        <ProjectForm onSubmit={handleSubmit} onCancel={handleCancel} />
      </SlideInForm>

      <Card className="overflow-hidden">
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={projectsData?.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: projectsData?.meta.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: projectsData?.meta.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [5, 10, 15, 20],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>
    </div>
  );
};

export default ProjectListPage;
