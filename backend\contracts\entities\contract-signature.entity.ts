import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';

import { User } from '../../auth/entities/user.entity';
import { Contract } from './contract.entity';

export enum SignatureStatus {
  PENDING = 'pending',
  SIGNED = 'signed',
  DECLINED = 'declined',
  EXPIRED = 'expired',
}

export enum SignatureType {
  ELECTRONIC = 'electronic',
  DIGITAL = 'digital',
  WET = 'wet',
}

/**
 * Entity chữ ký hợp đồng
 */
@Entity('contract_signatures')
export class ContractSignature {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'enum', enum: SignatureStatus, default: SignatureStatus.PENDING })
  status: SignatureStatus;

  @Column({ type: 'enum', enum: SignatureType, default: SignatureType.ELECTRONIC })
  type: SignatureType;

  @Column({ type: 'varchar', length: 100 })
  signerName: string;

  @Column({ type: 'varchar', length: 255 })
  signerEmail: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  signerPhone: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  signerTitle: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  signerCompany: string;

  @Column({ type: 'int', default: 1 })
  signOrder: number;

  @Column({ type: 'boolean', default: true })
  required: boolean;

  @Column({ type: 'text', nullable: true })
  signatureData: string; // Base64 encoded signature image or digital signature

  @Column({ type: 'varchar', length: 255, nullable: true })
  signatureHash: string; // Hash for verification

  @Column({ type: 'varchar', length: 100, nullable: true })
  ipAddress: string;

  @Column({ type: 'text', nullable: true })
  userAgent: string;

  @Column({ type: 'json', nullable: true })
  location: {
    latitude?: number;
    longitude?: number;
    address?: string;
  };

  @Column({ type: 'timestamp', nullable: true })
  signedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  sentAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  viewedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  expiresAt: Date;

  @Column({ type: 'text', nullable: true })
  declineReason: string;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  externalSignatureId: string; // ID from external e-signature service

  @Column({ type: 'json', nullable: true })
  externalData: Record<string, any>; // Data from external service

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  // Relations
  @ManyToOne(() => Contract, (contract) => contract.signatures, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'contractId' })
  contract: Contract;

  @Column({ type: 'uuid' })
  contractId: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'userId' })
  user: User; // Internal user if applicable

  @Column({ type: 'uuid', nullable: true })
  userId: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'delegatedById' })
  delegatedBy: User; // User who delegated this signature

  @Column({ type: 'uuid', nullable: true })
  delegatedById: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Methods
  sign(signatureData: string, ipAddress: string, userAgent: string, location?: any): void {
    this.status = SignatureStatus.SIGNED;
    this.signatureData = signatureData;
    this.ipAddress = ipAddress;
    this.userAgent = userAgent;
    this.location = location;
    this.signedAt = new Date();
    this.signatureHash = this.generateSignatureHash();
  }

  decline(reason: string): void {
    this.status = SignatureStatus.DECLINED;
    this.declineReason = reason;
  }

  markAsViewed(): void {
    if (!this.viewedAt) {
      this.viewedAt = new Date();
    }
  }

  isExpired(): boolean {
    return this.expiresAt && new Date() > this.expiresAt;
  }

  canSign(): boolean {
    return this.status === SignatureStatus.PENDING && !this.isExpired();
  }

  private generateSignatureHash(): string {
    // Generate a hash for signature verification
    const data = `${this.contractId}-${this.signerEmail}-${this.signedAt?.toISOString()}`;
    // In real implementation, use proper cryptographic hash
    return Buffer.from(data).toString('base64');
  }

  // Validation methods
  validateSignature(): boolean {
    if (!this.signatureData || !this.signatureHash) {
      return false;
    }

    // Verify signature hash
    const expectedHash = this.generateSignatureHash();
    return this.signatureHash === expectedHash;
  }

  getSignatureInfo(): {
    signer: string;
    signedAt: Date | null;
    ipAddress: string | null;
    isValid: boolean;
  } {
    return {
      signer: `${this.signerName} (${this.signerEmail})`,
      signedAt: this.signedAt,
      ipAddress: this.ipAddress,
      isValid: this.validateSignature(),
    };
  }
}
