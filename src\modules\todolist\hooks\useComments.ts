import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

import { CommentService } from '../services/comment.service';
import { CommentQueryDto, CreateCommentDto, UpdateCommentDto } from '../types/comment.types';

// Key for React Query
const COMMENTS_QUERY_KEY = 'comments';

/**
 * Hook to get comments for a task
 * @param taskId Task ID
 * @param params Query parameters
 * @returns Query result with comments
 */
export const useComments = (taskId: number, params?: CommentQueryDto) => {
  return useQuery({
    queryKey: [COMMENTS_QUERY_KEY, taskId, params],
    queryFn: () => CommentService.getComments(taskId, params),
    select: data => data.result,
    enabled: !!taskId,
  });
};

/**
 * Hook to get a comment by ID
 * @param taskId Task ID
 * @param commentId Comment ID
 * @returns Query result with the comment
 */
export const useComment = (taskId: number, commentId: number) => {
  return useQuery({
    queryKey: [COMMENTS_QUERY_KEY, taskId, commentId],
    queryFn: () => CommentService.getComment(taskId, commentId),
    select: data => data.result,
    enabled: !!taskId && !!commentId,
  });
};

/**
 * Hook to create a new comment
 * @returns Mutation result for creating a comment
 */
export const useCreateComment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateCommentDto) => CommentService.createComment(data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [COMMENTS_QUERY_KEY, variables.taskId] });
    },
  });
};

/**
 * Hook to update a comment
 * @returns Mutation result for updating a comment
 */
export const useUpdateComment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      taskId,
      commentId,
      data,
    }: {
      taskId: number;
      commentId: number;
      data: UpdateCommentDto;
    }) => CommentService.updateComment(taskId, commentId, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [COMMENTS_QUERY_KEY, variables.taskId] });
      queryClient.invalidateQueries({
        queryKey: [COMMENTS_QUERY_KEY, variables.taskId, variables.commentId],
      });
    },
  });
};

/**
 * Hook to delete a comment
 * @returns Mutation result for deleting a comment
 */
export const useDeleteComment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ taskId, commentId }: { taskId: number; commentId: number }) =>
      CommentService.deleteComment(taskId, commentId),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [COMMENTS_QUERY_KEY, variables.taskId] });
    },
  });
};
