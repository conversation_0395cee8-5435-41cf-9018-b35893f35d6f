import React from 'react';
import { useTranslation } from 'react-i18next';

import {
  <PERSON>,
  <PERSON>pography,
  Button,
  Badge,
  IconButton,
  Tooltip,
  Loading,
  ProgressBar,
} from '@/shared/components/common';
import { NotificationUtil } from '@/shared/utils/notification';

import {
  useTaskKeyResults,
  useUnlinkTaskFromKeyResult,
  useTaskKeyResultLinks,
} from '../../hooks/useOkrIntegration';
import { KeyResultDto, KeyResultStatus } from '../../types/okr-integration.types';
// import { useAuth } from '@/shared/hooks/useAuth';

interface TaskKeyResultListProps {
  taskId: number;
  onAddKeyResult: () => void;
}

/**
 * Component to display key results linked to a task
 */
const TaskKeyResultList: React.FC<TaskKeyResultListProps> = ({ taskId, onAddKeyResult }) => {
  const { t } = useTranslation(['common', 'todolist']);

  // Fetch key results for the task
  const { data: taskKeyResults, isLoading, refetch } = useTaskKeyResults(taskId);

  // Fetch task-key result links
  const { data: links } = useTaskKeyResultLinks(taskId);

  // Mutation to unlink a key result
  const { mutateAsync: unlinkKeyResult } = useUnlinkTaskFromKeyResult();

  // Get status badge color
  const getStatusBadgeColor = (status: KeyResultStatus) => {
    switch (status) {
      case KeyResultStatus.NOT_STARTED:
        return 'bg-gray-100 text-gray-800';
      case KeyResultStatus.IN_PROGRESS:
        return 'bg-blue-100 text-blue-800';
      case KeyResultStatus.COMPLETED:
        return 'bg-green-100 text-green-800';
      case KeyResultStatus.AT_RISK:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get status text
  const getStatusText = (status: KeyResultStatus) => {
    switch (status) {
      case KeyResultStatus.NOT_STARTED:
        return t('todolist:okr.keyResult.status.notStarted', 'Not Started');
      case KeyResultStatus.IN_PROGRESS:
        return t('todolist:okr.keyResult.status.inProgress', 'In Progress');
      case KeyResultStatus.COMPLETED:
        return t('todolist:okr.keyResult.status.completed', 'Completed');
      case KeyResultStatus.AT_RISK:
        return t('todolist:okr.keyResult.status.atRisk', 'At Risk');
      default:
        return '';
    }
  };

  // Format key result value
  const formatKeyResultValue = (keyResult: KeyResultDto, value: number) => {
    switch (keyResult.format) {
      case 'PERCENTAGE':
        return `${value}%`;
      case 'CURRENCY':
        return `$${value.toLocaleString()}`;
      case 'BOOLEAN':
        return value === 1 ? 'Yes' : 'No';
      default:
        return value.toLocaleString();
    }
  };

  // Handle unlink key result
  const handleUnlinkKeyResult = async (keyResultId: number) => {
    try {
      // Find the link ID
      const link = links?.items.find(link => link.keyResultId === keyResultId);
      if (!link) {return;}

      await unlinkKeyResult({ taskId, linkId: link.id });
      NotificationUtil.success({
        message: t('todolist:okr.notifications.unlinkSuccess', 'Key result unlinked successfully'),
      });
      refetch();
    } catch (error) {
      console.error('Error unlinking key result:', error);
      NotificationUtil.error({
        message: t('todolist:okr.notifications.unlinkError', 'Error unlinking key result'),
      });
    }
  };

  if (isLoading) {
    return <Loading />;
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <Typography variant="h6">
          {t('todolist:okr.keyResults.title', 'Linked Key Results')}
        </Typography>
        <Button size="sm" onClick={onAddKeyResult}>
          {t('todolist:okr.keyResults.add', 'Link Key Result')}
        </Button>
      </div>

      {!taskKeyResults?.keyResults || taskKeyResults.keyResults.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          {t('todolist:okr.keyResults.empty', 'No key results linked to this task yet.')}
        </div>
      ) : (
        <div className="space-y-4">
          {taskKeyResults.keyResults.map(keyResult => (
            <Card key={keyResult.id} className="p-4">
              <div className="flex justify-between items-start">
                <div>
                  <Typography variant="subtitle1" className="font-medium">
                    {keyResult.title}
                  </Typography>
                  {keyResult.description && (
                    <Typography variant="body2" className="text-gray-500 mt-1">
                      {keyResult.description}
                    </Typography>
                  )}
                </div>
                <div className="flex items-center space-x-2">
                  <Badge className={getStatusBadgeColor(keyResult.status)}>
                    {getStatusText(keyResult.status)}
                  </Badge>
                  <Tooltip content={t('todolist:okr.keyResults.unlink', 'Unlink Key Result')}>
                    <IconButton
                      icon="unlink"
                      size="sm"
                      variant="ghost"
                      onClick={() => handleUnlinkKeyResult(keyResult.id)}
                    />
                  </Tooltip>
                </div>
              </div>

              <div className="mt-4">
                <div className="flex justify-between items-center mb-1">
                  <Typography variant="body2" className="text-gray-500">
                    {t('todolist:okr.keyResult.progress', 'Progress')}
                  </Typography>
                  <Typography variant="body2" className="font-medium">
                    {keyResult.progress}%
                  </Typography>
                </div>
                <ProgressBar value={keyResult.progress} />
              </div>

              <div className="mt-4 grid grid-cols-3 gap-4">
                <div>
                  <Typography variant="caption" className="text-gray-500">
                    {t('todolist:okr.keyResult.startValue', 'Start Value')}
                  </Typography>
                  <Typography variant="body2" className="font-medium">
                    {formatKeyResultValue(keyResult, keyResult.startValue)}
                  </Typography>
                </div>
                <div>
                  <Typography variant="caption" className="text-gray-500">
                    {t('todolist:okr.keyResult.currentValue', 'Current Value')}
                  </Typography>
                  <Typography variant="body2" className="font-medium">
                    {formatKeyResultValue(keyResult, keyResult.currentValue)}
                  </Typography>
                </div>
                <div>
                  <Typography variant="caption" className="text-gray-500">
                    {t('todolist:okr.keyResult.targetValue', 'Target Value')}
                  </Typography>
                  <Typography variant="body2" className="font-medium">
                    {formatKeyResultValue(keyResult, keyResult.targetValue)}
                  </Typography>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default TaskKeyResultList;
