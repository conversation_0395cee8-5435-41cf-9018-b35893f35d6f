import { QueryDto } from '@/shared/dto/request/query.dto';

/**
 * Enum trạng thái phân đoạn
 */
export enum SegmentStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DRAFT = 'draft',
}

/**
 * Enum loại phân đoạn
 */
export enum SegmentType {
  CATEGORY = 'category',
  TAG = 'tag',
  PRICE_RANGE = 'price_range',
  CUSTOM = 'custom',
}

/**
 * Enum loại điều kiện
 */
export enum ConditionType {
  EQUALS = 'equals',
  NOT_EQUALS = 'not_equals',
  CONTAINS = 'contains',
  NOT_CONTAINS = 'not_contains',
  GREATER_THAN = 'greater_than',
  LESS_THAN = 'less_than',
  BETWEEN = 'between',
  IN = 'in',
  NOT_IN = 'not_in',
}

/**
 * Interface cho điều kiện phân đoạn
 */
export interface SegmentCondition {
  /**
   * Trường để áp dụng điều kiện
   */
  field: string;

  /**
   * Loại điều kiện
   */
  type: ConditionType;

  /**
   * Giá trị điều kiện
   */
  value: string | number | Array<string | number>;
}

/**
 * Interface cho nhóm điều kiện phân đoạn
 */
export interface SegmentConditionGroup {
  /**
   * Loại kết hợp điều kiện (AND/OR)
   */
  operator: 'AND' | 'OR';

  /**
   * Danh sách điều kiện
   */
  conditions: SegmentCondition[];

  /**
   * Danh sách nhóm điều kiện con
   */
  groups?: SegmentConditionGroup[];
}

/**
 * Interface cho phân đoạn sản phẩm
 */
export interface SegmentDto {
  /**
   * ID phân đoạn
   */
  id: string;

  /**
   * Tên phân đoạn
   */
  name: string;

  /**
   * Mô tả phân đoạn
   */
  description?: string;

  /**
   * Loại phân đoạn
   */
  type: SegmentType;

  /**
   * Trạng thái phân đoạn
   */
  status: SegmentStatus;

  /**
   * Điều kiện phân đoạn
   */
  conditions: SegmentConditionGroup;

  /**
   * Số lượng sản phẩm trong phân đoạn
   */
  productCount: number;

  /**
   * Ngày tạo
   */
  createdAt: string;

  /**
   * Ngày cập nhật
   */
  updatedAt: string;
}

/**
 * Interface cho tham số truy vấn danh sách phân đoạn
 */
export interface SegmentQueryDto extends QueryDto {
  /**
   * Lọc theo trạng thái
   */
  status?: SegmentStatus;

  /**
   * Lọc theo loại phân đoạn
   */
  type?: SegmentType;
}

/**
 * Interface cho dữ liệu tạo phân đoạn
 */
export interface CreateSegmentDto {
  /**
   * Tên phân đoạn
   */
  name: string;

  /**
   * Mô tả phân đoạn
   */
  description?: string;

  /**
   * Loại phân đoạn
   */
  type: SegmentType;

  /**
   * Trạng thái phân đoạn
   */
  status?: SegmentStatus;

  /**
   * Điều kiện phân đoạn
   */
  conditions: SegmentConditionGroup;
}

/**
 * Interface cho dữ liệu cập nhật phân đoạn
 */
export interface UpdateSegmentDto extends Partial<CreateSegmentDto> {
  /**
   * ID phân đoạn
   */
  id: string;
}
