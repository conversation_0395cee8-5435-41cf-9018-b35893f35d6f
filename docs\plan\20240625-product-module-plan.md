# Kế hoạch phát triển giao diện sản phẩm

## Tổng quan

Phát triển các giao diện và form liên quan đến quản lý sản phẩm, bao gồm:

1. <PERSON><PERSON> danh sách sản phẩm
2. Form tạo/chỉnh sửa sản phẩm
3. Form quản lý phân đoạn (segment) sản phẩm
4. Chi tiết sản phẩm

## Cấu trúc thư mục

```
src/modules/product/
├── api/                  # API calls và mock data
│   ├── product.api.ts
│   └── segment.api.ts
├── components/           # Các component riêng của module
│   ├── forms/
│   │   ├── ProductForm.tsx
│   │   └── SegmentForm.tsx
│   ├── product/
│   │   ├── ProductFilter.tsx
│   │   └── ProductDetails.tsx
│   └── common/
│       └── ProductStats.tsx
├── hooks/                # Custom hooks
│   ├── useProduct.ts
│   └── useSegment.ts
├── locales/              # Các file ngôn ngữ
│   ├── en.json
│   ├── vi.json
│   ├── zh.json
│   └── index.ts
├── pages/                # Các trang của module
│   ├── ProductListPage.tsx
│   ├── ProductDetailPage.tsx
│   └── SegmentListPage.tsx
├── schemas/              # Schema validation
│   ├── product.schema.ts
│   └── segment.schema.ts
├── types/                # Type definitions
│   ├── product.types.ts
│   └── segment.types.ts
├── index.ts              # Export các component, hooks, types
├── productRoutes.tsx     # Định nghĩa routes
└── README.md             # Tài liệu mô tả module
```

## Các thành phần cần phát triển

### 1. Types và Schemas

#### Product Types

- `ProductStatus`: Enum (ACTIVE, INACTIVE, DRAFT)
- `ProductType`: Enum (PHYSICAL, DIGITAL, SERVICE)
- `Product`: Interface (id, name, description, price, originalPrice, discount, images, status, type, categoryId, tags, createdAt, updatedAt)
- `ProductQueryDto`: Interface (extends QueryDto, thêm các filter như status, type, categoryId)

#### Segment Types

- `SegmentStatus`: Enum (ACTIVE, INACTIVE, DRAFT)
- `SegmentType`: Enum (CATEGORY, TAG, PRICE_RANGE, CUSTOM)
- `Segment`: Interface (id, name, description, type, status, conditions, productCount, createdAt, updatedAt)

### 2. Components

#### Forms

- `ProductForm`: Form tạo/chỉnh sửa sản phẩm
- `SegmentForm`: Form tạo/chỉnh sửa phân đoạn sản phẩm

#### Product Components

- `ProductFilter`: Component lọc sản phẩm
- `ProductDetails`: Component hiển thị chi tiết sản phẩm
- `ProductStats`: Component hiển thị thống kê sản phẩm

### 3. Pages

- `ProductListPage`: Trang danh sách sản phẩm
- `ProductDetailPage`: Trang chi tiết sản phẩm
- `SegmentListPage`: Trang danh sách phân đoạn

### 4. Hooks

- `useProduct`: Hook xử lý logic liên quan đến sản phẩm
- `useSegment`: Hook xử lý logic liên quan đến phân đoạn

### 5. Locales

Chuẩn bị các file ngôn ngữ cho:

- Tiếng Việt (vi)
- Tiếng Anh (en)
- Tiếng Trung (zh)

## Kế hoạch triển khai

### Giai đoạn 1: Cấu trúc cơ bản

1. Tạo cấu trúc thư mục
2. Định nghĩa types và schemas
3. Tạo file locales

### Giai đoạn 2: Components

1. Phát triển ProductForm
2. Phát triển SegmentForm
3. Phát triển các component filter và hiển thị

### Giai đoạn 3: Pages

1. Phát triển ProductListPage
2. Phát triển ProductDetailPage
3. Phát triển SegmentListPage

### Giai đoạn 4: Tích hợp và kiểm thử

1. Tích hợp routes
2. Kiểm thử responsive
3. Kiểm thử đa ngôn ngữ
4. Kiểm thử theme

## Yêu cầu kỹ thuật

### Responsive

- Sử dụng `ResponsiveGrid` thay vì `Grid`
- Thiết kế mobile-first
- Sử dụng media queries cho các breakpoint khác nhau

### Đa ngôn ngữ

- Sử dụng hook `useTranslation` từ react-i18next
- Tất cả text hiển thị phải sử dụng keys từ file locales
- Hỗ trợ 3 ngôn ngữ: Tiếng Việt, Tiếng Anh, Tiếng Trung

### Theme

- Sử dụng hook `useTheme` để áp dụng theme
- Đảm bảo các component hoạt động tốt với cả light và dark mode
- Tuân thủ color palette của hệ thống

### Components dùng chung

- Sử dụng các component từ `@/shared/components/common`
- Sử dụng `SlideInForm` cho các form thay vì modal
- Sử dụng `MenuIconBar` cho các action bar
- Sử dụng `Table` với `ActionMenu` cho các bảng dữ liệu

## Tiêu chí đánh giá

- Code tuân thủ ESLint và TypeScript
- Responsive trên các kích thước màn hình
- Hỗ trợ đầy đủ đa ngôn ngữ
- Tương thích với các theme của hệ thống
- Tái sử dụng tối đa các component dùng chung
- Hiệu suất tốt, không có memory leak
