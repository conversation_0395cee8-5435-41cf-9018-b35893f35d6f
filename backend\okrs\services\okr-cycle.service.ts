import { Injectable, Logger } from '@nestjs/common';
import { OkrCycleRepository } from '../repositories/okr-cycle.repository';
import { ObjectiveRepository } from '../repositories/objective.repository';
import { CreateOkrCycleDto, UpdateOkrCycleDto, OkrCycleQueryDto, OkrCycleResponseDto } from '../dto/okr-cycle';
import { OkrCycle } from '../entities/okr-cycle.entity';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { AppException } from '@/common';
import { OKRS_ERROR_CODES } from '../errors/okrs-error.code';
import { OkrCycleStatus } from '../enum/okr-cycle-status.enum';
import { WithTenant } from '@/common/decorators';

/**
 * Service cho chu kỳ OKR
 */
@Injectable()
export class OkrCycleService {
  private readonly logger = new Logger(OkrCycleService.name);

  constructor(
    private readonly okrCycleRepository: OkrCycleRepository,
    private readonly objectiveRepository: ObjectiveRepository
  ) {}

  /**
   * Tạo mới chu kỳ OKR
   * @param tenantId ID của tenant
   * @param userId ID người dùng tạo chu kỳ
   * @param dto DTO tạo chu kỳ OKR
   * @returns Phản hồi chu kỳ OKR đã tạo
   */
  async create(userId: number, dto: CreateOkrCycleDto): Promise<OkrCycleResponseDto> {
    // Kiểm tra khoảng thời gian
    if (new Date(dto.startDate) > new Date(dto.endDate)) {
      throw new AppException(
        OKRS_ERROR_CODES.OKR_CYCLE_INVALID_DATE_RANGE,
        'Ngày bắt đầu phải trước ngày kết thúc',
      );
    }

    console.log(userId);
    // Tạo chu kỳ OKR
    const cycle = await this.okrCycleRepository.create({
      name: dto.name,
      startDate: new Date(dto.startDate),
      endDate: new Date(dto.endDate),
      status: dto.status,
      createdBy: userId,
      createdAt: Date.now(), // Sử dụng timestamp đầy đủ (mili giây)
    });

    return this.mapToResponseDto(cycle);
  }

  /**
   * Lấy tất cả chu kỳ OKR với phân trang và lọc
   * @param tenantId ID của tenant
   * @param query Tham số truy vấn
   * @returns Danh sách phân trang các phản hồi chu kỳ OKR
   */
  @WithTenant((args) => args[0])
  async findAll(tenantId: number, query: OkrCycleQueryDto): Promise<PaginatedResult<OkrCycleResponseDto>> {
    // tenantId được sử dụng bởi WithTenant decorator để thiết lập context

    const result = await this.okrCycleRepository.findAll(query);

    return {
      items: result.items.map((cycle: OkrCycle) => this.mapToResponseDto(cycle)),
      meta: result.meta,
    };
  }

  /**
   * Lấy chu kỳ OKR theo ID
   * @param tenantId ID của tenant
   * @param id ID chu kỳ OKR
   * @returns Phản hồi chu kỳ OKR
   */
  @WithTenant((args) => args[0])
  async findById(tenantId: number, id: number): Promise<OkrCycleResponseDto> {
    // tenantId được sử dụng bởi WithTenant decorator để thiết lập context

    const cycle = await this.okrCycleRepository.findById(id);

    if (!cycle) {
      throw new AppException(
        OKRS_ERROR_CODES.OKR_CYCLE_NOT_FOUND,
        `Không tìm thấy chu kỳ OKR với ID ${id}`,
      );
    }

    return this.mapToResponseDto(cycle);
  }

  /**
   * Cập nhật chu kỳ OKR
   * @param tenantId ID của tenant
   * @param id ID chu kỳ OKR
   * @param dto DTO cập nhật chu kỳ OKR
   * @returns Phản hồi chu kỳ OKR đã cập nhật
   */
  @WithTenant((args) => args[0])
  async update(tenantId: number, id: number, dto: UpdateOkrCycleDto): Promise<OkrCycleResponseDto> {
    // tenantId được sử dụng bởi WithTenant decorator để thiết lập context

    // Kiểm tra chu kỳ tồn tại
    const existingCycle = await this.okrCycleRepository.findById(id);

    if (!existingCycle) {
      throw new AppException(
        OKRS_ERROR_CODES.OKR_CYCLE_NOT_FOUND,
        `Không tìm thấy chu kỳ OKR với ID ${id}`,
      );
    }

    // Kiểm tra khoảng thời gian nếu cả hai ngày được cung cấp
    if (dto.startDate && dto.endDate) {
      if (new Date(dto.startDate) > new Date(dto.endDate)) {
        throw new AppException(
          OKRS_ERROR_CODES.OKR_CYCLE_INVALID_DATE_RANGE,
          'Ngày bắt đầu phải trước ngày kết thúc',
        );
      }
    } else if (dto.startDate && !dto.endDate) {
      // Nếu chỉ cung cấp ngày bắt đầu, kiểm tra với ngày kết thúc hiện tại
      if (new Date(dto.startDate) > new Date(existingCycle.endDate)) {
        throw new AppException(
          OKRS_ERROR_CODES.OKR_CYCLE_INVALID_DATE_RANGE,
          'Ngày bắt đầu phải trước ngày kết thúc',
        );
      }
    } else if (!dto.startDate && dto.endDate) {
      // Nếu chỉ cung cấp ngày kết thúc, kiểm tra với ngày bắt đầu hiện tại
      if (new Date(existingCycle.startDate) > new Date(dto.endDate)) {
        throw new AppException(
          OKRS_ERROR_CODES.OKR_CYCLE_INVALID_DATE_RANGE,
          'Ngày bắt đầu phải trước ngày kết thúc',
        );
      }
    }

    // Chuẩn bị dữ liệu cập nhật
    const updateData: Partial<OkrCycle> = {
      name: dto.name,
    };

    // Chuyển đổi các trường ngày thành đối tượng Date nếu có
    if (dto.startDate) {
      updateData.startDate = new Date(dto.startDate);
    }

    if (dto.endDate) {
      updateData.endDate = new Date(dto.endDate);
    }

    if (dto.status) {
      updateData.status = dto.status;
    }

    // Cập nhật chu kỳ
    const updatedCycle = await this.okrCycleRepository.update(id, updateData);

    if (!updatedCycle) {
      throw new AppException(
        OKRS_ERROR_CODES.OKR_CYCLE_NOT_FOUND,
        `Không tìm thấy chu kỳ OKR với ID ${id}`,
      );
    }

    return this.mapToResponseDto(updatedCycle);
  }

  /**
   * Xóa chu kỳ OKR
   * @param tenantId ID của tenant
   * @param id ID chu kỳ OKR
   * @returns True nếu xóa thành công
   */
  @WithTenant((args) => args[0])
  async delete(tenantId: number, id: number): Promise<boolean> {
    // tenantId được sử dụng bởi WithTenant decorator để thiết lập context

    // Kiểm tra chu kỳ tồn tại
    const existingCycle = await this.okrCycleRepository.findById(id);

    if (!existingCycle) {
      throw new AppException(
        OKRS_ERROR_CODES.OKR_CYCLE_NOT_FOUND,
        `Không tìm thấy chu kỳ OKR với ID ${id}`,
      );
    }

    // Tìm tất cả các objective liên quan đến chu kỳ này
    const relatedObjectives = await this.objectiveRepository.findByCycleId(id);

    // Nếu có objective liên quan, cập nhật cycleId của chúng thành null
    if (relatedObjectives.length > 0) {
      this.logger.log(`Cập nhật ${relatedObjectives.length} objective liên quan đến chu kỳ OKR ${id}`);

      // Cập nhật từng objective
      for (const objective of relatedObjectives) {
        await this.objectiveRepository.update(objective.id, { cycleId: null });
      }
    }

    // Xóa chu kỳ
    const deleted = await this.okrCycleRepository.delete(id);

    if (!deleted) {
      throw new AppException(
        OKRS_ERROR_CODES.OKR_CYCLE_NOT_FOUND,
        `Không tìm thấy chu kỳ OKR với ID ${id}`,
      );
    }

    return true;
  }

  /**
   * Lấy chu kỳ OKR đang hoạt động
   * @param tenantId ID của tenant
   * @returns Phản hồi chu kỳ OKR đang hoạt động
   */
  @WithTenant((args) => args[0])
  async findActive(tenantId: number): Promise<OkrCycleResponseDto> {
    // tenantId được sử dụng bởi WithTenant decorator để thiết lập context

    const cycle = await this.okrCycleRepository.findActive();

    if (!cycle) {
      throw new AppException(
        OKRS_ERROR_CODES.OKR_CYCLE_NOT_FOUND,
        'Không tìm thấy chu kỳ OKR đang hoạt động',
      );
    }

    return this.mapToResponseDto(cycle);
  }

  /**
   * Chuyển đổi entity chu kỳ OKR sang DTO phản hồi
   * @param cycle Entity chu kỳ OKR
   * @returns DTO phản hồi chu kỳ OKR
   */
  private mapToResponseDto(cycle: OkrCycle): OkrCycleResponseDto {
    const response = new OkrCycleResponseDto();

    response.id = cycle.id;
    response.name = cycle.name;

    // Xử lý startDate an toàn
    if (cycle.startDate instanceof Date) {
      response.startDate = cycle.startDate.toISOString().split('T')[0]; // Định dạng YYYY-MM-DD
    } else if (typeof cycle.startDate === 'string') {
      // Nếu là chuỗi, giữ nguyên hoặc chuyển đổi nếu cần
      response.startDate = cycle.startDate;
    } else {
      // Trường hợp null, undefined hoặc kiểu dữ liệu khác
      response.startDate = ''; // Hoặc một giá trị mặc định khác
      this.logger.warn(`Invalid startDate for cycle ID ${cycle.id}: ${cycle.startDate}`);
    }

    // Xử lý endDate an toàn
    if (cycle.endDate instanceof Date) {
      response.endDate = cycle.endDate.toISOString().split('T')[0]; // Định dạng YYYY-MM-DD
    } else if (typeof cycle.endDate === 'string') {
      // Nếu là chuỗi, giữ nguyên hoặc chuyển đổi nếu cần
      response.endDate = cycle.endDate;
    } else {
      // Trường hợp null, undefined hoặc kiểu dữ liệu khác
      response.endDate = ''; // Hoặc một giá trị mặc định khác
      this.logger.warn(`Invalid endDate for cycle ID ${cycle.id}: ${cycle.endDate}`);
    }

    response.status = cycle.status || OkrCycleStatus.PLANNING; // Mặc định là PLANNING nếu null
    response.createdAt = cycle.createdAt || Date.now(); // Mặc định là thời gian hiện tại nếu null

    return response;
  }
}
