import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

import { OkrIntegrationService } from '../services/okr-integration.service';
import { CreateTaskKeyResultLinkDto } from '../types/okr-integration.types';

// Keys for React Query
const OKR_CYCLES_QUERY_KEY = 'okr-cycles';
const OKR_OBJECTIVES_QUERY_KEY = 'okr-objectives';
const OKR_KEY_RESULTS_QUERY_KEY = 'okr-key-results';
const TASK_KEY_RESULTS_QUERY_KEY = 'task-key-results';

/**
 * Hook to get active OKR cycles
 * @returns Query result with active OKR cycles
 */
export const useActiveCycles = () => {
  return useQuery({
    queryKey: [OKR_CYCLES_QUERY_KEY, 'active'],
    queryFn: () => OkrIntegrationService.getActiveCycles(),
    select: data => data.result,
  });
};

/**
 * Hook to get objectives for a cycle
 * @param cycleId Cycle ID
 * @returns Query result with objectives
 */
export const useObjectives = (cycleId: number) => {
  return useQuery({
    queryKey: [OKR_OBJECTIVES_QUERY_KEY, cycleId],
    queryFn: () => OkrIntegrationService.getObjectives(cycleId),
    select: data => data.result,
    enabled: !!cycleId,
  });
};

/**
 * Hook to get key results for an objective
 * @param objectiveId Objective ID
 * @returns Query result with key results
 */
export const useKeyResults = (objectiveId: number) => {
  return useQuery({
    queryKey: [OKR_KEY_RESULTS_QUERY_KEY, 'objective', objectiveId],
    queryFn: () => OkrIntegrationService.getKeyResults(objectiveId),
    select: data => data.result,
    enabled: !!objectiveId,
  });
};

/**
 * Hook to get key results for a user
 * @param userId User ID
 * @returns Query result with key results
 */
export const useUserKeyResults = (userId: number) => {
  return useQuery({
    queryKey: [OKR_KEY_RESULTS_QUERY_KEY, 'user', userId],
    queryFn: () => OkrIntegrationService.getUserKeyResults(userId),
    select: data => data.result,
    enabled: !!userId,
  });
};

/**
 * Hook to get task-key result links for a task
 * @param taskId Task ID
 * @returns Query result with task-key result links
 */
export const useTaskKeyResultLinks = (taskId: number) => {
  return useQuery({
    queryKey: [TASK_KEY_RESULTS_QUERY_KEY, 'links', taskId],
    queryFn: () => OkrIntegrationService.getTaskKeyResultLinks(taskId),
    select: data => data.result,
    enabled: !!taskId,
  });
};

/**
 * Hook to get key results for a task
 * @param taskId Task ID
 * @returns Query result with key results
 */
export const useTaskKeyResults = (taskId: number) => {
  return useQuery({
    queryKey: [TASK_KEY_RESULTS_QUERY_KEY, taskId],
    queryFn: () => OkrIntegrationService.getTaskKeyResults(taskId),
    select: data => data.result,
    enabled: !!taskId,
  });
};

/**
 * Hook to get tasks for a key result
 * @param keyResultId Key Result ID
 * @returns Query result with tasks
 */
export const useKeyResultTasks = (keyResultId: number) => {
  return useQuery({
    queryKey: [OKR_KEY_RESULTS_QUERY_KEY, keyResultId, 'tasks'],
    queryFn: () => OkrIntegrationService.getKeyResultTasks(keyResultId),
    select: data => data.result,
    enabled: !!keyResultId,
  });
};

/**
 * Hook to link a task to a key result
 * @returns Mutation result for linking a task to a key result
 */
export const useLinkTaskToKeyResult = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateTaskKeyResultLinkDto) =>
      OkrIntegrationService.linkTaskToKeyResult(data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [TASK_KEY_RESULTS_QUERY_KEY, variables.taskId] });
      queryClient.invalidateQueries({
        queryKey: [TASK_KEY_RESULTS_QUERY_KEY, 'links', variables.taskId],
      });
      queryClient.invalidateQueries({
        queryKey: [OKR_KEY_RESULTS_QUERY_KEY, variables.keyResultId, 'tasks'],
      });
    },
  });
};

/**
 * Hook to unlink a task from a key result
 * @returns Mutation result for unlinking a task from a key result
 */
export const useUnlinkTaskFromKeyResult = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ taskId, linkId }: { taskId: number; linkId: number }) =>
      OkrIntegrationService.unlinkTaskFromKeyResult(taskId, linkId),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [TASK_KEY_RESULTS_QUERY_KEY, variables.taskId] });
      queryClient.invalidateQueries({
        queryKey: [TASK_KEY_RESULTS_QUERY_KEY, 'links', variables.taskId],
      });
      // We don't know the keyResultId here, so we can't invalidate the specific key result tasks query
      // Instead, we could invalidate all key result tasks queries, but that might be too broad
    },
  });
};
