import { apiClient } from '@/shared/api';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';

import {
  OkrCycleDto,
  ObjectiveDto,
  KeyResultDto,
  TaskKeyResultLinkDto,
  CreateTaskKeyResultLinkDto,
  TaskWithKeyResultsDto,
  KeyResultWithTasksDto,
} from '../types/okr-integration.types';

/**
 * Service for OKR integration
 */
export const OkrIntegrationService = {
  /**
   * Get active OKR cycles
   * @returns Promise with API response containing active OKR cycles
   */
  getActiveCycles: () => {
    return apiClient.get<PaginatedResult<OkrCycleDto>>('/api/okr/cycles', {
      params: { status: 'ACTIVE' },
    });
  },

  /**
   * Get objectives for a cycle
   * @param cycleId Cycle ID
   * @returns Promise with API response containing objectives
   */
  getObjectives: (cycleId: number) => {
    return apiClient.get<PaginatedResult<ObjectiveDto>>(`/api/okr/cycles/${cycleId}/objectives`);
  },

  /**
   * Get key results for an objective
   * @param objectiveId Objective ID
   * @returns Promise with API response containing key results
   */
  getKeyResults: (objectiveId: number) => {
    return apiClient.get<PaginatedResult<KeyResultDto>>(
      `/api/okr/objectives/${objectiveId}/key-results`
    );
  },

  /**
   * Get key results for a user
   * @param userId User ID
   * @returns Promise with API response containing key results
   */
  getUserKeyResults: (userId: number) => {
    return apiClient.get<PaginatedResult<KeyResultDto>>('/api/okr/key-results', {
      params: { ownerId: userId },
    });
  },

  /**
   * Get task-key result links for a task
   * @param taskId Task ID
   * @returns Promise with API response containing task-key result links
   */
  getTaskKeyResultLinks: (taskId: number) => {
    return apiClient.get<PaginatedResult<TaskKeyResultLinkDto>>(`/api/tasks/${taskId}/key-results`);
  },

  /**
   * Get key results for a task
   * @param taskId Task ID
   * @returns Promise with API response containing key results
   */
  getTaskKeyResults: (taskId: number) => {
    return apiClient.get<TaskWithKeyResultsDto>(`/api/tasks/${taskId}/key-results/details`);
  },

  /**
   * Get tasks for a key result
   * @param keyResultId Key Result ID
   * @returns Promise with API response containing tasks
   */
  getKeyResultTasks: (keyResultId: number) => {
    return apiClient.get<KeyResultWithTasksDto>(`/api/okr/key-results/${keyResultId}/tasks`);
  },

  /**
   * Link a task to a key result
   * @param data Link data
   * @returns Promise with API response containing the created link
   */
  linkTaskToKeyResult: (data: CreateTaskKeyResultLinkDto) => {
    return apiClient.post<TaskKeyResultLinkDto>(`/api/tasks/${data.taskId}/key-results`, {
      keyResultId: data.keyResultId,
    });
  },

  /**
   * Unlink a task from a key result
   * @param taskId Task ID
   * @param linkId Link ID
   * @returns Promise with API response containing the result
   */
  unlinkTaskFromKeyResult: (taskId: number, linkId: number) => {
    return apiClient.delete<boolean>(`/api/tasks/${taskId}/key-results/${linkId}`);
  },
};
