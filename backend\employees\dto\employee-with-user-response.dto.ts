import { ApiProperty } from '@nestjs/swagger';
import { EmployeeStatus } from '../enum/employee-status.enum';

/**
 * DTO cho phản hồi thông tin người dùng
 */
export class UserResponseDto {
  /**
   * ID người dùng
   * @example 1
   */
  @ApiProperty({ description: 'ID người dùng', example: 1 })
  id: number;

  /**
   * Tên đăng nhập
   * @example "user001"
   */
  @ApiProperty({ description: 'Tên đăng nhập', example: 'user001' })
  username: string;

  /**
   * Địa chỉ email
   * @example "<EMAIL>"
   */
  @ApiProperty({ description: 'Địa chỉ email', example: '<EMAIL>' })
  email: string;

  /**
   * Họ và tên đầy đủ
   * @example "Nguyễn Văn A"
   */
  @ApiProperty({ description: 'Họ và tên đầy đủ', example: 'Nguyễ<PERSON> Văn <PERSON>' })
  fullName: string;
}

/**
 * DTO cho thông tin nhân viên trong phản hồi
 */
export class EmployeeResponseDto {
  /**
   * ID nhân viên
   * @example 1
   */
  @ApiProperty({ description: 'ID nhân viên', example: 1 })
  id: number;

  /**
   * ID người dùng liên kết
   * @example 1
   */
  @ApiProperty({ description: 'ID người dùng liên kết', example: 1 })
  userId: number;

  /**
   * Mã nhân viên
   * @example "EMP001"
   */
  @ApiProperty({ description: 'Mã nhân viên', example: 'EMP001' })
  employeeCode: string;

  /**
   * ID phòng ban
   * @example 1
   */
  @ApiProperty({ description: 'ID phòng ban', example: 1, nullable: true })
  departmentId: number | null;

  /**
   * Chức danh công việc
   * @example "Kỹ sư phần mềm"
   */
  @ApiProperty({ description: 'Chức danh công việc', example: 'Kỹ sư phần mềm', nullable: true })
  jobTitle: string | null;

  /**
   * Trạng thái nhân viên
   * @example "active"
   */
  @ApiProperty({
    description: 'Trạng thái nhân viên',
    enum: EmployeeStatus,
    example: EmployeeStatus.ACTIVE
  })
  status: EmployeeStatus;
}

/**
 * DTO cho phản hồi khi tạo nhân viên kèm tài khoản người dùng
 */
export class EmployeeWithUserResponseDto {
  /**
   * Thông tin nhân viên
   */
  @ApiProperty({ description: 'Thông tin nhân viên', type: EmployeeResponseDto })
  employee: EmployeeResponseDto;

  /**
   * Thông tin người dùng
   */
  @ApiProperty({ description: 'Thông tin người dùng', type: UserResponseDto })
  user: UserResponseDto;
}
