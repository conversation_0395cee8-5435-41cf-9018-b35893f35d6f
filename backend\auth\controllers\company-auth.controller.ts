import { Body, Controller, Post } from '@nestjs/common';
import {
  ApiExtraModels,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { CompanyAuthService } from '../services/company-auth.service';
import {
  CompanyRegisterDto,
  CompanyLoginDto,
  VerifyEmailDto,
  ResendOtpDto,
} from '../dto';
import { ApiResponseDto } from '@/common/response/api-response-dto';
import {
  CompanyLoginResponseDto,
  CompanyRegisterResponseDto,
  CompanyResponseDto,
  ResendOtpResponseDto,
  VerifyEmailResponseDto,
} from '../dto/company-response.dto';
import { SWAGGER_API_TAG } from '@/common/swagger/swagger.tags';

@ApiTags(SWAGGER_API_TAG.AUTHENTICATION)
@ApiExtraModels(
  ApiResponseDto,
  CompanyRegisterResponseDto,
  VerifyEmailResponseDto,
  CompanyLoginResponseDto,
  CompanyResponseDto,
  ResendOtpResponseDto,
)
@Controller('auth/company')
export class CompanyAuthController {
  constructor(private readonly companyAuthService: CompanyAuthService) {}

  @Post('register')
  @ApiOperation({ summary: 'Đăng ký tài khoản công ty mới' })
  @ApiResponse({
    status: 201,
    description: 'Đăng ký thành công, email xác thực đã được gửi',
    schema: ApiResponseDto.getSchema(CompanyRegisterResponseDto),
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ' })
  async register(
    @Body() registerDto: CompanyRegisterDto,
  ): Promise<ApiResponseDto<CompanyRegisterResponseDto>> {
    return this.companyAuthService.register(registerDto);
  }

  @Post('verify-email')
  @ApiOperation({ summary: 'Xác thực email đăng ký bằng OTP' })
  @ApiResponse({
    status: 200,
    description: 'Xác thực email thành công',
    schema: ApiResponseDto.getSchema(VerifyEmailResponseDto),
  })
  @ApiResponse({
    status: 400,
    description: 'Token hoặc OTP không hợp lệ hoặc đã hết hạn',
  })
  async verifyEmail(
    @Body() verifyEmailDto: VerifyEmailDto,
  ): Promise<ApiResponseDto<VerifyEmailResponseDto>> {
    return this.companyAuthService.verifyEmail(verifyEmailDto);
  }

  @Post('resend-otp')
  @ApiOperation({ summary: 'Gửi lại mã OTP xác thực' })
  @ApiResponse({
    status: 200,
    description: 'Gửi lại OTP thành công',
    schema: ApiResponseDto.getSchema(ResendOtpResponseDto),
  })
  @ApiResponse({
    status: 400,
    description: 'Token không hợp lệ hoặc tài khoản đã được xác thực',
  })
  async resendOtp(
    @Body() resendOtpDto: ResendOtpDto,
  ): Promise<ApiResponseDto<ResendOtpResponseDto>> {
    return this.companyAuthService.resendOtp(resendOtpDto);
  }
}
