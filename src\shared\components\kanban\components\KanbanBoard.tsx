import React, { useState, useCallback } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { useTranslation } from 'react-i18next';

import { Button, Icon, Loading } from '@/shared/components/common';

import '../styles/kanban.css';
import { useKanbanBoard } from '../hooks/useKanbanBoard';
import {
  KanbanBoardData,
  KanbanCard,
  KanbanColumn,
  KanbanMoveResult,
  KanbanVariant,
  KanbanFunctionality,
  KanbanTheme,
} from '../types/kanban.types';

import KanbanAddColumn from './KanbanAddColumn';
import KanbanColumnComponent from './KanbanColumn';

export interface KanbanBoardProps {
  /**
   * Dữ liệu ban đầu của Kanban Board
   */
  initialData: KanbanBoardData;

  /**
   * Callback khi có thay đổi dữ liệu
   */
  onChange?: (data: KanbanBoardData) => void;

  /**
   * Callback khi kéo thả thẻ
   */
  onCardMove?: (result: KanbanMoveResult) => void;

  /**
   * Callback khi thêm thẻ mới
   */
  onCardAdd?: (columnId: string, card: Partial<KanbanCard>) => void;

  /**
   * Callback khi click vào thẻ
   */
  onCardClick?: (card: KanbanCard) => void;

  /**
   * Callback khi sửa thẻ
   */
  onCardEdit?: (cardId: string, columnId: string, updates: Partial<KanbanCard>) => void;

  /**
   * Callback khi xóa thẻ
   */
  onCardDelete?: (cardId: string, columnId: string) => void;

  /**
   * Callback khi thêm cột mới
   */
  onColumnAdd?: (column: Partial<KanbanColumn>) => void;

  /**
   * Callback khi sửa cột
   */
  onColumnEdit?: (columnId: string, updates: Partial<KanbanColumn>) => void;

  /**
   * Callback khi xóa cột
   */
  onColumnDelete?: (columnId: string) => void;

  /**
   * Callback khi thay đổi thứ tự cột
   */
  onColumnReorder?: (columnId: string, newIndex: number) => void;

  /**
   * Có cho phép thêm cột mới không
   * @default true
   */
  allowAddColumn?: boolean;

  /**
   * Có cho phép kéo thả cột không
   * @default true
   */
  allowColumnDrag?: boolean;

  /**
   * Có cho phép thêm thẻ mới không
   * @default true
   */
  allowAddCard?: boolean;

  /**
   * Có cho phép kéo thả thẻ không
   * @default true
   */
  allowCardDrag?: boolean;

  /**
   * Có đang loading không
   * @default false
   */
  loading?: boolean;

  /**
   * Biến thể giao diện của Kanban Board
   * @default KanbanVariant.DEFAULT
   */
  variant?: KanbanVariant;

  /**
   * Biến thể chức năng của Kanban Board
   * @default KanbanFunctionality.INTERACTIVE
   */
  functionality?: KanbanFunctionality;

  /**
   * Biến thể theme của Kanban Board
   * @default KanbanTheme.DEFAULT
   */
  theme?: KanbanTheme;

  /**
   * Custom class name
   */
  className?: string;
}

/**
 * Component Kanban Board
 */
const KanbanBoard: React.FC<KanbanBoardProps> = ({
  initialData,
  onChange,
  onCardMove,
  onCardAdd,
  onCardClick,
  onCardEdit,
  onCardDelete,
  onColumnAdd,
  onColumnEdit,
  onColumnDelete,
  onColumnReorder,
  allowAddColumn = true,
  allowColumnDrag = true,
  allowAddCard = true,
  allowCardDrag = true,
  loading = false,
  variant = KanbanVariant.DEFAULT,
  functionality = KanbanFunctionality.INTERACTIVE,
  theme = KanbanTheme.DEFAULT,
  className = '',
}) => {
  const { t } = useTranslation();
  const [isAddingColumn, setIsAddingColumn] = useState(false);

  // Sử dụng hook useKanbanBoard để quản lý trạng thái
  const {
    boardData,
    getCardsForColumn,
    handleCardMove,
    handleCardAdd,
    handleCardEdit,
    handleCardDelete,
  } = useKanbanBoard({
    initialData,
    onChange,
    onCardMove,
    onCardAdd,
    onCardEdit,
    onCardDelete,
    onColumnAdd,
    onColumnEdit,
    onColumnDelete,
    onColumnReorder,
  });

  // Xử lý thêm cột mới
  const handleAddColumn = useCallback(
    (column: Partial<KanbanColumn>) => {
      if (onColumnAdd) {
        onColumnAdd(column);
      } else {
        // Xử lý nội bộ nếu không có callback bên ngoài
        const newColumn: KanbanColumn = {
          id: `column-${Date.now()}`,
          title: column.title || 'New Column',
          cardIds: [],
          ...column,
        };

        const newData: KanbanBoardData = {
          ...boardData,
          columns: [...boardData.columns, newColumn],
        };

        if (onChange) {
          onChange(newData);
        }
      }

      setIsAddingColumn(false);
    },
    [boardData, onChange, onColumnAdd]
  );

  // Xử lý xóa cột
  const handleDeleteColumn = useCallback(
    (columnId: string) => {
      if (onColumnDelete) {
        onColumnDelete(columnId);
      } else {
        // Xử lý nội bộ nếu không có callback bên ngoài
        const columnIndex = boardData.columns.findIndex(col => col.id === columnId);
        if (columnIndex === -1) {return;}

        const column = boardData.columns[columnIndex];
        const newColumns = boardData.columns.filter(col => col.id !== columnId);

        // Xóa tất cả các thẻ trong cột
        const newCards = { ...boardData.cards };
        for (const cardId of column.cardIds) {
          delete newCards[cardId];
        }

        const newData: KanbanBoardData = {
          columns: newColumns,
          cards: newCards,
        };

        if (onChange) {
          onChange(newData);
        }
      }
    },
    [boardData, onChange, onColumnDelete]
  );

  // Xử lý sửa cột
  const handleEditColumn = useCallback(
    (columnId: string, updates: Partial<KanbanColumn>) => {
      if (onColumnEdit) {
        onColumnEdit(columnId, updates);
      } else {
        // Xử lý nội bộ nếu không có callback bên ngoài
        const columnIndex = boardData.columns.findIndex(col => col.id === columnId);
        if (columnIndex === -1) {return;}

        const newColumns = [...boardData.columns];
        newColumns[columnIndex] = {
          ...newColumns[columnIndex],
          ...updates,
        };

        const newData: KanbanBoardData = {
          ...boardData,
          columns: newColumns,
        };

        if (onChange) {
          onChange(newData);
        }
      }
    },
    [boardData, onChange, onColumnEdit]
  );

  // Xử lý thay đổi thứ tự cột
  const handleColumnReorder = useCallback(
    (columnId: string, newIndex: number) => {
      if (onColumnReorder) {
        onColumnReorder(columnId, newIndex);
      } else {
        // Xử lý nội bộ nếu không có callback bên ngoài
        const columnIndex = boardData.columns.findIndex(col => col.id === columnId);
        if (columnIndex === -1 || columnIndex === newIndex) {return;}

        const newColumns = [...boardData.columns];
        const [removed] = newColumns.splice(columnIndex, 1);
        newColumns.splice(newIndex, 0, removed);

        const newData: KanbanBoardData = {
          ...boardData,
          columns: newColumns,
        };

        if (onChange) {
          onChange(newData);
        }
      }
    },
    [boardData, onChange, onColumnReorder]
  );

  // Adapter cho onCardMove để phù hợp với kiểu dữ liệu của KanbanColumn
  const handleCardMoveAdapter = useCallback(
    (cardId: string, sourceColumnId: string, targetColumnId: string, newIndex: number) => {
      handleCardMove({
        cardId,
        sourceColumnId,
        targetColumnId,
        newIndex,
      });
    },
    [handleCardMove]
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loading />
      </div>
    );
  }

  // Xác định các class dựa trên biến thể
  const getVariantClasses = () => {
    switch (variant) {
      case KanbanVariant.COMPACT:
        return 'kanban-compact';
      case KanbanVariant.DETAILED:
        return 'kanban-detailed';
      case KanbanVariant.VERTICAL:
        return 'kanban-vertical flex-col';
      case KanbanVariant.HORIZONTAL:
        return 'kanban-horizontal';
      case KanbanVariant.SWIMLANE:
        return 'kanban-swimlane';
      case KanbanVariant.TIMELINE:
        return 'kanban-timeline';
      case KanbanVariant.GRID:
        return 'kanban-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4';
      case KanbanVariant.MASONRY:
        return 'kanban-masonry columns-1 md:columns-2 lg:columns-3 gap-4';
      default:
        return '';
    }
  };

  const getFunctionalityClasses = () => {
    switch (functionality) {
      case KanbanFunctionality.SIMPLE:
        return 'kanban-simple';
      case KanbanFunctionality.ADVANCED:
        return 'kanban-advanced';
      case KanbanFunctionality.READONLY:
        return 'kanban-readonly pointer-events-none';
      case KanbanFunctionality.COLLABORATIVE:
        return 'kanban-collaborative';
      case KanbanFunctionality.FILTERED:
        return 'kanban-filtered';
      case KanbanFunctionality.SEARCHABLE:
        return 'kanban-searchable';
      case KanbanFunctionality.SORTABLE:
        return 'kanban-sortable';
      default:
        return '';
    }
  };

  const getThemeClasses = () => {
    switch (theme) {
      case KanbanTheme.COLORFUL:
        return 'kanban-colorful';
      case KanbanTheme.MINIMAL:
        return 'kanban-minimal';
      case KanbanTheme.BORDERED:
        return 'kanban-bordered';
      case KanbanTheme.DARK:
        return 'kanban-dark bg-gray-900 text-white';
      case KanbanTheme.LIGHT:
        return 'kanban-light bg-white text-gray-900';
      case KanbanTheme.GRADIENT:
        return 'kanban-gradient bg-gradient-to-r from-blue-500 to-purple-500';
      case KanbanTheme.MATERIAL:
        return 'kanban-material shadow-lg';
      case KanbanTheme.GLASSMORPHISM:
        return 'kanban-glassmorphism backdrop-blur-md bg-white/30 dark:bg-gray-900/30';
      case KanbanTheme.NEUMORPHISM:
        return 'kanban-neumorphism shadow-[inset_-5px_-5px_10px_rgba(255,255,255,0.7),inset_5px_5px_10px_rgba(0,0,0,0.1)]';
      default:
        return '';
    }
  };

  // Xác định các props cho KanbanColumn dựa trên biến thể
  const getColumnProps = (column: KanbanColumn, index: number) => {
    const baseProps = {
      key: column.id,
      column,
      cards: getCardsForColumn(column.id),
      index,
      onCardMove: handleCardMoveAdapter,
      onCardAdd: handleCardAdd,
      onCardClick: onCardClick,
      onCardEdit: handleCardEdit,
      onCardDelete: handleCardDelete,
      onColumnEdit: handleEditColumn,
      onColumnDelete: handleDeleteColumn,
      onColumnReorder: handleColumnReorder,
      allowAddCard: functionality === KanbanFunctionality.READONLY ? false : allowAddCard,
      allowColumnDrag: functionality === KanbanFunctionality.READONLY ? false : allowColumnDrag,
      allowCardDrag: functionality === KanbanFunctionality.READONLY ? false : allowCardDrag,
    };

    // Thêm các props tùy chỉnh dựa trên biến thể
    switch (variant) {
      case KanbanVariant.COMPACT:
        return {
          ...baseProps,
          className: 'min-w-[220px] max-w-[220px]',
        };

      case KanbanVariant.DETAILED:
        return {
          ...baseProps,
          className: 'min-w-[320px] max-w-[320px]',
        };

      case KanbanVariant.VERTICAL:
        return {
          ...baseProps,
          className: 'w-full max-w-full mb-4',
        };

      case KanbanVariant.SWIMLANE:
        return {
          ...baseProps,
          className: 'min-w-[280px] max-w-[280px] border-t-4',
          style: { borderTopColor: column.color || '#ccc' },
        };

      case KanbanVariant.TIMELINE:
        return {
          ...baseProps,
          className:
            'min-w-[280px] max-w-[280px] relative pl-8 before:content-[""] before:absolute before:left-3 before:top-0 before:bottom-0 before:w-1 before:bg-gray-200 dark:before:bg-gray-700',
        };

      case KanbanVariant.GRID:
      case KanbanVariant.MASONRY:
        return {
          ...baseProps,
          className: 'w-full break-inside-avoid',
        };

      default:
        return baseProps;
    }
  };

  // Kết hợp tất cả các class
  const containerClasses = `
    kanban-board
    ${getVariantClasses()}
    ${getFunctionalityClasses()}
    ${getThemeClasses()}
    ${className}
  `;

  // Xác định layout dựa trên biến thể
  const isVertical = variant === KanbanVariant.VERTICAL;
  const isGrid = variant === KanbanVariant.GRID;
  const isMasonry = variant === KanbanVariant.MASONRY;
  const isSwimLane = variant === KanbanVariant.SWIMLANE;
  const isTimeline = variant === KanbanVariant.TIMELINE;
  const isSpecialLayout = isGrid || isMasonry || isSwimLane || isTimeline;

  // Thêm các tính năng dựa trên biến thể chức năng
  const isSearchable = functionality === KanbanFunctionality.SEARCHABLE;
  const isFilterable = functionality === KanbanFunctionality.FILTERED;
  const isSortable = functionality === KanbanFunctionality.SORTABLE;
  const isReadOnly = functionality === KanbanFunctionality.READONLY;

  // Render các tính năng bổ sung
  const renderAdditionalFeatures = () => {
    return (
      <>
        {isSearchable && (
          <div className="mb-4">
            <input
              type="text"
              placeholder={t('kanban.searchPlaceholder', 'Search cards...')}
              className="w-full p-2 border border-gray-300 dark:border-gray-700 rounded-md"
            />
          </div>
        )}

        {isFilterable && (
          <div className="mb-4 flex flex-wrap gap-2">
            <Button variant="outline" size="sm">
              <Icon name="filter" size="sm" className="mr-1" />
              {t('kanban.filterByPriority', 'Filter by Priority')}
            </Button>
            <Button variant="outline" size="sm">
              <Icon name="filter" size="sm" className="mr-1" />
              {t('kanban.filterByAssignee', 'Filter by Assignee')}
            </Button>
          </div>
        )}

        {isSortable && (
          <div className="mb-4 flex flex-wrap gap-2">
            <Button variant="outline" size="sm">
              <Icon name="sort" size="sm" className="mr-1" />
              {t('kanban.sortByDueDate', 'Sort by Due Date')}
            </Button>
            <Button variant="outline" size="sm">
              <Icon name="sort" size="sm" className="mr-1" />
              {t('kanban.sortByPriority', 'Sort by Priority')}
            </Button>
          </div>
        )}
      </>
    );
  };

  return (
    <DndProvider backend={HTML5Backend}>
      <div className={`flex flex-col h-full ${containerClasses}`}>
        {/* Additional Features */}
        {(isSearchable || isFilterable || isSortable) && renderAdditionalFeatures()}

        {/* Main Content */}
        <div
          className={`
          ${isVertical ? 'flex flex-col' : 'flex overflow-x-auto'}
          ${isGrid ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4' : ''}
          ${isMasonry ? 'columns-1 md:columns-2 lg:columns-3 gap-4' : ''}
          pb-4 pt-2 h-full
        `}
        >
          {/* Columns */}
          <div
            className={`
            ${isVertical ? 'flex flex-col' : 'flex space-x-4'}
            ${isSpecialLayout ? 'w-full' : ''}
            h-full
          `}
          >
            {boardData.columns.map((column, index) => (
              <KanbanColumnComponent {...getColumnProps(column, index)} />
            ))}

            {/* Add Column Button or Form */}
            {allowAddColumn && !isReadOnly && !isSpecialLayout && (
              <div className={`${isVertical ? 'w-full' : 'min-w-[280px]'} flex-shrink-0`}>
                {isAddingColumn ? (
                  <KanbanAddColumn
                    onAdd={handleAddColumn}
                    onCancel={() => setIsAddingColumn(false)}
                  />
                ) : (
                  <Button
                    variant="outline"
                    className="w-full h-10 flex items-center justify-center border-dashed"
                    onClick={() => setIsAddingColumn(true)}
                  >
                    <Icon name="plus" size="sm" className="mr-2" />
                    {t('kanban.addColumn', 'Add Column')}
                  </Button>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Add Column Button for Special Layouts */}
        {allowAddColumn && !isReadOnly && isSpecialLayout && (
          <div className="mt-4">
            {isAddingColumn ? (
              <KanbanAddColumn onAdd={handleAddColumn} onCancel={() => setIsAddingColumn(false)} />
            ) : (
              <Button
                variant="outline"
                className="w-full h-10 flex items-center justify-center border-dashed"
                onClick={() => setIsAddingColumn(true)}
              >
                <Icon name="plus" size="sm" className="mr-2" />
                {t('kanban.addColumn', 'Add Column')}
              </Button>
            )}
          </div>
        )}
      </div>
    </DndProvider>
  );
};

export default KanbanBoard;
