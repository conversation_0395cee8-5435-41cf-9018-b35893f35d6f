/**
 * Text Message Component
 * Renders plain text messages with optional formatting
 */

import React from 'react';
import { Typography } from '@/shared/components/common';
import { TextContentData } from '@/shared/websocket/types/chat-message.types';

// Props interface
export interface TextMessageProps {
  data: TextContentData;
  className?: string;
}

/**
 * Text Message Component
 */
const TextMessage: React.FC<TextMessageProps> = ({ data, className = '' }) => {
  const { text, formatting } = data;

  // Apply text formatting if available
  const renderFormattedText = () => {
    if (!formatting) {
      return <Typography variant="body2">{text}</Typography>;
    }

    // Split text into segments with formatting
    const segments: Array<{
      text: string;
      start: number;
      end: number;
      formats: string[];
    }> = [];

    // Create segments based on formatting ranges
    const formatTypes = Object.keys(formatting) as Array<keyof typeof formatting>;
    const allRanges: Array<{ start: number; end: number; type: string }> = [];

    formatTypes.forEach(type => {
      const ranges = formatting[type] || [];
      ranges.forEach(([start, end]) => {
        allRanges.push({ start, end, type });
      });
    });

    // Sort ranges by start position
    allRanges.sort((a, b) => a.start - b.start);

    // Build segments
    let currentPos = 0;
    const textLength = text.length;

    while (currentPos < textLength) {
      // Find next formatting range
      const nextRange = allRanges.find(range => range.start >= currentPos);
      
      if (!nextRange) {
        // No more formatting, add rest of text
        segments.push({
          text: text.slice(currentPos),
          start: currentPos,
          end: textLength,
          formats: [],
        });
        break;
      }

      // Add unformatted text before next range
      if (nextRange.start > currentPos) {
        segments.push({
          text: text.slice(currentPos, nextRange.start),
          start: currentPos,
          end: nextRange.start,
          formats: [],
        });
      }

      // Find all overlapping formats for this range
      const overlappingFormats = allRanges
        .filter(range => 
          range.start <= nextRange.start && 
          range.end >= nextRange.end
        )
        .map(range => range.type);

      // Add formatted segment
      segments.push({
        text: text.slice(nextRange.start, nextRange.end),
        start: nextRange.start,
        end: nextRange.end,
        formats: overlappingFormats,
      });

      currentPos = nextRange.end;
    }

    // Render segments with formatting
    return (
      <Typography variant="body2" className="whitespace-pre-wrap">
        {segments.map((segment, index) => {
          let element = <span key={index}>{segment.text}</span>;

          // Apply formatting styles
          segment.formats.forEach(format => {
            switch (format) {
              case 'bold':
                element = <strong key={`${index}-bold`}>{element}</strong>;
                break;
              case 'italic':
                element = <em key={`${index}-italic`}>{element}</em>;
                break;
              case 'code':
                element = (
                  <code 
                    key={`${index}-code`}
                    className="bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded text-sm font-mono"
                  >
                    {element}
                  </code>
                );
                break;
              case 'underline':
                element = <u key={`${index}-underline`}>{element}</u>;
                break;
              case 'strikethrough':
                element = <s key={`${index}-strikethrough`}>{element}</s>;
                break;
            }
          });

          return element;
        })}
      </Typography>
    );
  };

  return (
    <div className={`p-3 rounded-lg bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 ${className}`}>
      {renderFormattedText()}
    </div>
  );
};

export default TextMessage;
