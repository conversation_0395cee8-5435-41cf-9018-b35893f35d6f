import { formatDistanceToNow } from 'date-fns';
import { enUS, vi, zhCN } from 'date-fns/locale';
import React from 'react';
import { useTranslation } from 'react-i18next';

import {
  Button,
  Icon,
  Typography,
  Badge,
  Modal,
  Textarea,
  Input,
  Select,
} from '@/shared/components/common';

import { KanbanCard } from '../types/kanban.types';

export interface KanbanCardDetailsProps {
  /**
   * Thẻ cần hiển thị chi tiết
   */
  card: KanbanCard;

  /**
   * Callback khi đóng modal
   */
  onClose: () => void;

  /**
   * Callback khi cập nhật thẻ
   */
  onUpdate?: (cardId: string, updates: Partial<KanbanCard>) => void;

  /**
   * Callback khi xóa thẻ
   */
  onDelete?: (cardId: string) => void;

  /**
   * Có cho phép chỉnh sửa không
   * @default true
   */
  editable?: boolean;

  /**
   * Custom class name
   */
  className?: string;
}

/**
 * Component hiển thị chi tiết thẻ Kanban
 */
const KanbanCardDetails: React.FC<KanbanCardDetailsProps> = ({
  card,
  onClose,
  onUpdate,
  onDelete,
  editable = true,
  className = '',
}) => {
  const { t, i18n } = useTranslation();
  const [isEditing, setIsEditing] = React.useState(false);
  const [editedCard, setEditedCard] = React.useState<Partial<KanbanCard>>({
    title: card.title,
    description: card.description,
    priority: card.priority,
    dueDate: card.dueDate,
  });

  // Xác định locale cho date-fns
  const getLocale = () => {
    switch (i18n.language) {
      case 'vi':
        return vi;
      case 'zh':
        return zhCN;
      default:
        return enUS;
    }
  };

  // Format due date
  const formatDueDate = () => {
    if (!card.dueDate) {return '';}

    try {
      const date = new Date(card.dueDate);
      return formatDistanceToNow(date, { addSuffix: true, locale: getLocale() });
    } catch {
      return card.dueDate;
    }
  };

  // Xác định variant cho mức độ ưu tiên
  const getPriorityVariant = (): 'primary' | 'success' | 'warning' | 'danger' | 'info' => {
    const priorityVariants: Record<string, 'primary' | 'success' | 'warning' | 'danger' | 'info'> =
      {
        low: 'info',
        medium: 'success',
        high: 'warning',
        urgent: 'danger',
      };

    return priorityVariants[card.priority || 'medium'];
  };

  // Xử lý cập nhật thẻ
  const handleUpdate = () => {
    if (onUpdate) {
      onUpdate(card.id, editedCard);
    }
    setIsEditing(false);
  };

  // Xử lý xóa thẻ
  const handleDelete = () => {
    if (onDelete) {
      onDelete(card.id);
      onClose();
    }
  };

  // Xử lý hủy chỉnh sửa
  const handleCancelEdit = () => {
    setEditedCard({
      title: card.title,
      description: card.description,
      priority: card.priority,
      dueDate: card.dueDate,
    });
    setIsEditing(false);
  };

  // Các tùy chọn cho Select
  const priorityOptions = [
    { value: 'low', label: t('kanban.priority.low', 'Low') },
    { value: 'medium', label: t('kanban.priority.medium', 'Medium') },
    { value: 'high', label: t('kanban.priority.high', 'High') },
    { value: 'urgent', label: t('kanban.priority.urgent', 'Urgent') },
  ];

  return (
    <Modal
      title={isEditing ? t('kanban.cardDetails.editTitle', 'Edit Card') : card.title}
      onClose={onClose}
      className={`max-w-2xl ${className}`}
      footer={
        <div className="flex justify-between">
          <div>
            {editable && !isEditing && (
              <Button variant="danger" onClick={handleDelete}>
                <Icon name="trash" className="mr-1" />
                {t('kanban.cardDetails.delete', 'Delete')}
              </Button>
            )}
          </div>
          <div className="flex space-x-2">
            {isEditing ? (
              <>
                <Button variant="outline" onClick={handleCancelEdit}>
                  {t('kanban.cardDetails.cancel', 'Cancel')}
                </Button>
                <Button variant="primary" onClick={handleUpdate}>
                  {t('kanban.cardDetails.save', 'Save')}
                </Button>
              </>
            ) : (
              <>
                <Button variant="outline" onClick={onClose}>
                  {t('kanban.cardDetails.close', 'Close')}
                </Button>
                {editable && (
                  <Button variant="primary" onClick={() => setIsEditing(true)}>
                    <Icon name="edit" className="mr-1" />
                    {t('kanban.cardDetails.edit', 'Edit')}
                  </Button>
                )}
              </>
            )}
          </div>
        </div>
      }
    >
      <div className="space-y-4">
        {/* Header */}
        {!isEditing && (
          <div className="flex items-center justify-between">
            <Badge variant={getPriorityVariant()} size="md">
              {t(`kanban.priority.${card.priority || 'medium'}`)}
            </Badge>
            {card.dueDate && (
              <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                <Icon name="calendar" size="sm" className="mr-1" />
                <span>{formatDueDate()}</span>
              </div>
            )}
          </div>
        )}

        {/* Edit Form */}
        {isEditing ? (
          <div className="space-y-4">
            <div>
              <Typography variant="body2" className="mb-1 font-medium">
                {t('kanban.cardDetails.title', 'Title')}
              </Typography>
              <Input
                value={editedCard.title}
                onChange={e => setEditedCard({ ...editedCard, title: e.target.value })}
                className="w-full"
              />
            </div>
            <div>
              <Typography variant="body2" className="mb-1 font-medium">
                {t('kanban.cardDetails.description', 'Description')}
              </Typography>
              <Textarea
                value={editedCard.description || ''}
                onChange={e => setEditedCard({ ...editedCard, description: e.target.value })}
                className="w-full"
                rows={5}
              />
            </div>
            <div>
              <Typography variant="body2" className="mb-1 font-medium">
                {t('kanban.cardDetails.priority', 'Priority')}
              </Typography>
              <Select
                options={priorityOptions}
                value={editedCard.priority}
                onChange={value => setEditedCard({ ...editedCard, priority: value as string })}
                className="w-full"
              />
            </div>
            <div>
              <Typography variant="body2" className="mb-1 font-medium">
                {t('kanban.cardDetails.dueDate', 'Due Date')}
              </Typography>
              <Input
                type="date"
                value={
                  editedCard.dueDate ? new Date(editedCard.dueDate).toISOString().split('T')[0] : ''
                }
                onChange={e =>
                  setEditedCard({
                    ...editedCard,
                    dueDate: e.target.value ? new Date(e.target.value).toISOString() : undefined,
                  })
                }
                className="w-full"
              />
            </div>
          </div>
        ) : (
          <>
            {/* Description */}
            {card.description && (
              <div>
                <Typography variant="body2" className="font-medium mb-1">
                  {t('kanban.cardDetails.description', 'Description')}
                </Typography>
                <Typography variant="body1" className="whitespace-pre-line">
                  {card.description}
                </Typography>
              </div>
            )}

            {/* Assignee */}
            {card.assignee && (
              <div>
                <Typography variant="body2" className="font-medium mb-1">
                  {t('kanban.cardDetails.assignee', 'Assignee')}
                </Typography>
                <div className="flex items-center">
                  <div className="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center overflow-hidden mr-2">
                    {card.assignee.avatar ? (
                      <img
                        src={card.assignee.avatar}
                        alt={card.assignee.name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <Typography variant="body2">
                        {card.assignee.name.charAt(0).toUpperCase()}
                      </Typography>
                    )}
                  </div>
                  <Typography variant="body1">{card.assignee.name}</Typography>
                </div>
              </div>
            )}

            {/* Labels */}
            {card.labels && card.labels.length > 0 && (
              <div>
                <Typography variant="body2" className="font-medium mb-1">
                  {t('kanban.cardDetails.labels', 'Labels')}
                </Typography>
                <div className="flex flex-wrap gap-2">
                  {card.labels.map(label => (
                    <Badge
                      key={label.id}
                      variant="info"
                      size="md"
                      className={`bg-${label.color || 'gray'}-100 text-${label.color || 'gray'}-800`}
                    >
                      {label.name}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Progress */}
            {card.progress !== undefined && (
              <div>
                <Typography variant="body2" className="font-medium mb-1">
                  {t('kanban.cardDetails.progress', 'Progress')}
                </Typography>
                <div className="flex items-center">
                  <div className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden mr-2">
                    <div
                      className={`h-full ${
                        getPriorityVariant() === 'primary'
                          ? 'bg-primary'
                          : getPriorityVariant() === 'success'
                            ? 'bg-green-500'
                            : getPriorityVariant() === 'warning'
                              ? 'bg-yellow-500'
                              : getPriorityVariant() === 'danger'
                                ? 'bg-red-500'
                                : 'bg-blue-500'
                      }`}
                      style={{ width: `${card.progress}%` }}
                    ></div>
                  </div>
                  <Typography variant="body2">{card.progress}%</Typography>
                </div>
              </div>
            )}

            {/* Comments */}
            {card.commentsCount !== undefined && card.commentsCount > 0 && (
              <div>
                <Typography variant="body2" className="font-medium mb-1">
                  {t('kanban.cardDetails.comments', 'Comments')}
                </Typography>
                <Typography variant="body2" className="text-gray-500 dark:text-gray-400">
                  {t('kanban.cardDetails.commentsCount', '{{count}} comments', {
                    count: card.commentsCount,
                  })}
                </Typography>
              </div>
            )}

            {/* Attachments */}
            {card.attachmentsCount !== undefined && card.attachmentsCount > 0 && (
              <div>
                <Typography variant="body2" className="font-medium mb-1">
                  {t('kanban.cardDetails.attachments', 'Attachments')}
                </Typography>
                <Typography variant="body2" className="text-gray-500 dark:text-gray-400">
                  {t('kanban.cardDetails.attachmentsCount', '{{count}} attachments', {
                    count: card.attachmentsCount,
                  })}
                </Typography>
              </div>
            )}
          </>
        )}
      </div>
    </Modal>
  );
};

export default KanbanCardDetails;
