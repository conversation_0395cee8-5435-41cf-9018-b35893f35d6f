import clsx from 'clsx';
import { useCallback, useEffect, useRef, useState } from 'react';
import { FiLoader } from 'react-icons/fi';
import { twMerge } from 'tailwind-merge';

import Card from '../Card';
import Icon from '../Icon';
import ScrollArea from '../ScrollArea';
import Table from '../Table';

import { DataDisplayProps, ViewMode } from './types';

/**
 * Component hiển thị dữ liệu có thể chuyển đổi giữa dạng bảng và dạng card
 */
const DataDisplay = <T extends Record<string, unknown>>({
  data = [],
  columns = [],
  renderItem,
  keyField = 'id',
  defaultViewMode = 'table',
  onViewModeChange,
  itemIcon,
  onItemClick,
  onLoadMore,
  loading = false,
  hasMore = false,
  maxHeight = '600px',
  gridColumns = 3,
  gridGap = 4,
  className,
  tableProps,
}: DataDisplayProps<T>) => {
  // State cho chế độ hiển thị
  const [viewMode, setViewMode] = useState<ViewMode>(defaultViewMode);

  // Ref cho scroll area để kiểm tra vị trí cuộn
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const loaderRef = useRef<HTMLDivElement>(null);
  const [loadingMore, setLoadingMore] = useState(false);

  // Xử lý chuyển đổi chế độ hiển thị
  const handleViewModeChange = (mode: ViewMode) => {
    setViewMode(mode);
    onViewModeChange?.(mode);
  };

  // Hàm xử lý khi scroll đến cuối
  const handleObserver = useCallback(
    (entries: IntersectionObserverEntry[]) => {
      const target = entries[0];
      if (target.isIntersecting && hasMore && !loadingMore) {
        setLoadingMore(true);
        onLoadMore?.();
      }
    },
    [hasMore, loadingMore, onLoadMore]
  );

  // Thiết lập intersection observer để phát hiện khi cuộn đến cuối
  useEffect(() => {
    const observer = new IntersectionObserver(handleObserver, {
      root: scrollAreaRef.current,
      rootMargin: '20px',
      threshold: 0.1,
    });

    if (loaderRef.current) {
      observer.observe(loaderRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, [handleObserver]);

  // Reset trạng thái loading khi dữ liệu thay đổi
  useEffect(() => {
    setLoadingMore(false);
  }, [data]);

  // Tạo grid template dựa trên số cột
  const gridTemplateColumns = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 sm:grid-cols-2',
    3: 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3',
    4: 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4',
    5: 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5',
    6: 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-6',
  }[gridColumns];

  // Tạo khoảng cách giữa các card
  const gap = {
    1: 'gap-1',
    2: 'gap-2',
    3: 'gap-3',
    4: 'gap-4',
    5: 'gap-5',
    6: 'gap-6',
    8: 'gap-8',
  }[gridGap];

  // Render default card nếu không có renderItem
  const renderDefaultCard = (item: T, _index: number) => {
    const iconEl = typeof itemIcon === 'function' ? itemIcon(item) : itemIcon;

    return (
      <div
        className={clsx(
          'h-full flex flex-col p-4 cursor-pointer transition-all hover:shadow-md',
          onItemClick && 'hover:border-primary'
        )}
        onClick={() => onItemClick?.(item)}
      >
        {iconEl && <div className="mb-2">{iconEl}</div>}
        <div className="flex-1">
          {Object.entries(item).map(([key, value]) => {
            // Skip key field if it's not user-friendly
            if (key === keyField && key === 'id') {return null;}

            return (
              <div key={key} className="mb-1">
                <div className="text-sm text-gray-500 dark:text-gray-400">{key}</div>
                <div>{String(value)}</div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <div className={twMerge('w-full', className)}>
      {/* View mode switcher */}
      <div className="flex justify-end mb-4">
        <div className="flex items-stretch bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden">
          <button
            className={`flex items-center justify-center px-6 py-2.5 ${
              viewMode === 'table'
                ? 'bg-primary text-white'
                : 'text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'
            } transition-colors w-20`}
            onClick={() => handleViewModeChange('table')}
            title="Hiển thị dạng bảng"
          >
            <Icon name="list" size="md" />
          </button>
          <button
            className={`flex items-center justify-center px-6 py-2.5 ${
              viewMode === 'card'
                ? 'bg-primary text-white'
                : 'text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'
            } transition-colors w-20`}
            onClick={() => handleViewModeChange('card')}
            title="Hiển thị dạng card"
          >
            <Icon name="grid" size="md" />
          </button>
        </div>
      </div>

      {/* Content based on view mode */}
      {viewMode === 'table' ? (
        <Table columns={columns} data={data} rowKey={keyField} {...tableProps} />
      ) : (
        <Card>
          <ScrollArea
            ref={scrollAreaRef}
            style={{ height: maxHeight }}
            className="overflow-y-auto"
            autoHide={true}
          >
            <div className={clsx('grid', gridTemplateColumns, gap, 'p-4')}>
              {data.map((item, index) => (
                <Card key={String(item[keyField] || index)} className="h-full overflow-hidden">
                  {renderItem ? renderItem({ item, index }) : renderDefaultCard(item, index)}
                </Card>
              ))}
            </div>

            {/* Loader element for infinite scrolling */}
            {(hasMore || loading) && (
              <div ref={loaderRef} className="flex justify-center items-center py-4">
                {loadingMore && (
                  <div className="flex items-center">
                    <FiLoader className="animate-spin mr-2" />
                    <span>Đang tải thêm...</span>
                  </div>
                )}
              </div>
            )}
          </ScrollArea>
        </Card>
      )}
    </div>
  );
};

export default DataDisplay;
