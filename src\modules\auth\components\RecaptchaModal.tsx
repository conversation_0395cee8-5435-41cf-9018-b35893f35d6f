import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';

import { Modal } from '@/shared/components/common';
import { env } from '@/shared/utils';

import { useRecaptcha } from '../hooks/useRecaptcha';

interface RecaptchaModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (token: string) => void;
  onError?: (error: string) => void;
}

/**
 * Modal component for reCAPTCHA verification
 */
const RecaptchaModal: React.FC<RecaptchaModalProps> = ({ isOpen, onClose, onSuccess, onError }) => {
  const { t } = useTranslation();

  // Sử dụng hook useRecaptcha với containerId cố định
  const {
    recaptchaToken,
    isLoaded,
    error: recaptchaError,
    resetRecaptcha,
  } = useRecaptcha('recaptcha-modal-container', 'LOGIN');

  // Xử lý khi có token reCAPTCHA
  useEffect(() => {
    if (recaptchaToken && isOpen) {
      onSuccess(recaptchaToken);
      onClose();
    }
  }, [recaptchaToken, isOpen, onSuccess, onClose]);

  // Xử lý lỗi reCAPTCHA
  useEffect(() => {
    if (recaptchaError && isOpen) {
      onError?.(recaptchaError);
    }
  }, [recaptchaError, isOpen, onError]);

  // Reset reCAPTCHA khi đóng modal và khởi tạo lại khi mở
  useEffect(() => {
    if (isOpen) {
      // Delay để đảm bảo modal đã render hoàn toàn
      const timer = setTimeout(() => {
        // Kiểm tra xem container đã tồn tại chưa và khởi tạo lại reCAPTCHA
        const container = document.getElementById('recaptcha-modal-container');
        if (container) {
          // Reset và khởi tạo lại reCAPTCHA
          resetRecaptcha();
        } else {
          console.warn('reCAPTCHA container not found, modal may not be fully rendered');
        }
      }, 300);
      return () => clearTimeout(timer);
    } else {
      resetRecaptcha();
      return undefined;
    }
  }, [isOpen, resetRecaptcha]);

  const handleClose = () => {
    resetRecaptcha();
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={t('auth.recaptchaVerification', 'Xác thực reCAPTCHA')}
      size="md"
      closeOnClickOutside={false}
      closeOnEsc={true}
      footer={false}
    >
      <div className="space-y-4">
        <p className="text-sm text-gray-600 dark:text-gray-400">
          {t(
            'auth.recaptchaDescription',
            'Vui lòng hoàn thành xác thực reCAPTCHA để tiếp tục đăng nhập.'
          )}
        </p>

        {/* Container cho reCAPTCHA */}
        <div className="flex justify-center relative">
          {/* Loading indicator khi reCAPTCHA đang tải */}
          {!isLoaded && !recaptchaError && (
            <div className="flex flex-col items-center justify-center space-y-2 py-4 min-h-[78px]">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {t('auth.loadingRecaptcha', 'Đang tải reCAPTCHA...')}
              </p>
            </div>
          )}

          {/* Container cho reCAPTCHA - chỉ hiển thị khi đã tải xong hoặc có lỗi */}
          <div
            id="recaptcha-modal-container"
            className={`g-recaptcha flex justify-center min-h-[78px] dark:border-gray-700 rounded-md ${
              !isLoaded && !recaptchaError ? 'hidden' : ''
            }`}
            data-sitekey={env.recaptchaSiteKey}
          ></div>
        </div>
      </div>
    </Modal>
  );
};

export default RecaptchaModal;
