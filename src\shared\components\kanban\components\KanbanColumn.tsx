import React, { memo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Icon, Typography } from '@/shared/components/common';

import { useColumnDrop, useColumnDragDrop } from '../hooks/useKanbanDragDrop';
import {
  KanbanCard as KanbanCardType,
  KanbanColumn as KanbanColumnType,
} from '../types/kanban.types';

import KanbanAddCard from './KanbanAddCard';
import KanbanCard from './KanbanCard';

export interface KanbanColumnProps {
  /**
   * Dữ liệu cột
   */
  column: KanbanColumnType;

  /**
   * Danh sách các thẻ trong cột
   */
  cards: KanbanCardType[];

  /**
   * Vị trí của cột
   */
  index: number;

  /**
   * Callback khi kéo thả thẻ
   */
  onCardMove?: (
    cardId: string,
    sourceColumnId: string,
    targetColumnId: string,
    newIndex: number
  ) => void;

  /**
   * Callback khi thêm thẻ mới
   */
  onCardAdd?: (columnId: string, card: Partial<KanbanCardType>) => void;

  /**
   * Callback khi click vào thẻ
   */
  onCardClick?: (card: KanbanCardType) => void;

  /**
   * Callback khi sửa thẻ
   */
  onCardEdit?: (cardId: string, columnId: string, updates: Partial<KanbanCardType>) => void;

  /**
   * Callback khi xóa thẻ
   */
  onCardDelete?: (cardId: string, columnId: string) => void;

  /**
   * Callback khi sửa cột
   */
  onColumnEdit?: (columnId: string, updates: Partial<KanbanColumnType>) => void;

  /**
   * Callback khi xóa cột
   */
  onColumnDelete?: (columnId: string) => void;

  /**
   * Callback khi thay đổi thứ tự cột
   */
  onColumnReorder?: (columnId: string, newIndex: number) => void;

  /**
   * Có cho phép thêm thẻ mới không
   * @default true
   */
  allowAddCard?: boolean;

  /**
   * Có cho phép kéo thả cột không
   * @default true
   */
  allowColumnDrag?: boolean;

  /**
   * Có cho phép kéo thả thẻ không
   * @default true
   */
  allowCardDrag?: boolean;

  /**
   * Custom class name
   */
  className?: string;
}

/**
 * Component hiển thị một cột trong Kanban Board
 */
const KanbanColumn: React.FC<KanbanColumnProps> = ({
  column,
  cards,
  index,
  onCardMove,
  onCardAdd,
  onCardClick,
  onCardEdit,
  onCardDelete,
  // onColumnEdit, // Sẽ được sử dụng trong tương lai
  onColumnDelete,
  onColumnReorder,
  allowAddCard = true,
  allowColumnDrag = true,
  allowCardDrag = true,
  className = '',
}) => {
  const { t } = useTranslation();
  const [isAddingCard, setIsAddingCard] = useState(false);

  // Xử lý drop cho cột
  const handleCardMove = (result: {
    cardId: string;
    sourceColumnId: string;
    targetColumnId: string;
    newIndex: number;
  }) => {
    if (onCardMove) {
      onCardMove(result.cardId, result.sourceColumnId, result.targetColumnId, result.newIndex);
    }
  };

  const { dropRef, isOver, canDrop } = useColumnDrop(column.id, handleCardMove);

  // Xử lý drag & drop cho cột
  const { ref: columnDragRef, isDragging } = useColumnDragDrop(
    column,
    index,
    onColumnReorder,
    allowColumnDrag
  );

  // Xử lý thêm thẻ mới
  const handleAddCard = (card: Partial<KanbanCardType>) => {
    if (onCardAdd) {
      onCardAdd(column.id, card);
      setIsAddingCard(false);
    }
  };

  // Xử lý xóa cột
  const handleDeleteColumn = () => {
    if (onColumnDelete) {
      onColumnDelete(column.id);
    }
  };

  // Xử lý sửa tiêu đề cột - Sẽ được sử dụng trong tương lai
  // const handleEditColumnTitle = (title: string) => {
  //   if (onColumnEdit) {
  //     onColumnEdit(column.id, { title });
  //   }
  // };

  // Kết hợp ref cho cả drag và drop
  const combinedRef = (node: HTMLDivElement) => {
    columnDragRef(node);
    dropRef(node);
  };

  // Xác định màu cho cột
  const getColumnColor = () => {
    return column.color || 'gray';
  };

  return (
    <div
      ref={combinedRef}
      className={`
        kanban-column
        flex flex-col h-full min-w-[280px] max-w-[280px]
        ${isDragging ? 'opacity-50' : 'opacity-100'}
        ${isOver ? 'is-over' : ''}
        ${canDrop ? 'can-drop' : ''}
        ${isOver && canDrop ? 'ring-2 ring-primary ring-opacity-70' : ''}
        ${className}
      `}
      style={{
        transition: 'all 0.2s ease',
        transform: isOver && canDrop ? 'scale(1.02)' : 'scale(1)',
      }}
    >
      {/* Column Header */}
      <div
        className={`
          px-3 py-2 rounded-t-md flex justify-between items-center
          bg-${getColumnColor()}-100 dark:bg-${getColumnColor()}-900/30
          border border-${getColumnColor()}-200 dark:border-${getColumnColor()}-800
        `}
        style={{ cursor: allowColumnDrag ? 'grab' : 'default' }}
      >
        <div className="flex items-center">
          <div className={`w-3 h-3 rounded-full bg-${getColumnColor()}-500 mr-2`}></div>
          <Typography variant="subtitle1" className="font-medium">
            {column.title}
          </Typography>
          {column.cardIds.length > 0 && (
            <Typography variant="body2" className="ml-2 text-gray-500 dark:text-gray-400">
              {column.cardIds.length}
            </Typography>
          )}
        </div>
        <div className="flex items-center space-x-1">
          {allowAddCard && (
            <Button
              variant="ghost"
              size="sm"
              iconOnly
              onClick={() => setIsAddingCard(true)}
              aria-label={t('kanban.addCard', 'Add card')}
            >
              <Icon name="plus" size="sm" />
            </Button>
          )}
          <Button
            variant="ghost"
            size="sm"
            iconOnly
            onClick={handleDeleteColumn}
            aria-label={t('kanban.deleteColumn', 'Delete column')}
          >
            <Icon name="trash" size="sm" />
          </Button>
        </div>
      </div>

      {/* Column Content */}
      <div
        className={`
          flex-1 p-2 overflow-y-auto
          bg-gray-50 dark:bg-gray-900
          border-x border-b border-gray-200 dark:border-gray-700 rounded-b-md
        `}
        style={{ maxHeight: 'calc(100vh - 200px)' }}
      >
        {/* Add Card Form */}
        {isAddingCard && (
          <KanbanAddCard
            onAdd={handleAddCard}
            onCancel={() => setIsAddingCard(false)}
            className="mb-2"
          />
        )}

        {/* Cards */}
        <div className="space-y-2">
          {cards.map((card, cardIndex) => (
            <KanbanCard
              key={card.id}
              card={card}
              columnId={column.id}
              index={cardIndex}
              onCardMove={onCardMove}
              onClick={onCardClick}
              onEdit={onCardEdit ? card => onCardEdit(card.id, column.id, card) : undefined}
              onDelete={onCardDelete ? card => onCardDelete(card.id, column.id) : undefined}
              allowDrag={allowCardDrag}
            />
          ))}
        </div>

        {/* Empty State */}
        {cards.length === 0 && !isAddingCard && (
          <div className="flex flex-col items-center justify-center h-20 text-gray-400 dark:text-gray-600">
            <Typography variant="body2">{t('kanban.emptyColumn', 'No cards')}</Typography>
            {allowAddCard && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsAddingCard(true)}
                className="mt-1"
              >
                <Icon name="plus" size="sm" className="mr-1" />
                {t('kanban.addCard', 'Add card')}
              </Button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default memo(KanbanColumn);
