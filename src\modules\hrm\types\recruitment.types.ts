/**
 * Types cho module Recruitment
 */

// Enum cho trạng thái vị trí tuyển dụng
export enum JobPositionStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  PAUSED = 'paused',
  CLOSED = 'closed',
  CANCELLED = 'cancelled',
}

// Enum cho mức độ ưu tiên
export enum Priority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

// Enum cho loại công việc
export enum JobType {
  FULL_TIME = 'full_time',
  PART_TIME = 'part_time',
  CONTRACT = 'contract',
  INTERNSHIP = 'internship',
  FREELANCE = 'freelance',
}

// Enum cho trạng thái ứng viên
export enum CandidateStatus {
  NEW = 'new',
  SCREENING = 'screening',
  INTERVIEW = 'interview',
  TECHNICAL_TEST = 'technical_test',
  FINAL_INTERVIEW = 'final_interview',
  OFFER = 'offer',
  HIRED = 'hired',
  REJECTED = 'rejected',
  WITHDRAWN = 'withdrawn',
}

// Interface cho vị trí tuyển dụng
export interface JobPosition {
  id: string;
  title: string;
  description: string;
  requirements: string;
  benefits: string;
  departmentId: string;
  departmentName?: string;
  location: string;
  jobType: JobType;
  salaryMin?: number;
  salaryMax?: number;
  currency: string;
  experienceYears: number;
  status: JobPositionStatus;
  priority: Priority;
  openings: number;
  publishedAt?: string;
  closedAt?: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

// Interface cho ứng viên
export interface Candidate {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address?: string;
  resumeUrl?: string;
  coverLetterUrl?: string;
  linkedinUrl?: string;
  portfolioUrl?: string;
  status: CandidateStatus;
  appliedAt: string;
  lastContactAt?: string;
  notes?: string;
  rating?: number;
  jobPositionId: string;
  jobPositionTitle?: string;
  createdAt: string;
  updatedAt: string;
}

// Interface cho quy trình tuyển dụng
export interface RecruitmentProcess {
  id: string;
  name: string;
  description?: string;
  steps: RecruitmentStep[];
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
}

// Interface cho bước trong quy trình tuyển dụng
export interface RecruitmentStep {
  id: string;
  name: string;
  description?: string;
  order: number;
  isRequired: boolean;
  estimatedDays: number;
  status: CandidateStatus;
}

// DTOs cho API calls
export interface CreateJobPositionDto {
  title: string;
  description: string;
  requirements: string;
  benefits?: string;
  departmentId: string;
  location: string;
  jobType: JobType;
  salaryMin?: number;
  salaryMax?: number;
  currency: string;
  experienceYears: number;
  priority: Priority;
  openings: number;
}

export interface UpdateJobPositionDto extends Partial<CreateJobPositionDto> {
  status?: JobPositionStatus;
}

export interface CreateCandidateDto {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address?: string;
  resumeUrl?: string;
  coverLetterUrl?: string;
  linkedinUrl?: string;
  portfolioUrl?: string;
  jobPositionId: string;
  notes?: string;
}

export interface UpdateCandidateDto extends Partial<CreateCandidateDto> {
  status?: CandidateStatus;
  rating?: number;
  notes?: string;
}

// Query DTOs
export interface JobPositionQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  status?: JobPositionStatus;
  departmentId?: string;
  jobType?: JobType;
  priority?: Priority;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface CandidateQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  status?: CandidateStatus;
  jobPositionId?: string;
  rating?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Response types
export interface JobPositionResponse {
  items: JobPosition[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface CandidateResponse {
  items: Candidate[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Statistics types
export interface RecruitmentStats {
  totalPositions: number;
  activePositions: number;
  totalCandidates: number;
  newCandidates: number;
  hiredThisMonth: number;
  averageTimeToHire: number;
  topDepartments: Array<{
    departmentName: string;
    openPositions: number;
  }>;
  candidatesByStatus: Array<{
    status: CandidateStatus;
    count: number;
  }>;
}
