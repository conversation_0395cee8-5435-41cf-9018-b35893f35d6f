/**
 * Form Sync Hook
 * Handles real-time form synchronization via WebSocket
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { getChatWebSocketService } from '../../services/chat-websocket.service';
import { FormSchema } from '../types/chat-message.types';
import { 
  UseFormSyncOptions, 
  UseFormSyncReturn, 
  FormState, 
  FormFieldState,
  FormValidationResult,
  FormSubmissionResult,
  FormSyncError
} from '../types/form-sync.types';
import { 
  FormPrefillData, 
  FormFieldSyncData, 
  FormValidationResultData, 
  FormSubmitResultData 
} from '../types/websocket-events.types';

/**
 * Form Sync Hook
 */
export function useFormSync(options: UseFormSyncOptions): UseFormSyncReturn {
  const {
    formId,
    messageId,
    roomId,
    schema,
    initialData = {},
    enableRealTimeSync = true,
    enableValidation = true,
    syncDebounceMs = 300,
    validationDebounceMs = 500,
    onSubmit,
    onValidation,
    onError,
  } = options;

  // State
  const [formData, setFormData] = useState<Record<string, unknown>>(initialData);
  const [fieldStates, setFieldStates] = useState<Record<string, FormFieldState>>({});
  const [formState, setFormState] = useState<FormState>({
    formId,
    messageId,
    data: initialData,
    errors: {},
    warnings: {},
    isValid: true,
    isDirty: false,
    isSubmitting: false,
    lastUpdated: new Date().toISOString(),
    lastValidated: new Date().toISOString(),
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [warnings, setWarnings] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Refs
  const serviceRef = useRef(getChatWebSocketService());
  const syncTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const validationTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastSyncDataRef = useRef<Record<string, unknown>>(initialData);

  // Initialize field states
  useEffect(() => {
    const newFieldStates: Record<string, FormFieldState> = {};
    
    Object.keys(schema.properties).forEach(fieldName => {
      newFieldStates[fieldName] = {
        name: fieldName,
        value: formData[fieldName],
        isValid: true,
        isDirty: false,
        isTouched: false,
        isValidating: false,
        lastUpdated: new Date().toISOString(),
      };
    });

    setFieldStates(newFieldStates);
  }, [schema, formData]);

  // Set up WebSocket event listeners
  useEffect(() => {
    const service = serviceRef.current;
    if (!service) return;

    const unsubscribers: Array<() => void> = [];

    // Form prefill
    unsubscribers.push(
      service.on('form_prefill', (data: FormPrefillData) => {
        if (data.formId === formId && data.messageId === messageId) {
          setFormData(prev => ({ ...prev, ...data.data }));
          lastSyncDataRef.current = { ...lastSyncDataRef.current, ...data.data };
          
          // Update form state
          setFormState(prev => ({
            ...prev,
            data: { ...prev.data, ...data.data },
            lastUpdated: new Date().toISOString(),
          }));
        }
      })
    );

    // Field sync from server
    unsubscribers.push(
      service.on('form_field_sync', (data: FormFieldSyncData) => {
        if (data.formId === formId && data.messageId === messageId && data.source === 'server') {
          setFormData(prev => ({
            ...prev,
            [data.fieldName]: data.fieldValue,
          }));

          // Update field state
          setFieldStates(prev => ({
            ...prev,
            [data.fieldName]: {
              ...prev[data.fieldName],
              value: data.fieldValue,
              lastUpdated: new Date().toISOString(),
            },
          }));
        }
      })
    );

    // Validation result
    unsubscribers.push(
      service.on('form_validation_result', (data: FormValidationResultData) => {
        if (data.formId === formId && data.messageId === messageId) {
          setErrors(data.errors);
          
          setFormState(prev => ({
            ...prev,
            errors: data.errors,
            isValid: data.isValid,
            lastValidated: new Date().toISOString(),
          }));

          if (onValidation) {
            onValidation({
              formId: data.formId,
              messageId: data.messageId,
              isValid: data.isValid,
              errors: data.errors,
              fieldName: data.fieldName,
              timestamp: new Date().toISOString(),
            });
          }
        }
      })
    );

    // Form submission result
    unsubscribers.push(
      service.on('form_submit_result', (data: FormSubmitResultData) => {
        if (data.formId === formId && data.messageId === messageId) {
          setIsSubmitting(false);
          
          setFormState(prev => ({
            ...prev,
            isSubmitting: false,
          }));

          if (!data.success && data.error && onError) {
            onError(data.error);
          }

          if (data.validationErrors) {
            setErrors(data.validationErrors);
            setFormState(prev => ({
              ...prev,
              errors: data.validationErrors || {},
              isValid: false,
            }));
          }
        }
      })
    );

    return () => {
      unsubscribers.forEach(unsubscribe => unsubscribe());
    };
  }, [formId, messageId, onValidation, onError]);

  // Debounced sync to server
  const debouncedSync = useCallback((fieldName: string, value: unknown) => {
    if (!enableRealTimeSync) return;

    if (syncTimeoutRef.current) {
      clearTimeout(syncTimeoutRef.current);
    }

    syncTimeoutRef.current = setTimeout(() => {
      const service = serviceRef.current;
      if (service) {
        service.updateFormField(formId, messageId, fieldName, value, roomId);
      }
    }, syncDebounceMs);
  }, [formId, messageId, roomId, enableRealTimeSync, syncDebounceMs]);

  // Debounced validation
  const debouncedValidation = useCallback((fieldName?: string) => {
    if (!enableValidation) return;

    if (validationTimeoutRef.current) {
      clearTimeout(validationTimeoutRef.current);
    }

    validationTimeoutRef.current = setTimeout(() => {
      const service = serviceRef.current;
      if (service) {
        service.socket?.emit('form_validate', {
          formId,
          messageId,
          formData,
          fieldName,
          roomId,
        });
      }
    }, validationDebounceMs);
  }, [formId, messageId, formData, roomId, enableValidation, validationDebounceMs]);

  // Update field
  const updateField = useCallback((fieldName: string, value: unknown) => {
    setFormData(prev => ({
      ...prev,
      [fieldName]: value,
    }));

    // Update field state
    setFieldStates(prev => ({
      ...prev,
      [fieldName]: {
        ...prev[fieldName],
        value,
        isDirty: true,
        isTouched: true,
        lastUpdated: new Date().toISOString(),
      },
    }));

    // Update form state
    setFormState(prev => ({
      ...prev,
      data: { ...prev.data, [fieldName]: value },
      isDirty: true,
      lastUpdated: new Date().toISOString(),
    }));

    // Sync to server
    debouncedSync(fieldName, value);

    // Validate field
    debouncedValidation(fieldName);
  }, [debouncedSync, debouncedValidation]);

  // Set form data
  const setFormDataCallback = useCallback((data: Record<string, unknown>) => {
    setFormData(data);
    lastSyncDataRef.current = data;

    // Update form state
    setFormState(prev => ({
      ...prev,
      data,
      isDirty: true,
      lastUpdated: new Date().toISOString(),
    }));

    // Validate entire form
    debouncedValidation();
  }, [debouncedValidation]);

  // Reset form
  const resetForm = useCallback(() => {
    setFormData(initialData);
    setErrors({});
    setWarnings({});
    lastSyncDataRef.current = initialData;

    setFormState(prev => ({
      ...prev,
      data: initialData,
      errors: {},
      warnings: {},
      isValid: true,
      isDirty: false,
      lastUpdated: new Date().toISOString(),
    }));

    // Reset field states
    const newFieldStates: Record<string, FormFieldState> = {};
    Object.keys(schema.properties).forEach(fieldName => {
      newFieldStates[fieldName] = {
        name: fieldName,
        value: initialData[fieldName],
        isValid: true,
        isDirty: false,
        isTouched: false,
        isValidating: false,
        lastUpdated: new Date().toISOString(),
      };
    });
    setFieldStates(newFieldStates);
  }, [initialData, schema]);

  // Validate field
  const validateField = useCallback(async (fieldName: string): Promise<FormValidationResult> => {
    return new Promise((resolve) => {
      const service = serviceRef.current;
      if (!service) {
        const result: FormValidationResult = {
          formId,
          messageId,
          isValid: true,
          errors: {},
          fieldName,
          timestamp: new Date().toISOString(),
        };
        resolve(result);
        return;
      }

      // Set field as validating
      setFieldStates(prev => ({
        ...prev,
        [fieldName]: {
          ...prev[fieldName],
          isValidating: true,
        },
      }));

      // Listen for validation result
      const unsubscribe = service.on('form_validation_result', (data: FormValidationResultData) => {
        if (data.formId === formId && data.messageId === messageId && data.fieldName === fieldName) {
          unsubscribe();
          
          // Update field state
          setFieldStates(prev => ({
            ...prev,
            [fieldName]: {
              ...prev[fieldName],
              isValidating: false,
              isValid: data.isValid,
              error: data.errors[fieldName],
            },
          }));

          const result: FormValidationResult = {
            formId: data.formId,
            messageId: data.messageId,
            isValid: data.isValid,
            errors: data.errors,
            fieldName: data.fieldName,
            timestamp: new Date().toISOString(),
          };
          
          resolve(result);
        }
      });

      // Emit validation request
      service.socket?.emit('form_validate', {
        formId,
        messageId,
        formData,
        fieldName,
        roomId,
      });
    });
  }, [formId, messageId, formData, roomId]);

  // Validate form
  const validateForm = useCallback(async (): Promise<FormValidationResult> => {
    return new Promise((resolve) => {
      const service = serviceRef.current;
      if (!service) {
        const result: FormValidationResult = {
          formId,
          messageId,
          isValid: true,
          errors: {},
          timestamp: new Date().toISOString(),
        };
        resolve(result);
        return;
      }

      // Listen for validation result
      const unsubscribe = service.on('form_validation_result', (data: FormValidationResultData) => {
        if (data.formId === formId && data.messageId === messageId && !data.fieldName) {
          unsubscribe();
          
          const result: FormValidationResult = {
            formId: data.formId,
            messageId: data.messageId,
            isValid: data.isValid,
            errors: data.errors,
            timestamp: new Date().toISOString(),
          };
          
          resolve(result);
        }
      });

      // Emit validation request
      service.socket?.emit('form_validate', {
        formId,
        messageId,
        formData,
        roomId,
      });
    });
  }, [formId, messageId, formData, roomId]);

  // Submit form
  const submitForm = useCallback(async (): Promise<FormSubmissionResult> => {
    setIsSubmitting(true);
    setFormState(prev => ({ ...prev, isSubmitting: true }));

    return new Promise((resolve) => {
      const service = serviceRef.current;
      
      if (!service) {
        const result: FormSubmissionResult = {
          formId,
          messageId,
          success: false,
          error: 'WebSocket service not available',
          timestamp: new Date().toISOString(),
        };
        setIsSubmitting(false);
        setFormState(prev => ({ ...prev, isSubmitting: false }));
        resolve(result);
        return;
      }

      // Listen for submission result
      const unsubscribe = service.on('form_submit_result', (data: FormSubmitResultData) => {
        if (data.formId === formId && data.messageId === messageId) {
          unsubscribe();
          
          const result: FormSubmissionResult = {
            formId: data.formId,
            messageId: data.messageId,
            success: data.success,
            data: data.data,
            error: data.error,
            validationErrors: data.validationErrors,
            timestamp: new Date().toISOString(),
          };
          
          setIsSubmitting(false);
          setFormState(prev => ({ ...prev, isSubmitting: false }));
          resolve(result);
        }
      });

      // Use custom onSubmit if provided
      if (onSubmit) {
        onSubmit(formData)
          .then((result) => {
            const submissionResult: FormSubmissionResult = {
              formId,
              messageId,
              success: true,
              data: result,
              timestamp: new Date().toISOString(),
            };
            setIsSubmitting(false);
            setFormState(prev => ({ ...prev, isSubmitting: false }));
            resolve(submissionResult);
          })
          .catch((error) => {
            const submissionResult: FormSubmissionResult = {
              formId,
              messageId,
              success: false,
              error: error.message || 'Submission failed',
              timestamp: new Date().toISOString(),
            };
            setIsSubmitting(false);
            setFormState(prev => ({ ...prev, isSubmitting: false }));
            resolve(submissionResult);
          });
      } else {
        // Use WebSocket submission
        service.submitForm(formId, messageId, formData, roomId);
      }
    });
  }, [formId, messageId, formData, roomId, onSubmit]);

  // Computed values
  const isValid = Object.keys(errors).length === 0;
  const isDirty = formState.isDirty;
  const isConnected = serviceRef.current?.isConnected() ?? false;

  // Cleanup
  useEffect(() => {
    return () => {
      if (syncTimeoutRef.current) {
        clearTimeout(syncTimeoutRef.current);
      }
      if (validationTimeoutRef.current) {
        clearTimeout(validationTimeoutRef.current);
      }
    };
  }, []);

  return {
    // Form state
    formData,
    formState,
    fieldStates,
    
    // Form operations
    updateField,
    setFormData: setFormDataCallback,
    resetForm,
    
    // Validation
    validateField,
    validateForm,
    
    // Submission
    submitForm,
    
    // Status
    isValid,
    isDirty,
    isSubmitting,
    isConnected,
    
    // Errors
    errors,
    warnings,
  };
}

export default useFormSync;
