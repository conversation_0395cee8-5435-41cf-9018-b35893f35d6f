/**
 * Message Actions Component
 * Action buttons for chat messages (reply, edit, delete, react)
 */

import React, { useState } from 'react';
import { Icon, Button } from '@/shared/components/common';

// Props interface
export interface MessageActionsProps {
  messageId: string;
  isCurrentUser?: boolean;
  onReply?: (messageId: string) => void;
  onEdit?: (messageId: string) => void;
  onDelete?: (messageId: string) => void;
  onReact?: (messageId: string, emoji: string) => void;
  className?: string;
}

// Common emoji reactions
const COMMON_REACTIONS = ['👍', '❤️', '😂', '😮', '😢', '😡'];

/**
 * Message Actions Component
 */
const MessageActions: React.FC<MessageActionsProps> = ({
  messageId,
  isCurrentUser = false,
  onReply,
  onEdit,
  onDelete,
  onReact,
  className = '',
}) => {
  const [showReactions, setShowReactions] = useState(false);

  // Handle reply
  const handleReply = () => {
    if (onReply) {
      onReply(messageId);
    }
  };

  // Handle edit
  const handleEdit = () => {
    if (onEdit) {
      onEdit(messageId);
    }
  };

  // Handle delete
  const handleDelete = () => {
    if (onDelete) {
      onDelete(messageId);
    }
  };

  // Handle reaction
  const handleReaction = (emoji: string) => {
    if (onReact) {
      onReact(messageId, emoji);
    }
    setShowReactions(false);
  };

  return (
    <div className={`flex items-center gap-1 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-1 ${className}`}>
      {/* Reply button */}
      {onReply && (
        <Button
          variant="ghost"
          size="sm"
          onClick={handleReply}
          className="h-8 w-8 p-0"
          title="Reply"
        >
          <Icon name="corner-down-left" size="sm" />
        </Button>
      )}

      {/* React button */}
      {onReact && (
        <div className="relative">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowReactions(!showReactions)}
            className="h-8 w-8 p-0"
            title="Add reaction"
          >
            <Icon name="smile" size="sm" />
          </Button>

          {/* Reactions popup */}
          {showReactions && (
            <div className="absolute bottom-full mb-2 left-0 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-2 flex gap-1 z-50">
              {COMMON_REACTIONS.map((emoji) => (
                <button
                  key={emoji}
                  onClick={() => handleReaction(emoji)}
                  className="w-8 h-8 flex items-center justify-center rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  title={`React with ${emoji}`}
                >
                  <span className="text-lg">{emoji}</span>
                </button>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Edit button (only for current user) */}
      {onEdit && isCurrentUser && (
        <Button
          variant="ghost"
          size="sm"
          onClick={handleEdit}
          className="h-8 w-8 p-0"
          title="Edit"
        >
          <Icon name="edit" size="sm" />
        </Button>
      )}

      {/* Delete button (only for current user) */}
      {onDelete && isCurrentUser && (
        <Button
          variant="ghost"
          size="sm"
          onClick={handleDelete}
          className="h-8 w-8 p-0 text-red-500 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20"
          title="Delete"
        >
          <Icon name="trash" size="sm" />
        </Button>
      )}

      {/* Click outside to close reactions */}
      {showReactions && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setShowReactions(false)}
        />
      )}
    </div>
  );
};

export default MessageActions;
