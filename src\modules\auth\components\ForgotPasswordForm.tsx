import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { z } from 'zod';

import { Input, Button } from '@/shared/components/common';
import { zodResolver } from '@hookform/resolvers/zod';

// Validation schema
const forgotPasswordSchema = z.object({
  email: z.string().min(1, 'Email is required').email('Invalid email format'),
});

type ForgotPasswordFormValues = z.infer<typeof forgotPasswordSchema>;

const ForgotPasswordForm = () => {
  const { t } = useTranslation(['auth', 'common']);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const form = useForm<ForgotPasswordFormValues>({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: {
      email: '',
    },
  });

  const handleSubmit = async () => {
    setIsSubmitting(true);
    setError(null);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Show success message
      setSuccess(true);
      form.reset();
    } catch {
      setError(t('auth:forgotPassword.error', 'Failed to send reset link. Please try again.'));
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="w-full max-w-md mx-auto bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
      <div className="p-6">
        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            {t('auth:forgotPassword.title', 'Forgot Password')}
          </h2>
          <p className="text-gray-600 dark:text-gray-300 mt-2">
            {t(
              'auth:forgotPassword.description',
              'Enter your email address and we will send you a link to reset your password.'
            )}
          </p>
        </div>

        {success && (
          <div className="mb-6 p-4 bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-800/50 rounded-md text-green-800 dark:text-green-300">
            <h3 className="font-medium">
              {t('auth:forgotPassword.success.title', 'Check your email')}
            </h3>
            <p className="mt-1 text-sm">
              {t(
                'auth:forgotPassword.success.description',
                'We have sent a password reset link to your email address.'
              )}
            </p>
          </div>
        )}

        {error && (
          <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800/50 rounded-md text-red-800 dark:text-red-300">
            <h3 className="font-medium">{t('common:error', 'Error')}</h3>
            <p className="mt-1 text-sm">{error}</p>
          </div>
        )}

        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
          <div className="space-y-2">
            <label
              htmlFor="email"
              className="block text-sm font-medium text-gray-700 dark:text-gray-300"
            >
              {t('auth:fields.email', 'Email')}
            </label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              {...form.register('email')}
              className="w-full"
            />
            {form.formState.errors.email && (
              <p className="text-sm text-red-600 dark:text-red-400">
                {form.formState.errors.email.message}
              </p>
            )}
          </div>

          <Button type="submit" className="w-full" isLoading={isSubmitting}>
            {t('auth:forgotPassword.submit', 'Send Reset Link')}
          </Button>
        </form>

        <div className="mt-6 text-center">
          <Link to="/auth/login" className="text-sm text-primary hover:underline">
            {t('auth:forgotPassword.backToLogin', 'Back to Login')}
          </Link>
        </div>
      </div>
    </div>
  );
};

export default ForgotPasswordForm;
