import React from 'react';
import { useTranslation } from 'react-i18next';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Legend, Tooltip } from 'recharts';

interface CompletionRateChartProps {
  rate: number; // Completion rate as a percentage (0-100)
}

/**
 * Chart component to display completion rate as a gauge
 */
const CompletionRateChart: React.FC<CompletionRateChartProps> = ({ rate }) => {
  const { t } = useTranslation(['todolist']);

  // Ensure rate is between 0 and 100
  const safeRate = Math.max(0, Math.min(100, rate));

  // Prepare data for the chart
  const chartData = [
    {
      name: t('todolist:dashboard.charts.completed', 'Completed'),
      value: safeRate,
      color: '#22c55e',
    },
    {
      name: t('todolist:dashboard.charts.remaining', 'Remaining'),
      value: 100 - safeRate,
      color: '#e5e7eb',
    },
  ];

  return (
    <div className="relative h-full w-full">
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <Pie
            data={chartData}
            cx="50%"
            cy="50%"
            startAngle={180}
            endAngle={0}
            innerRadius={60}
            outerRadius={80}
            paddingAngle={0}
            dataKey="value"
          >
            {chartData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Pie>
          <Tooltip
            formatter={(value: number) => [
              `${value.toFixed(1)}%`,
              value === safeRate
                ? t('todolist:dashboard.charts.completionRate', 'Completion Rate')
                : t('todolist:dashboard.charts.remaining', 'Remaining'),
            ]}
          />
          <Legend />
        </PieChart>
      </ResponsiveContainer>
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center">
        <div className="text-3xl font-bold">{safeRate.toFixed(1)}%</div>
        <div className="text-sm text-gray-500">
          {t('todolist:dashboard.charts.completionRate', 'Completion Rate')}
        </div>
      </div>
    </div>
  );
};

export default CompletionRateChart;
