import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

import { ProjectService } from '../services/project.service';
import {
  ProjectQueryDto,
  CreateProjectDto,
  UpdateProjectDto,
  CreateProjectMemberDto,
  UpdateProjectMemberDto,
} from '../types/project.types';

// Key cho React Query
const PROJECTS_QUERY_KEY = 'projects';
const PROJECT_MEMBERS_QUERY_KEY = 'project-members';

/**
 * Hook để lấy danh sách dự án
 * @param params Tham số truy vấn
 * @returns Query result với danh sách dự án
 */
export const useProjects = (params?: ProjectQueryDto) => {
  return useQuery({
    queryKey: [PROJECTS_QUERY_KEY, params],
    queryFn: () => ProjectService.getProjects(params),
    select: data => data.result,
  });
};

/**
 * Hook để lấy chi tiết dự án
 * @param id ID dự án
 * @returns Query result với chi tiết dự án
 */
export const useProject = (id: number) => {
  return useQuery({
    queryKey: [PROJECTS_QUERY_KEY, id],
    queryFn: () => ProjectService.getProject(id),
    select: data => data.result,
    enabled: !!id,
  });
};

/**
 * Hook để tạo dự án mới
 * @returns Mutation result cho việc tạo dự án
 */
export const useCreateProject = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateProjectDto) => ProjectService.createProject(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [PROJECTS_QUERY_KEY] });
    },
  });
};

/**
 * Hook để cập nhật dự án
 * @returns Mutation result cho việc cập nhật dự án
 */
export const useUpdateProject = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateProjectDto }) =>
      ProjectService.updateProject(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [PROJECTS_QUERY_KEY] });
      queryClient.invalidateQueries({ queryKey: [PROJECTS_QUERY_KEY, variables.id] });
    },
  });
};

/**
 * Hook để xóa dự án
 * @returns Mutation result cho việc xóa dự án
 */
export const useDeleteProject = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => ProjectService.deleteProject(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [PROJECTS_QUERY_KEY] });
    },
  });
};

/**
 * Hook để lấy danh sách thành viên dự án
 * @param projectId ID dự án
 * @returns Query result với danh sách thành viên
 */
export const useProjectMembers = (projectId: number) => {
  return useQuery({
    queryKey: [PROJECT_MEMBERS_QUERY_KEY, projectId],
    queryFn: () => ProjectService.getProjectMembers(projectId),
    select: data => data.result,
    enabled: !!projectId,
  });
};

/**
 * Hook để thêm thành viên vào dự án
 * @returns Mutation result cho việc thêm thành viên
 */
export const useAddProjectMember = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ projectId, data }: { projectId: number; data: CreateProjectMemberDto }) =>
      ProjectService.addProjectMember(projectId, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: [PROJECT_MEMBERS_QUERY_KEY, variables.projectId],
      });
    },
  });
};

/**
 * Hook để cập nhật thành viên dự án
 * @returns Mutation result cho việc cập nhật thành viên
 */
export const useUpdateProjectMember = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      projectId,
      memberId,
      data,
    }: {
      projectId: number;
      memberId: number;
      data: UpdateProjectMemberDto;
    }) => ProjectService.updateProjectMember(projectId, memberId, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: [PROJECT_MEMBERS_QUERY_KEY, variables.projectId],
      });
    },
  });
};

/**
 * Hook để xóa thành viên khỏi dự án
 * @returns Mutation result cho việc xóa thành viên
 */
export const useRemoveProjectMember = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ projectId, memberId }: { projectId: number; memberId: number }) =>
      ProjectService.removeProjectMember(projectId, memberId),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: [PROJECT_MEMBERS_QUERY_KEY, variables.projectId],
      });
    },
  });
};
