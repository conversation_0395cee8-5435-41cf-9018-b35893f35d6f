import React from 'react';

import { Card } from '@/shared/components/common';

import { PermissionGroupDto } from '../types/permission.types';

/**
 * Component hiển thị thông tin nhóm quyền
 * @param group Đ<PERSON>i tượng nhóm quyền
 * @param className Classes tùy chỉnh bổ sung (tù<PERSON> chọn)
 */
interface PermissionGroupCardProps {
  group: PermissionGroupDto;
  className?: string;
}

export const PermissionGroupCard: React.FC<PermissionGroupCardProps> = ({ group, className }) => {
  // Trích xuất module từ nhóm quyền hoặc từ key của group (nếu không có module)
  const groupModule = group.module || group.key.split('.')[0];

  return (
    <Card className={`shadow-md overflow-hidden ${className || ''}`}>
      {/* Header của card */}
      <div className="p-4 border-b bg-primary/5 dark:bg-primary/10">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold text-primary">{group.name}</h3>
          <span className="text-xs font-medium px-2 py-1 bg-primary/10 rounded-full">
            {groupModule}
          </span>
        </div>
      </div>

      {/* Nội dung card */}
      <div className="p-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {group.permissions.map(permission => {
            // Trích xuất module từ quyền hoặc từ key của permission (nếu không có module)
            const permissionModule = permission.module || permission.key.split('.')[0];

            return (
              <div
                key={permission.key}
                className="p-3 rounded-md bg-card shadow-sm hover:shadow-md transition-all duration-200"
              >
                <div className="font-medium text-card-foreground mb-1">{permission.name}</div>
                <div className="text-xs font-medium text-primary/80 mb-1">{permissionModule}</div>
                <div className="text-sm text-muted-foreground">{permission.description}</div>
              </div>
            );
          })}
        </div>
      </div>
    </Card>
  );
};

export default PermissionGroupCard;
