import { Body, Controller, Get, Patch, Post, UseGuards } from '@nestjs/common';
import {
  ApiExtraModels,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { UserAuthService } from '../services/user-auth.service';
import { UserLoginDto } from '../dto/user-login.dto';
import { ApiResponseDto } from '@/common/response/api-response-dto';
import {
  UserLoginResponseDto,
  UserResponseDto,
} from '../dto/user-response.dto';
import {
  ChangePasswordDto,
  ChangePasswordResponseDto,
} from '../dto/change-password.dto';
import {
  ForgotPasswordDto,
  ForgotPasswordResponseDto,
  ResetPasswordDto,
  ResetPasswordResponseDto,
} from '../dto/reset-password.dto';
import {
  RegisterEmployeeDto,
  RegisterEmployeeResponseDto,
} from '../dto/register-employee.dto';
import { UpdateUserProfileDto } from '../dto/update-user-profile.dto';
import {
  AvatarUploadUrlResponseDto,
  UpdateUserAvatarDto,
} from '../dto/update-user-avatar.dto';
import { JwtUserGuard } from '../guards/jwt-user.guard';
import { CurrentUser } from '../decorators/current-user.decorator';
import { SWAGGER_API_TAG } from '@/common/swagger/swagger.tags';
import { JwtPayload } from '../guards/jwt.util';
import { AppException } from '@/common/exceptions/app.exception';
import { AUTH_ERROR_CODE } from '../errors/auth-error.code';

@ApiTags(SWAGGER_API_TAG.AUTHENTICATION)
@ApiExtraModels(
  ApiResponseDto,
  UserLoginResponseDto,
  UserResponseDto,
  ChangePasswordResponseDto,
  ForgotPasswordResponseDto,
  ResetPasswordResponseDto,
  RegisterEmployeeResponseDto,
  UpdateUserProfileDto,
  AvatarUploadUrlResponseDto,
  UpdateUserAvatarDto,
)
@Controller('auth/user')
export class UserAuthController {
  constructor(private readonly userAuthService: UserAuthService) {}

  @Post('login')
  @ApiOperation({ summary: 'Đăng nhập tài khoản người dùng' })
  @ApiResponse({
    status: 200,
    description: 'Đăng nhập thành công',
    schema: ApiResponseDto.getSchema(UserLoginResponseDto),
  })
  @ApiResponse({
    status: 401,
    description: 'Thông tin đăng nhập không chính xác',
  })
  async login(
    @Body() loginDto: UserLoginDto,
  ): Promise<ApiResponseDto<UserLoginResponseDto>> {
    return this.userAuthService.login(loginDto);
  }

  @Get('profile')
  @UseGuards(JwtUserGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Lấy thông tin người dùng hiện tại' })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin thành công',
    schema: ApiResponseDto.getSchema(UserLoginResponseDto),
  })
  @ApiResponse({ status: 401, description: 'Không có quyền truy cập' })
  async getProfile(
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<UserLoginResponseDto>> {
    return this.userAuthService.getUserProfile(user.id);
  }

  @Post('change-password')
  @UseGuards(JwtUserGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Đổi mật khẩu người dùng' })
  @ApiResponse({
    status: 200,
    description: 'Đổi mật khẩu thành công',
    schema: ApiResponseDto.getSchema(ChangePasswordResponseDto),
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu không hợp lệ hoặc mật khẩu hiện tại không chính xác',
  })
  @ApiResponse({ status: 401, description: 'Không có quyền truy cập' })
  async changePassword(
    @CurrentUser() user: JwtPayload,
    @Body() changePasswordDto: ChangePasswordDto,
  ): Promise<ApiResponseDto<ChangePasswordResponseDto>> {
    return this.userAuthService.changePassword(user.id, changePasswordDto);
  }

  @Post('forgot-password')
  @ApiOperation({ summary: 'Yêu cầu đặt lại mật khẩu' })
  @ApiResponse({
    status: 200,
    description: 'Gửi email đặt lại mật khẩu thành công',
    schema: ApiResponseDto.getSchema(ForgotPasswordResponseDto),
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ' })
  async forgotPassword(
    @Body() forgotPasswordDto: ForgotPasswordDto,
  ): Promise<ApiResponseDto<ForgotPasswordResponseDto>> {
    return this.userAuthService.forgotPassword(forgotPasswordDto);
  }

  @Post('reset-password')
  @ApiOperation({ summary: 'Đặt lại mật khẩu' })
  @ApiResponse({
    status: 200,
    description: 'Đặt lại mật khẩu thành công',
    schema: ApiResponseDto.getSchema(ResetPasswordResponseDto),
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu không hợp lệ hoặc token không hợp lệ',
  })
  async resetPassword(
    @Body() resetPasswordDto: ResetPasswordDto,
  ): Promise<ApiResponseDto<ResetPasswordResponseDto>> {
    return this.userAuthService.resetPassword(resetPasswordDto);
  }

  @Patch('profile')
  @UseGuards(JwtUserGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Cập nhật thông tin cá nhân' })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật thông tin thành công',
    schema: ApiResponseDto.getSchema(UserResponseDto),
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ' })
  @ApiResponse({ status: 401, description: 'Không có quyền truy cập' })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy thông tin người dùng',
  })
  async updateProfile(
    @CurrentUser() user: JwtPayload,
    @Body() updateProfileDto: UpdateUserProfileDto,
  ): Promise<ApiResponseDto<UserResponseDto>> {
    return this.userAuthService.updateUserProfile(user.id, updateProfileDto);
  }

  @Post('profile/avatar-upload-url')
  @UseGuards(JwtUserGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Tạo URL tạm thời để upload avatar' })
  @ApiResponse({
    status: 200,
    description: 'Tạo URL upload thành công',
    schema: ApiResponseDto.getSchema(AvatarUploadUrlResponseDto),
  })
  @ApiResponse({ status: 401, description: 'Không có quyền truy cập' })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy thông tin người dùng',
  })
  async createAvatarUploadUrl(
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<AvatarUploadUrlResponseDto>> {
    return this.userAuthService.createAvatarUploadUrl(user.id);
  }

  @Patch('profile/avatar')
  @UseGuards(JwtUserGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Cập nhật avatar' })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật avatar thành công',
    schema: ApiResponseDto.getSchema(UserResponseDto),
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ' })
  @ApiResponse({ status: 401, description: 'Không có quyền truy cập' })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy thông tin người dùng',
  })
  async updateAvatar(
    @CurrentUser() user: JwtPayload,
    @Body() updateAvatarDto: UpdateUserAvatarDto,
  ): Promise<ApiResponseDto<UserResponseDto>> {
    return this.userAuthService.updateUserAvatar(user.id, updateAvatarDto);
  }
}
