import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import {
  Button,
  CollapsibleCard,
  Form,
  FormItem,
  Input,
  Textarea,
} from '@/shared/components/common';
import { useFormErrors } from '@/shared/hooks/useFormErrors';

import { usePermissionGroups } from '../../hooks/usePermissions';
import {
  CreateRoleDto,
  PermissionGroupDto,
  RoleDto,
  UpdateRoleDto,
} from '../../types/permission.types';

interface RoleFormProps {
  /**
   * Vai trò để chỉnh sửa (undefined nếu tạo mới)
   */
  role?: RoleDto;

  /**
   * Callback khi submit form
   */
  onSubmit: (data: CreateRoleDto | UpdateRoleDto) => void;

  /**
   * Callback khi hủy form
   */
  onCancel: () => void;

  /**
   * Trạng thái loading khi submit
   */
  isSubmitting?: boolean;
}

interface FormData {
  name: string;
  description: string;
  permissions: string[];
}

/**
 * Component form tạo/cập nhật vai trò
 */
const RoleForm: React.FC<RoleFormProps> = ({ role, onSubmit, onCancel, isSubmitting = false }) => {
  const { t } = useTranslation(['hrm', 'common']);
  const { formRef, setFormErrors } = useFormErrors<FormData>();

  // State cho form data
  const [formData, setFormData] = useState<FormData>({
    name: role?.name || '',
    description: role?.description || '',
    permissions: role?.permissions || [],
  });

  // Lấy danh sách permissions
  const { data: permissionGroupsData, isLoading: isLoadingPermissions } = usePermissionGroups();

  // Update form data khi role thay đổi
  useEffect(() => {
    if (role) {
      setFormData({
        name: role.name,
        description: role.description || '',
        permissions: role.permissions || [],
      });
    }
  }, [role]);

  // Xử lý thay đổi input
  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // Xử lý chọn/bỏ chọn permission
  const handlePermissionToggle = (permissionKey: string) => {
    setFormData(prev => ({
      ...prev,
      permissions: prev.permissions.includes(permissionKey)
        ? prev.permissions.filter(p => p !== permissionKey)
        : [...prev.permissions, permissionKey],
    }));
  };

  // Xử lý chọn/bỏ chọn tất cả permissions trong một nhóm
  const handleGroupToggle = (group: PermissionGroupDto) => {
    const groupPermissions = group.permissions.map(p => p.key);
    const allSelected = groupPermissions.every(p => formData.permissions.includes(p));

    setFormData(prev => ({
      ...prev,
      permissions: allSelected
        ? prev.permissions.filter(p => !groupPermissions.includes(p))
        : [...new Set([...prev.permissions, ...groupPermissions])],
    }));
  };

  // Xử lý submit form
  const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    // Validation
    const errors: Partial<FormData> = {};
    if (!formData.name.trim()) {
      errors.name = t('hrm:permission.form.errors.nameRequired', 'Tên vai trò là bắt buộc');
    }

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    // Submit data
    const submitData = role
      ? ({
          name: formData.name.trim(),
          description: formData.description.trim() || undefined,
          permissions: formData.permissions,
        } as UpdateRoleDto)
      : ({
          name: formData.name.trim(),
          description: formData.description.trim() || undefined,
          permissions: formData.permissions,
        } as CreateRoleDto);

    onSubmit(submitData);
  };

  return (
    <div className="p-6 bg-background text-foreground">
      <h2 className="text-xl font-semibold mb-6 text-primary">
        {role
          ? t('hrm:permission.form.editTitle', 'Chỉnh sửa vai trò')
          : t('hrm:permission.form.createTitle', 'Tạo vai trò mới')}
      </h2>

      <Form ref={formRef} onSubmit={handleSubmit} className="space-y-6">
        {/* Thông tin cơ bản */}
        <div className="space-y-4">
          <FormItem label={t('hrm:permission.form.name', 'Tên vai trò')} name="name" required>
            <Input
              value={formData.name}
              onChange={e => handleInputChange('name', e.target.value)}
              placeholder={t('hrm:permission.form.namePlaceholder', 'Nhập tên vai trò')}
              disabled={isSubmitting}
            />
          </FormItem>

          <FormItem label={t('hrm:permission.form.description', 'Mô tả')} name="description">
            <Textarea
              value={formData.description}
              onChange={e => handleInputChange('description', e.target.value)}
              placeholder={t('hrm:permission.form.descriptionPlaceholder', 'Nhập mô tả vai trò')}
              rows={3}
              disabled={isSubmitting}
            />
          </FormItem>
        </div>

        {/* Phân quyền */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-foreground">
            {t('hrm:permission.form.permissions', 'Phân quyền')}
          </h3>

          {isLoadingPermissions ? (
            <div className="flex justify-center items-center h-20">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
            </div>
          ) : (
            <div className="space-y-3">
              {permissionGroupsData?.groups.map((group: PermissionGroupDto) => {
                const groupPermissions = group.permissions.map(p => p.key);
                const selectedCount = groupPermissions.filter(p =>
                  formData.permissions.includes(p)
                ).length;
                const allSelected = selectedCount === groupPermissions.length;
                const someSelected = selectedCount > 0 && selectedCount < groupPermissions.length;

                return (
                  <CollapsibleCard
                    key={group.key}
                    title={
                      <div className="flex items-center justify-between w-full">
                        <div className="flex items-center space-x-3">
                          <input
                            type="checkbox"
                            checked={allSelected}
                            ref={el => {
                              if (el) el.indeterminate = someSelected;
                            }}
                            onChange={() => handleGroupToggle(group)}
                            className="rounded border-gray-300 text-primary focus:ring-primary"
                            disabled={isSubmitting}
                          />
                          <span className="font-medium">{group.name}</span>
                          <span className="text-xs bg-primary/10 px-2 py-1 rounded-full">
                            {selectedCount}/{groupPermissions.length}
                          </span>
                        </div>
                      </div>
                    }
                    defaultOpen={selectedCount > 0}
                    className="border border-border"
                  >
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {group.permissions.map(permission => (
                        <label
                          key={permission.key}
                          className="flex items-start space-x-3 p-3 rounded-md hover:bg-muted/50 cursor-pointer"
                        >
                          <input
                            type="checkbox"
                            checked={formData.permissions.includes(permission.key)}
                            onChange={() => handlePermissionToggle(permission.key)}
                            className="mt-1 rounded border-gray-300 text-primary focus:ring-primary"
                            disabled={isSubmitting}
                          />
                          <div className="flex-1">
                            <div className="font-medium text-sm">{permission.name}</div>
                            {permission.description && (
                              <div className="text-xs text-muted-foreground mt-1">
                                {permission.description}
                              </div>
                            )}
                          </div>
                        </label>
                      ))}
                    </div>
                  </CollapsibleCard>
                );
              })}
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-3 pt-4 border-t border-border">
          <Button type="button" variant="outline" onClick={onCancel} disabled={isSubmitting}>
            {t('common:cancel', 'Hủy')}
          </Button>
          <Button type="submit" variant="primary" disabled={isSubmitting} loading={isSubmitting}>
            {role ? t('common:update', 'Cập nhật') : t('common:create', 'Tạo mới')}
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default RoleForm;
