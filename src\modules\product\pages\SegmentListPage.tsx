import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import {
  Badge,
  Button,
  Card,
  Icon,
  Input,
  ResponsiveGrid,
  Select,
  Table,
  Typography,
} from '@/shared/components/common';
import ActionMenu from '@/shared/components/common/ActionMenu';
import SlideInForm from '@/shared/components/common/SlideInForm';
import { TableColumn } from '@/shared/components/common/Table/types';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { formatDate } from '@/shared/utils/date';

import SegmentForm from '../components/forms/SegmentForm';
import { SegmentDto, SegmentQueryDto, SegmentStatus, SegmentType } from '../types/segment.types';

/**
 * Trang danh sách phân đoạn sản phẩm
 */
const SegmentListPage: React.FC = () => {
  const { t } = useTranslation(['product']);

  // Mock data cho danh sách phân đoạn
  const [segments, setSegments] = useState<SegmentDto[]>([
    {
      id: '1',
      name: 'Sản phẩm cao cấp',
      description: 'Các sản phẩm có giá trên 20 triệu đồng',
      type: SegmentType.PRICE_RANGE,
      status: SegmentStatus.ACTIVE,
      conditions: {
        operator: 'AND',
        conditions: [
          {
            field: 'price',
            type: 'greater_than',
            value: 20000000,
          },
        ],
      },
      productCount: 12,
      createdAt: '2023-06-01T00:00:00Z',
      updatedAt: '2023-06-15T00:00:00Z',
    },
    {
      id: '2',
      name: 'Laptop',
      description: 'Tất cả sản phẩm thuộc danh mục laptop',
      type: SegmentType.CATEGORY,
      status: SegmentStatus.ACTIVE,
      conditions: {
        operator: 'AND',
        conditions: [
          {
            field: 'categoryId',
            type: 'equals',
            value: '1',
          },
        ],
      },
      productCount: 8,
      createdAt: '2023-06-02T00:00:00Z',
      updatedAt: '2023-06-16T00:00:00Z',
    },
    {
      id: '3',
      name: 'Sản phẩm khuyến mãi',
      description: 'Các sản phẩm đang có khuyến mãi',
      type: SegmentType.CUSTOM,
      status: SegmentStatus.DRAFT,
      conditions: {
        operator: 'AND',
        conditions: [
          {
            field: 'discount',
            type: 'greater_than',
            value: 0,
          },
        ],
      },
      productCount: 5,
      createdAt: '2023-06-03T00:00:00Z',
      updatedAt: '2023-06-17T00:00:00Z',
    },
    {
      id: '4',
      name: 'Dịch vụ',
      description: 'Tất cả sản phẩm thuộc loại dịch vụ',
      type: SegmentType.CUSTOM,
      status: SegmentStatus.INACTIVE,
      conditions: {
        operator: 'AND',
        conditions: [
          {
            field: 'type',
            type: 'equals',
            value: 'service',
          },
        ],
      },
      productCount: 3,
      createdAt: '2023-06-04T00:00:00Z',
      updatedAt: '2023-06-18T00:00:00Z',
    },
  ]);

  // State cho filter
  const [filters, setFilters] = useState<Partial<SegmentQueryDto>>({});

  // State cho form
  const { isVisible, showForm, hideForm } = useSlideForm();
  const [selectedSegment, setSelectedSegment] = useState<SegmentDto | undefined>(undefined);

  // Xử lý filter
  const handleFilter = (newFilters: Partial<SegmentQueryDto>) => {
    setFilters(newFilters);
    // Trong thực tế, sẽ gọi API với filter mới
    console.log('Apply filters:', newFilters);
  };

  // Xử lý thêm/sửa phân đoạn
  const handleSubmit = (values: any) => {
    if (selectedSegment) {
      // Cập nhật phân đoạn
      const updatedSegments = segments.map(s =>
        s.id === selectedSegment.id ? { ...s, ...values } : s
      );
      setSegments(updatedSegments);
    } else {
      // Thêm phân đoạn mới
      const newSegment: SegmentDto = {
        id: `${segments.length + 1}`,
        ...values,
        productCount: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      setSegments([...segments, newSegment]);
    }
    hideForm();
    setSelectedSegment(undefined);
  };

  // Xử lý xóa phân đoạn
  const handleDelete = (id: string) => {
    const updatedSegments = segments.filter(s => s.id !== id);
    setSegments(updatedSegments);
  };

  // Xử lý chỉnh sửa phân đoạn
  const handleEdit = (segment: SegmentDto) => {
    setSelectedSegment(segment);
    showForm();
  };

  // Xử lý thêm phân đoạn mới
  const handleAddSegment = () => {
    setSelectedSegment(undefined);
    showForm();
  };

  // Định nghĩa cột cho bảng
  const columns = useMemo<TableColumn<SegmentDto>[]>(
    () => [
      {
        id: 'name',
        header: t('product:table.name'),
        cell: ({ row }) => (
          <div>
            <Typography variant="body1" className="font-medium">
              {row.name}
            </Typography>
            <Typography variant="caption" className="text-muted">
              {row.description}
            </Typography>
          </div>
        ),
      },
      {
        id: 'type',
        header: t('product:segment.form.typeLabel'),
        cell: ({ row }) => t(`product:common.${row.type}`),
      },
      {
        id: 'status',
        header: t('product:table.status'),
        cell: ({ row }) => {
          let variant: 'success' | 'warning' | 'danger' | 'info' = 'info';
          switch (row.status) {
            case SegmentStatus.ACTIVE:
              variant = 'success';
              break;
            case SegmentStatus.INACTIVE:
              variant = 'danger';
              break;
            case SegmentStatus.DRAFT:
              variant = 'warning';
              break;
          }
          return <Badge variant={variant}>{t(`product:common.${row.status}`)}</Badge>;
        },
      },
      {
        id: 'productCount',
        header: t('product:segment.form.matchingProducts', { count: 0 }),
        cell: ({ row }) => row.productCount,
      },
      {
        id: 'createdAt',
        header: t('product:table.createdAt'),
        cell: ({ row }) => formatDate(row.createdAt),
      },
      {
        id: 'actions',
        header: t('product:common.actions'),
        cell: ({ row }) => (
          <ActionMenu
            items={[
              {
                key: 'edit',
                label: t('product:common.edit'),
                icon: 'edit',
                onClick: () => handleEdit(row),
              },
              {
                key: 'delete',
                label: t('product:common.delete'),
                icon: 'trash-2',
                onClick: () => handleDelete(row.id),
                danger: true,
              },
            ]}
            showAllInMenu
          />
        ),
        width: '80px',
        align: 'center',
      },
    ],
    [t]
  );

  // Lọc phân đoạn theo filter
  const filteredSegments = useMemo(() => {
    return segments.filter(segment => {
      // Lọc theo trạng thái
      if (filters.status && segment.status !== filters.status) {
        return false;
      }

      // Lọc theo loại
      if (filters.type && segment.type !== filters.type) {
        return false;
      }

      // Lọc theo từ khóa tìm kiếm
      if (
        filters.search &&
        !segment.name.toLowerCase().includes(filters.search.toLowerCase()) &&
        !segment.description?.toLowerCase().includes(filters.search.toLowerCase())
      ) {
        return false;
      }

      return true;
    });
  }, [segments, filters]);

  return (
    <div>
      <MenuIconBar
        title={t('product:segment.list.title')}
        primaryAction={{
          icon: 'plus',
          label: t('product:segment.list.addSegment'),
          onClick: handleAddSegment,
        }}
      />

      <Card className="mb-4">
        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row sm:items-center gap-2">
            <div className="flex-grow">
              <Input
                placeholder={t('product:segment.list.searchPlaceholder')}
                value={filters.search || ''}
                onChange={e => handleFilter({ ...filters, search: e.target.value })}
                leftIcon={<Icon name="search" size="sm" />}
                fullWidth
              />
            </div>
            <div className="flex space-x-2">
              <Button
                variant="primary"
                onClick={() => handleFilter(filters)}
                leftIcon={<Icon name="filter" size="sm" />}
              >
                {t('product:common.filter')}
              </Button>
              <Button variant="outline" onClick={() => handleFilter({})}>
                {t('product:common.cancel')}
              </Button>
            </div>
          </div>

          <ResponsiveGrid maxColumns={{ xs: 1, sm: 2 }} gap={4}>
            <div>
              <label className="block text-sm font-medium mb-1">
                {t('product:segment.list.filterByStatus')}
              </label>
              <Select
                options={[
                  { value: '', label: t('product:common.all') },
                  { value: SegmentStatus.ACTIVE, label: t('product:common.active') },
                  { value: SegmentStatus.INACTIVE, label: t('product:common.inactive') },
                  { value: SegmentStatus.DRAFT, label: t('product:common.draft') },
                ]}
                value={filters.status || ''}
                onChange={value =>
                  handleFilter({
                    ...filters,
                    status: value ? (value as SegmentStatus) : undefined,
                  })
                }
                fullWidth
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">
                {t('product:segment.list.filterByType')}
              </label>
              <Select
                options={[
                  { value: '', label: t('product:common.all') },
                  { value: SegmentType.CATEGORY, label: t('product:common.category') },
                  { value: SegmentType.TAG, label: t('product:common.tag') },
                  { value: SegmentType.PRICE_RANGE, label: t('product:common.priceRange') },
                  { value: SegmentType.CUSTOM, label: t('product:common.custom') },
                ]}
                value={filters.type || ''}
                onChange={value =>
                  handleFilter({
                    ...filters,
                    type: value ? (value as SegmentType) : undefined,
                  })
                }
                fullWidth
              />
            </div>
          </ResponsiveGrid>
        </div>
      </Card>

      <Card>
        <div className="mb-4 flex justify-between items-center">
          <Typography variant="body2" className="text-muted">
            {t('product:segment.list.showingSegments', {
              count: filteredSegments.length,
              total: segments.length,
            })}
          </Typography>
          <Button
            variant="primary"
            leftIcon={<Icon name="plus" size="sm" />}
            onClick={handleAddSegment}
          >
            {t('product:segment.list.addSegment')}
          </Button>
        </div>

        <Table
          columns={columns}
          data={filteredSegments}
          emptyMessage={t('product:segment.list.noSegments')}
        />
      </Card>

      <SlideInForm isVisible={isVisible}>
        <SegmentForm segment={selectedSegment} onSubmit={handleSubmit} onCancel={hideForm} />
      </SlideInForm>
    </div>
  );
};

export default SegmentListPage;
