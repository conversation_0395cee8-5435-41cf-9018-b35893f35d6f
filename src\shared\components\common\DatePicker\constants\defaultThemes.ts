import { CalendarTheme, CalendarColorScheme } from '../types/theme.types';

/**
 * Base spacing configuration
 */
const baseSpacing = {
  padding: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '0.75rem',
    lg: '1rem',
    xl: '1.5rem',
  },
  margin: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '0.75rem',
    lg: '1rem',
    xl: '1.5rem',
  },
  gap: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '0.75rem',
    lg: '1rem',
    xl: '1.5rem',
  },
  cell: {
    size: '2.5rem',
    padding: '0.5rem',
    margin: '0.125rem',
  },
  calendar: {
    padding: '1rem',
    headerHeight: '3rem',
    footerHeight: '2.5rem',
  },
};

/**
 * Base typography configuration
 */
const baseTypography = {
  fontFamily: {
    primary: 'Plus Jakarta Sans, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    secondary: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    mono: 'JetBrains Mono, "Fira Code", Consolas, monospace',
  },
  fontSize: {
    xs: '0.75rem',
    sm: '0.875rem',
    md: '1rem',
    lg: '1.125rem',
    xl: '1.25rem',
    '2xl': '1.5rem',
  },
  fontWeight: {
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
  },
  lineHeight: {
    tight: '1.25',
    normal: '1.5',
    relaxed: '1.75',
  },
  letterSpacing: {
    tight: '-0.025em',
    normal: '0',
    wide: '0.025em',
  },
};

/**
 * Base shadows configuration
 */
const baseShadows = {
  none: 'none',
  sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
  calendar: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  cell: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  dropdown: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  tooltip: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
};

/**
 * Base border radius configuration
 */
const baseBorderRadius = {
  none: '0',
  sm: '0.25rem',
  md: '0.375rem',
  lg: '0.5rem',
  xl: '0.75rem',
  full: '9999px',
  calendar: '0.75rem',
  cell: '9999px',
  button: '0.375rem',
};

/**
 * Base transitions configuration
 */
const baseTransitions = {
  duration: {
    fast: '150ms',
    normal: '200ms',
    slow: '300ms',
  },
  timing: {
    linear: 'linear',
    ease: 'ease',
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    easeInOut: 'ease-in-out',
    bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
  },
  default: 'all 200ms ease-in-out',
  colors: 'color 200ms ease-in-out, background-color 200ms ease-in-out, border-color 200ms ease-in-out',
  transform: 'transform 200ms ease-in-out',
  opacity: 'opacity 200ms ease-in-out',
};

/**
 * Base animations configuration
 */
const baseAnimations = {
  keyframes: {
    fadeIn: 'fadeIn 200ms ease-in-out',
    fadeOut: 'fadeOut 200ms ease-in-out',
    slideIn: 'slideIn 300ms ease-out',
    slideOut: 'slideOut 300ms ease-in',
    scaleIn: 'scaleIn 200ms ease-out',
    scaleOut: 'scaleOut 200ms ease-in',
    bounce: 'bounce 600ms ease-in-out',
    pulse: 'pulse 2s infinite',
  },
  monthTransition: {
    duration: '300ms',
    timing: 'ease-out',
  },
  cellHover: {
    duration: '150ms',
    timing: 'ease-out',
  },
  selection: {
    duration: '200ms',
    timing: 'ease-out',
  },
};

/**
 * Color schemes configuration
 */
const colorSchemes: Record<CalendarColorScheme, { primary: string; secondary: string; light: string; dark: string }> = {
  blue: {
    primary: '#3B82F6',
    secondary: '#60A5FA',
    light: '#DBEAFE',
    dark: '#1E40AF',
  },
  green: {
    primary: '#10B981',
    secondary: '#34D399',
    light: '#D1FAE5',
    dark: '#047857',
  },
  purple: {
    primary: '#8B5CF6',
    secondary: '#A78BFA',
    light: '#EDE9FE',
    dark: '#5B21B6',
  },
  red: {
    primary: '#EF4444',
    secondary: '#F87171',
    light: '#FEE2E2',
    dark: '#B91C1C',
  },
  orange: {
    primary: '#F97316',
    secondary: '#FB923C',
    light: '#FED7AA',
    dark: '#C2410C',
  },
  pink: {
    primary: '#EC4899',
    secondary: '#F472B6',
    light: '#FCE7F3',
    dark: '#BE185D',
  },
  indigo: {
    primary: '#6366F1',
    secondary: '#818CF8',
    light: '#E0E7FF',
    dark: '#3730A3',
  },
  teal: {
    primary: '#14B8A6',
    secondary: '#2DD4BF',
    light: '#CCFBF1',
    dark: '#0F766E',
  },
};

/**
 * Light theme configuration
 */
export const lightTheme: CalendarTheme = {
  name: 'light',
  colors: {
    background: '#FFFFFF',
    surface: '#F9FAFB',
    overlay: 'rgba(0, 0, 0, 0.5)',
    text: {
      primary: '#111827',
      secondary: '#6B7280',
      disabled: '#9CA3AF',
      inverse: '#FFFFFF',
    },
    border: {
      default: '#E5E7EB',
      focus: '#3B82F6',
      hover: '#D1D5DB',
    },
    primary: '#3B82F6',
    secondary: '#60A5FA',
    success: '#10B981',
    warning: '#F59E0B',
    error: '#EF4444',
    info: '#3B82F6',
    today: '#3B82F6',
    selected: '#3B82F6',
    hover: '#F3F4F6',
    disabled: '#F9FAFB',
    weekend: '#F3F4F6',
    otherMonth: '#9CA3AF',
    rangeStart: '#3B82F6',
    rangeEnd: '#3B82F6',
    rangeMiddle: '#DBEAFE',
    rangeHover: '#BFDBFE',
    event: {
      dot: '#3B82F6',
      badge: '#3B82F6',
      highlight: '#DBEAFE',
      priority: {
        low: '#10B981',
        medium: '#F59E0B',
        high: '#EF4444',
      },
    },
  },
  spacing: baseSpacing,
  typography: baseTypography,
  shadows: baseShadows,
  borderRadius: baseBorderRadius,
  transitions: baseTransitions,
  animations: baseAnimations,
};

/**
 * Dark theme configuration
 */
export const darkTheme: CalendarTheme = {
  name: 'dark',
  colors: {
    background: '#1F2937',
    surface: '#374151',
    overlay: 'rgba(0, 0, 0, 0.75)',
    text: {
      primary: '#F9FAFB',
      secondary: '#D1D5DB',
      disabled: '#6B7280',
      inverse: '#111827',
    },
    border: {
      default: '#4B5563',
      focus: '#60A5FA',
      hover: '#6B7280',
    },
    primary: '#60A5FA',
    secondary: '#93C5FD',
    success: '#34D399',
    warning: '#FBBF24',
    error: '#F87171',
    info: '#60A5FA',
    today: '#60A5FA',
    selected: '#60A5FA',
    hover: '#4B5563',
    disabled: '#374151',
    weekend: '#4B5563',
    otherMonth: '#6B7280',
    rangeStart: '#60A5FA',
    rangeEnd: '#60A5FA',
    rangeMiddle: '#1E3A8A',
    rangeHover: '#1E40AF',
    event: {
      dot: '#60A5FA',
      badge: '#60A5FA',
      highlight: '#1E3A8A',
      priority: {
        low: '#34D399',
        medium: '#FBBF24',
        high: '#F87171',
      },
    },
  },
  spacing: baseSpacing,
  typography: baseTypography,
  shadows: {
    ...baseShadows,
    calendar: '0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.1)',
    dropdown: '0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.1)',
  },
  borderRadius: baseBorderRadius,
  transitions: baseTransitions,
  animations: baseAnimations,
};

/**
 * Function to create theme with custom color scheme
 */
export const createThemeWithColorScheme = (
  baseTheme: CalendarTheme,
  colorScheme: CalendarColorScheme
): CalendarTheme => {
  const scheme = colorSchemes[colorScheme];
  
  return {
    ...baseTheme,
    colors: {
      ...baseTheme.colors,
      primary: scheme.primary,
      secondary: scheme.secondary,
      today: scheme.primary,
      selected: scheme.primary,
      rangeStart: scheme.primary,
      rangeEnd: scheme.primary,
      rangeMiddle: scheme.light,
      event: {
        ...baseTheme.colors.event,
        dot: scheme.primary,
        badge: scheme.primary,
        highlight: scheme.light,
      },
    },
  };
};

/**
 * Default themes export
 */
export const defaultThemes = {
  light: lightTheme,
  dark: darkTheme,
};

export { colorSchemes };
