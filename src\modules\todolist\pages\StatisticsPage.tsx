import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { useAuth } from '@/modules/auth';
import { Card, RangePicker, Select } from '@/shared/components/common';

import { useProjects } from '../hooks/useProjects';
import { useProjectPerformance, useUserPerformance } from '../hooks/useStatistics';

/**
 * Trang thống kê
 */
const StatisticsPage: React.FC = () => {
  const { t } = useTranslation(['common', 'todolist']);
  const { user } = useAuth();
  const [selectedProjectId, setSelectedProjectId] = useState<number | null>(null);
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null]);

  // L<PERSON>y danh sách dự án
  const { data: projectsData, isLoading: isLoadingProjects } = useProjects();

  // Chuyển đổi dateRange thành timestamp
  const startDate = dateRange[0] ? dateRange[0].getTime() : undefined;
  const endDate = dateRange[1] ? dateRange[1].getTime() : undefined;

  // Lấy hiệu suất dự án nếu đã chọn dự án
  const { data: projectPerformance, isLoading: isLoadingProjectPerformance } =
    useProjectPerformance(selectedProjectId || 0, startDate, endDate);

  // Lấy hiệu suất người dùng hiện tại
  const { data: userPerformance, isLoading: isLoadingUserPerformance } = useUserPerformance(
    typeof user?.id === 'string' ? parseInt(user.id, 10) : user?.id || 0,
    startDate,
    endDate,
    selectedProjectId || undefined
  );

  // Xử lý thay đổi dự án
  const handleProjectChange = (value: string | number | string[] | number[]) => {
    if (typeof value === 'string') {
      setSelectedProjectId(value ? parseInt(value, 10) : null);
    } else if (typeof value === 'number') {
      setSelectedProjectId(value);
    }
  };

  // Xử lý thay đổi khoảng thời gian
  const handleDateRangeChange = (range: [Date | null, Date | null]) => {
    setDateRange(range);
  };

  return (
    <div>
      <div className="mb-6 flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">{t('todolist:statistics.title', 'Thống kê')}</h1>
          <p className="text-gray-500 mt-1">
            {t('todolist:statistics.description', 'Xem báo cáo và thống kê công việc')}
          </p>
        </div>
        <a
          href="/todolist/statistics/enhanced"
          className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors flex items-center"
        >
          <span className="mr-2">
            {t('todolist:statistics.enhancedView', 'Xem biểu đồ nâng cao')}
          </span>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fillRule="evenodd"
              d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
              clipRule="evenodd"
            />
          </svg>
        </a>
      </div>

      <Card className="mb-6">
        <div className="flex flex-wrap gap-4">
          <div className="w-full md:w-64">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('todolist:statistics.filters.project', 'Dự án')}
            </label>
            <Select
              placeholder={t('todolist:statistics.filters.selectProject', 'Chọn dự án')}
              onChange={handleProjectChange}
              value={selectedProjectId?.toString() || ''}
              options={
                projectsData?.items.map(project => ({
                  value: project.id.toString(),
                  label: project.title,
                })) || []
              }
              loading={isLoadingProjects}
            />
          </div>
          <div className="w-full md:w-auto flex-grow">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('todolist:statistics.filters.dateRange', 'Khoảng thời gian')}
            </label>
            <RangePicker
              value={dateRange}
              onChange={handleDateRangeChange}
              placeholder={[
                t('todolist:statistics.filters.startDate', 'Ngày bắt đầu'),
                t('todolist:statistics.filters.endDate', 'Ngày kết thúc'),
              ]}
            />
          </div>
        </div>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <Card>
          <h2 className="text-lg font-semibold mb-4">
            {t('todolist:statistics.personalPerformance', 'Hiệu suất cá nhân')}
          </h2>
          {isLoadingUserPerformance ? (
            <div className="h-40 flex items-center justify-center">
              <p>{t('common:loading', 'Đang tải...')}</p>
            </div>
          ) : userPerformance ? (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <p className="text-sm text-gray-500">
                    {t('todolist:statistics.metrics.totalTasks', 'Tổng số công việc')}
                  </p>
                  <p className="text-2xl font-bold">{userPerformance.totalTasks}</p>
                </div>
                <div className="bg-green-50 p-4 rounded-lg">
                  <p className="text-sm text-gray-500">
                    {t('todolist:statistics.metrics.completedTasks', 'Đã hoàn thành')}
                  </p>
                  <p className="text-2xl font-bold">{userPerformance.completedTasks}</p>
                </div>
                <div className="bg-yellow-50 p-4 rounded-lg">
                  <p className="text-sm text-gray-500">
                    {t('todolist:statistics.metrics.averageScore', 'Điểm trung bình')}
                  </p>
                  <p className="text-2xl font-bold">{userPerformance.averageScore.toFixed(1)}</p>
                </div>
                <div className="bg-purple-50 p-4 rounded-lg">
                  <p className="text-sm text-gray-500">
                    {t('todolist:statistics.metrics.completionRate', 'Tỷ lệ hoàn thành')}
                  </p>
                  <p className="text-2xl font-bold">{userPerformance.completionRate.toFixed(1)}%</p>
                </div>
              </div>
            </div>
          ) : (
            <div className="h-40 flex items-center justify-center">
              <p>{t('todolist:statistics.noData', 'Không có dữ liệu')}</p>
            </div>
          )}
        </Card>

        {selectedProjectId && (
          <Card>
            <h2 className="text-lg font-semibold mb-4">
              {t('todolist:statistics.projectPerformance', 'Hiệu suất dự án')}
            </h2>
            {isLoadingProjectPerformance ? (
              <div className="h-40 flex items-center justify-center">
                <p>{t('common:loading', 'Đang tải...')}</p>
              </div>
            ) : projectPerformance ? (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <p className="text-sm text-gray-500">
                      {t('todolist:statistics.metrics.totalTasks', 'Tổng số công việc')}
                    </p>
                    <p className="text-2xl font-bold">{projectPerformance.totalTasks}</p>
                  </div>
                  <div className="bg-green-50 p-4 rounded-lg">
                    <p className="text-sm text-gray-500">
                      {t('todolist:statistics.metrics.completedTasks', 'Đã hoàn thành')}
                    </p>
                    <p className="text-2xl font-bold">{projectPerformance.completedTasks}</p>
                  </div>
                  <div className="bg-yellow-50 p-4 rounded-lg">
                    <p className="text-sm text-gray-500">
                      {t('todolist:statistics.metrics.averageScore', 'Điểm trung bình')}
                    </p>
                    <p className="text-2xl font-bold">
                      {projectPerformance.averageScore.toFixed(1)}
                    </p>
                  </div>
                  <div className="bg-purple-50 p-4 rounded-lg">
                    <p className="text-sm text-gray-500">
                      {t('todolist:statistics.metrics.completionRate', 'Tỷ lệ hoàn thành')}
                    </p>
                    <p className="text-2xl font-bold">
                      {projectPerformance.completionRate.toFixed(1)}%
                    </p>
                  </div>
                </div>
              </div>
            ) : (
              <div className="h-40 flex items-center justify-center">
                <p>{t('todolist:statistics.noData', 'Không có dữ liệu')}</p>
              </div>
            )}
          </Card>
        )}
      </div>

      {selectedProjectId && projectPerformance && (
        <Card>
          <h2 className="text-lg font-semibold mb-4">
            {t('todolist:statistics.teamPerformance', 'Hiệu suất nhóm')}
          </h2>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('todolist:statistics.team.member', 'Thành viên')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('todolist:statistics.team.completedTasks', 'Công việc hoàn thành')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('todolist:statistics.team.totalTasks', 'Tổng công việc')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('todolist:statistics.team.completionRate', 'Tỷ lệ hoàn thành')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('todolist:statistics.team.averageScore', 'Điểm trung bình')}
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {projectPerformance.members.map(member => (
                  <tr key={member.userId}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            {member.name || `User ${member.userId}`}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {member.completedTasks}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {member.totalTasks}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {member.completionRate.toFixed(1)}%
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {member.averageScore.toFixed(1)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </Card>
      )}
    </div>
  );
};

export default StatisticsPage;
