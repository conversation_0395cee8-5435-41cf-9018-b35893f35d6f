{"kanban": {"addCard": "Add card", "addColumn": "Add column", "deleteColumn": "Delete column", "editColumn": "Edit column", "emptyColumn": "No cards", "cardTitlePlaceholder": "Enter card title...", "columnTitlePlaceholder": "Enter column title...", "priority": {"low": "Low", "medium": "Medium", "high": "High", "urgent": "<PERSON><PERSON>"}, "status": {"todo": "To Do", "inProgress": "In Progress", "review": "Review", "done": "Done"}, "variants": {"title": "Layout Variant", "default": "<PERSON><PERSON><PERSON>", "compact": "Compact", "detailed": "Detailed", "horizontal": "Horizontal", "vertical": "Vertical", "swimlane": "<PERSON><PERSON><PERSON><PERSON>", "timeline": "Timeline", "grid": "Grid", "masonry": "Masonry", "defaultDescription": "Default layout with horizontal columns", "compactDescription": "Compact layout with less information", "detailedDescription": "Shows more detailed information", "horizontalDescription": "Horizontal layout (same as DEFAULT)", "verticalDescription": "Vertical layout with columns stacked vertically", "swimlaneDescription": "Swimlane layout with colored headers", "timelineDescription": "Timeline layout with vertical line", "gridDescription": "Grid layout with columns in a responsive grid", "masonryDescription": "Masonry layout with columns of varying heights"}, "functionality": {"title": "Functionality", "interactive": "Interactive", "simple": "Simple", "advanced": "Advanced", "readonly": "Read-only", "collaborative": "Collaborative", "filtered": "Filtered", "searchable": "Searchable", "sortable": "Sortable", "interactiveDescription": "Full interactive features", "simpleDescription": "Only basic features", "advancedDescription": "Additional advanced features", "readonlyDescription": "View only, no changes allowed", "collaborativeDescription": "Real-time collaboration features", "filteredDescription": "Filtering capabilities", "searchableDescription": "Search functionality", "sortableDescription": "Sorting capabilities"}, "theme": {"title": "Theme", "default": "<PERSON><PERSON><PERSON>", "colorful": "Colorful", "minimal": "Minimal", "bordered": "Bordered", "dark": "Dark", "light": "Light", "gradient": "Gradient", "material": "Material", "glassmorphism": "Glassmorphism", "neumorphism": "Neumorphism", "defaultDescription": "Default theme", "colorfulDescription": "Each card has color based on priority", "minimalDescription": "Minimalist interface", "borderedDescription": "Clear borders", "darkDescription": "Dark theme with light text", "lightDescription": "Light theme with dark text", "gradientDescription": "Gradient background", "materialDescription": "Material design with shadows", "glassmorphismDescription": "Glassmorphism effect with blur", "neumorphismDescription": "Neumorphism effect with soft shadows"}, "searchPlaceholder": "Search cards...", "filterByPriority": "Filter by Priority", "filterByAssignee": "Filter by <PERSON><PERSON><PERSON>", "sortByDueDate": "Sort by Due Date", "sortByPriority": "Sort by Priority", "cardDetails": {"title": "Title", "description": "Description", "priority": "Priority", "dueDate": "Due Date", "assignee": "Assignee", "labels": "Labels", "progress": "Progress", "comments": "Comments", "attachments": "Attachments", "commentsCount": "{{count}} comments", "attachmentsCount": "{{count}} attachments", "editTitle": "Edit Card", "delete": "Delete", "cancel": "Cancel", "save": "Save", "close": "Close", "edit": "Edit", "descriptionPlaceholder": "Enter description...", "addLabel": "Add Label", "addComment": "Add comment", "addAttachment": "Add attachment"}}}