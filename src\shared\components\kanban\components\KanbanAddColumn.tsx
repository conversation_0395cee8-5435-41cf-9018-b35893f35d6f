import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Card, Icon, Input } from '@/shared/components/common';

import { KanbanColumn } from '../types/kanban.types';

export interface KanbanAddColumnProps {
  /**
   * Callback khi thêm cột mới
   */
  onAdd: (column: Partial<KanbanColumn>) => void;

  /**
   * Callback khi hủy thêm cột
   */
  onCancel: () => void;

  /**
   * Custom class name
   */
  className?: string;
}

/**
 * Component form thêm cột mới trong Kanban Board
 */
const KanbanAddColumn: React.FC<KanbanAddColumnProps> = ({ onAdd, onCancel, className = '' }) => {
  const { t } = useTranslation();
  const [title, setTitle] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // X<PERSON> lý thêm cột mới
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!title.trim()) {return;}

    setIsSubmitting(true);

    const newColumn: Partial<KanbanColumn> = {
      title: title.trim(),
      cardIds: [],
    };

    onAdd(newColumn);
    setTitle('');
    setIsSubmitting(false);
  };

  return (
    <Card className={`p-3 bg-white dark:bg-gray-800 min-w-[280px] ${className}`}>
      <form onSubmit={handleSubmit}>
        <Input
          value={title}
          onChange={e => setTitle(e.target.value)}
          placeholder={t('kanban.columnTitlePlaceholder', 'Enter column title...')}
          className="w-full mb-2"
          autoFocus
        />

        <div className="flex justify-end space-x-2">
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={onCancel}
            disabled={isSubmitting}
          >
            {t('common.cancel', 'Cancel')}
          </Button>

          <Button
            type="submit"
            variant="primary"
            size="sm"
            disabled={!title.trim() || isSubmitting}
          >
            {isSubmitting ? (
              <>
                <Icon name="loading" size="sm" className="animate-spin mr-1" />
                {t('common.adding', 'Adding...')}
              </>
            ) : (
              t('common.add', 'Add')
            )}
          </Button>
        </div>
      </form>
    </Card>
  );
};

export default KanbanAddColumn;
