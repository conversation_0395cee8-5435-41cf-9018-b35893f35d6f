import React, { useState, useCallback } from 'react';
import { Card, Button, Modal, Icon } from '@/shared/components/common';
import Calendar from '../components/Calendar';
import EventForm from '../components/EventForm';
import { CalendarEvent } from '../types';
import { DateSelectArg, EventClickArg } from '@fullcalendar/core';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@/shared/contexts/theme';
import '../styles/calendar.css';

/**
 * Calendar page component
 *
 * This page displays a full calendar with event management capabilities.
 */
const CalendarPage: React.FC = () => {
  const { t } = useTranslation(['common', 'calendar']);
  const { themeMode } = useTheme();
  const [events, setEvents] = useState<CalendarEvent[]>([
    {
      id: '1',
      title: 'Họp nhóm',
      start: new Date(new Date().setHours(10, 0, 0, 0)),
      end: new Date(new Date().setHours(12, 0, 0, 0)),
      description: '<PERSON><PERSON><PERSON><PERSON> họp nhóm hàng tuần để thảo luận về tiến độ dự án',
      location: 'Phòng họp A',
      className: 'calendar-event-meeting',
      extendedProps: {
        type: 'meeting',
      },
    },
    {
      id: '2',
      title: 'Ăn trưa với khách hàng',
      start: new Date(
        new Date(new Date().setDate(new Date().getDate() + 1)).setHours(12, 30, 0, 0)
      ),
      end: new Date(new Date(new Date().setDate(new Date().getDate() + 1)).setHours(14, 0, 0, 0)),
      description: 'Cuộc họp ăn trưa với khách hàng để thảo luận về yêu cầu mới',
      location: 'Nhà hàng trung tâm',
      className: 'calendar-event-appointment',
      extendedProps: {
        type: 'appointment',
      },
    },
    {
      id: '3',
      title: 'Hạn chót dự án',
      start: new Date(new Date().setDate(new Date().getDate() + 5)),
      allDay: true,
      description: 'Hạn chót cuối cùng để nộp dự án',
      className: 'calendar-event-deadline',
      extendedProps: {
        type: 'deadline',
      },
    },
  ]);

  // State cho form thêm sự kiện
  const [showEventModal, setShowEventModal] = useState(false);
  const [showEventDetailModal, setShowEventDetailModal] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<CalendarEvent | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [newEvent, setNewEvent] = useState<CalendarEvent>({
    id: '',
    title: '',
    start: new Date(),
    end: new Date(new Date().setHours(new Date().getHours() + 1)),
    allDay: false,
    className: 'calendar-event-meeting',
    extendedProps: {
      type: 'meeting',
    },
  });

  // Danh sách các loại sự kiện
  const eventTypes = [
    {
      value: 'meeting',
      label: t('calendar:eventTypes.meeting', 'Cuộc họp'),
      color: 'blue-500',
      icon: 'users'
    },
    {
      value: 'appointment',
      label: t('calendar:eventTypes.appointment', 'Cuộc hẹn'),
      color: 'green-500',
      icon: 'calendar'
    },
    {
      value: 'deadline',
      label: t('calendar:eventTypes.deadline', 'Hạn chót'),
      color: 'red-500',
      icon: 'alert-circle'
    },
    {
      value: 'training',
      label: t('calendar:eventTypes.training', 'Đào tạo'),
      color: 'purple-500',
      icon: 'document'
    },
    {
      value: 'workshop',
      label: t('calendar:eventTypes.workshop', 'Hội thảo'),
      color: 'orange-500',
      icon: 'presentation'
    },
    {
      value: 'planning',
      label: t('calendar:eventTypes.planning', 'Lập kế hoạch'),
      color: 'yellow-500',
      icon: 'layout'
    },
    {
      value: 'event',
      label: t('calendar:eventTypes.event', 'Sự kiện'),
      color: 'pink-500',
      icon: 'star'
    },
    {
      value: 'leave',
      label: t('calendar:eventTypes.leave', 'Nghỉ phép'),
      color: 'indigo-500',
      icon: 'home'
    },
    {
      value: 'client',
      label: t('calendar:eventTypes.client', 'Khách hàng'),
      color: 'teal-500',
      icon: 'building'
    },
    {
      value: 'personal',
      label: t('calendar:eventTypes.personal', 'Cá nhân'),
      color: 'gray-500',
      icon: 'user'
    },
  ];

  // Xử lý khi chọn ngày trên lịch
  const handleDateSelect = useCallback((selectInfo: DateSelectArg) => {
    setNewEvent({
      id: String(Date.now()),
      title: '',
      start: selectInfo.start,
      end: selectInfo.end,
      allDay: selectInfo.allDay,
      className: 'calendar-event-meeting',
      extendedProps: {
        type: 'meeting',
      },
    });
    setShowEventModal(true);
  }, []);

  // Xử lý khi click vào sự kiện
  const handleEventClick = useCallback((clickInfo: EventClickArg) => {
    const event = clickInfo.event;

    // Tạo đối tượng sự kiện từ thông tin được click
    const clickedEvent: CalendarEvent = {
      id: event.id,
      title: event.title,
      start: event.start || new Date(),
      end: event.end || undefined,
      allDay: event.allDay,
      description: event.extendedProps?.description || '',
      location: event.extendedProps?.location || '',
      className:
        Array.isArray(event.classNames) && event.classNames.length > 0
          ? event.classNames[0]
          : 'calendar-event-meeting',
      extendedProps: {
        type: event.extendedProps?.type || 'meeting',
      },
    };

    // Lưu sự kiện được chọn và hiển thị modal chi tiết
    setSelectedEvent(clickedEvent);
    setShowEventDetailModal(true);
  }, []);

  // Cập nhật trường của sự kiện mới
  const updateNewEventField = (
    field: keyof CalendarEvent,
    value: string | Date | boolean | undefined
  ) => {
    setNewEvent(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // Cập nhật loại sự kiện
  const updateEventType = (
    type: 'meeting' | 'appointment' | 'deadline' | 'training' | 'workshop' | 'planning' | 'event' | 'leave' | 'client' | 'personal'
  ) => {
    const className = `calendar-event-${type}`;
    setNewEvent(prev => ({
      ...prev,
      className,
      extendedProps: {
        ...prev.extendedProps,
        type,
      },
    }));
  };

  // Lưu sự kiện mới
  const handleSaveEvent = () => {
    if (!newEvent.title.trim()) return;

    setEvents(prev => [...prev, newEvent]);
    setShowEventModal(false);

    // Reset form
    setNewEvent({
      id: '',
      title: '',
      start: new Date(),
      end: new Date(new Date().setHours(new Date().getHours() + 1)),
      allDay: false,
      className: 'calendar-event-meeting',
      extendedProps: {
        type: 'meeting',
      },
    });
  };

  // Cập nhật sự kiện đã chọn
  const handleUpdateEvent = () => {
    if (!selectedEvent || !selectedEvent.title.trim()) return;

    setEvents(prev => prev.map(event => (event.id === selectedEvent.id ? selectedEvent : event)));

    setShowEventDetailModal(false);
    setSelectedEvent(null);
    setIsEditMode(false);
  };

  // Xóa sự kiện đã chọn
  const handleDeleteEvent = () => {
    if (!selectedEvent) return;

    setEvents(prev => prev.filter(event => event.id !== selectedEvent.id));
    setShowEventDetailModal(false);
    setSelectedEvent(null);
  };

  // Cập nhật trường của sự kiện đã chọn
  const updateSelectedEventField = (
    field: keyof CalendarEvent,
    value: string | Date | boolean | undefined
  ) => {
    if (!selectedEvent) return;

    setSelectedEvent(prev => {
      if (!prev) return prev;
      return {
        ...prev,
        [field]: value,
      };
    });
  };

  // Cập nhật loại sự kiện đã chọn
  const updateSelectedEventType = (
    type: 'meeting' | 'appointment' | 'deadline' | 'training' | 'workshop' | 'planning' | 'event' | 'leave' | 'client' | 'personal'
  ) => {
    if (!selectedEvent) return;

    const className = `calendar-event-${type}`;
    setSelectedEvent(prev => {
      if (!prev) return prev;
      return {
        ...prev,
        className,
        extendedProps: {
          ...prev.extendedProps,
          type,
        },
      };
    });
  };

  return (
    <div className="w-full bg-background text-foreground">
      <Card className="h-full">
        <div className="p-6">
          <Calendar
            events={events}
            editable={true}
            selectable={true}
            onDateSelect={handleDateSelect}
            onEventClick={handleEventClick}
            height="calc(100vh - 200px)"
            className={`calendar-${themeMode} w-full`}
          />
        </div>
      </Card>

      {/* Form thêm sự kiện mới */}
      {showEventModal && (
        <EventForm
          event={newEvent}
          eventTypes={eventTypes}
          isOpen={showEventModal}
          title={t('calendar:addEvent', 'Thêm sự kiện')}
          onClose={() => setShowEventModal(false)}
          onSave={handleSaveEvent}
          onUpdateField={updateNewEventField}
          onUpdateEventType={updateEventType}
        />
      )}

      {/* Modal xem chi tiết và chỉnh sửa sự kiện */}
      {showEventDetailModal &&
        selectedEvent &&
        (isEditMode ? (
          // Chế độ chỉnh sửa
          <EventForm
            event={selectedEvent}
            eventTypes={eventTypes}
            isOpen={showEventDetailModal}
            title={t('calendar:editEvent', 'Chỉnh sửa sự kiện')}
            onClose={() => {
              setIsEditMode(false);
              setShowEventDetailModal(false);
            }}
            onSave={handleUpdateEvent}
            onUpdateField={updateSelectedEventField}
            onUpdateEventType={updateSelectedEventType}
          />
        ) : (
          // Chế độ xem chi tiết
          <Modal
            isOpen={showEventDetailModal}
            onClose={() => setShowEventDetailModal(false)}
            title={t('calendar:eventDetails', 'Chi tiết sự kiện')}
            size="md"
            closeOnEsc={true}
            footer={
              <div className="flex justify-between w-full">
                <div className="flex gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    leftIcon={<Icon name="trash" size="xs" />}
                    onClick={handleDeleteEvent}
                    className="text-red-500 hover:text-red-600"
                  >
                    <span className="text-sm">{t('calendar:deleteEvent', 'Xóa')}</span>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    leftIcon={<Icon name="edit" size="xs" />}
                    onClick={() => setIsEditMode(true)}
                    className="hover:bg-card-hover transition-colors"
                  >
                    <span className="text-sm">{t('common:edit', 'Sửa')}</span>
                  </Button>
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowEventDetailModal(false)}
                  className="px-3 py-1 rounded-md hover:bg-card-hover transition-colors"
                >
                  <span className="text-sm">{t('common:close', 'Đóng')}</span>
                </Button>
              </div>
            }
          >
            <div className="space-y-4">
              <div
                className={`rounded-lg overflow-hidden shadow-sm border-l-4 border-${
                  selectedEvent.extendedProps?.type === 'meeting'
                    ? 'blue-500'
                    : selectedEvent.extendedProps?.type === 'appointment'
                      ? 'green-500'
                      : selectedEvent.extendedProps?.type === 'deadline'
                        ? 'red-500'
                        : selectedEvent.extendedProps?.type === 'training'
                          ? 'purple-500'
                          : selectedEvent.extendedProps?.type === 'workshop'
                            ? 'orange-500'
                            : selectedEvent.extendedProps?.type === 'planning'
                              ? 'yellow-500'
                              : selectedEvent.extendedProps?.type === 'event'
                                ? 'pink-500'
                                : selectedEvent.extendedProps?.type === 'leave'
                                  ? 'indigo-500'
                                  : selectedEvent.extendedProps?.type === 'client'
                                    ? 'teal-500'
                                    : selectedEvent.extendedProps?.type === 'personal'
                                      ? 'gray-500'
                                      : 'blue-500'
                }`}
              >
                <div className="p-4 bg-card">
                  <h3 className="text-xl font-medium mb-3">{selectedEvent.title}</h3>

                  <div className="space-y-2">
                    {/* Loại sự kiện */}
                    <div className="flex items-center gap-2">
                      <div
                        className={`w-3 h-3 rounded-full bg-${
                          selectedEvent.extendedProps?.type === 'meeting'
                            ? 'blue-500'
                            : selectedEvent.extendedProps?.type === 'appointment'
                              ? 'green-500'
                              : selectedEvent.extendedProps?.type === 'deadline'
                                ? 'red-500'
                                : selectedEvent.extendedProps?.type === 'training'
                                  ? 'purple-500'
                                  : selectedEvent.extendedProps?.type === 'workshop'
                                    ? 'orange-500'
                                    : selectedEvent.extendedProps?.type === 'planning'
                                      ? 'yellow-500'
                                      : selectedEvent.extendedProps?.type === 'event'
                                        ? 'pink-500'
                                        : selectedEvent.extendedProps?.type === 'leave'
                                          ? 'indigo-500'
                                          : selectedEvent.extendedProps?.type === 'client'
                                            ? 'teal-500'
                                            : selectedEvent.extendedProps?.type === 'personal'
                                              ? 'gray-500'
                                              : 'blue-500'
                        }`}
                      ></div>
                      <span className="text-sm">
                        {eventTypes.find(t => t.value === selectedEvent.extendedProps?.type)
                          ?.label || 'Sự kiện'}
                      </span>
                    </div>

                    {/* Thời gian */}
                    <div className="flex items-center gap-2">
                      <Icon name="calendar" size="sm" className="text-muted" />
                      <span className="text-sm">
                        {typeof selectedEvent.start === 'string'
                          ? new Date(selectedEvent.start).toLocaleDateString('vi-VN')
                          : selectedEvent.start.toLocaleDateString('vi-VN')}
                        {!selectedEvent.allDay &&
                          typeof selectedEvent.start === 'object' &&
                          ` ${selectedEvent.start.getHours().toString().padStart(2, '0')}:${selectedEvent.start.getMinutes().toString().padStart(2, '0')}`}
                        {selectedEvent.end &&
                          ` - ${
                            typeof selectedEvent.end === 'string'
                              ? new Date(selectedEvent.end).toLocaleDateString('vi-VN')
                              : selectedEvent.end.toLocaleDateString('vi-VN')
                          }
                        ${
                          !selectedEvent.allDay &&
                          typeof selectedEvent.end === 'object' &&
                          ` ${selectedEvent.end.getHours().toString().padStart(2, '0')}:${selectedEvent.end.getMinutes().toString().padStart(2, '0')}`
                        }`}
                      </span>
                    </div>

                    {/* Địa điểm */}
                    {selectedEvent.location && (
                      <div className="flex items-center gap-2">
                        <Icon name="map-pin" size="sm" className="text-muted" />
                        <span className="text-sm">{selectedEvent.location}</span>
                      </div>
                    )}
                  </div>

                  {/* Mô tả */}
                  {selectedEvent.description && (
                    <div className="mt-4 pt-3 border-t border-border">
                      <h4 className="text-sm font-medium mb-2">
                        {t('calendar:description', 'Mô tả')}
                      </h4>
                      <p className="text-sm whitespace-pre-line">{selectedEvent.description}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </Modal>
        ))}
    </div>
  );
};

export default CalendarPage;
