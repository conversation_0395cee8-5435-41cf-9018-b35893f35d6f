import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { ContractSignatureService } from '../services/contract-signature.service';
import type {
  SignatureQueryParams,
  SignatureCreateDto,
  SignatureUpdateDto,
  SignatureSignDto,
  SignatureDeclineDto,
  SignatureDelegateDto,
} from '../types/contract-signature.types';

/**
 * Query keys for contract signatures
 */
export const CONTRACT_SIGNATURE_QUERY_KEYS = {
  ALL: ['contract-signatures'] as const,
  LIST: (params: SignatureQueryParams) => [...CONTRACT_SIGNATURE_QUERY_KEYS.ALL, 'list', params] as const,
  DETAIL: (id: string) => [...CONTRACT_SIGNATURE_QUERY_KEYS.ALL, 'detail', id] as const,
  CONTRACT: (contractId: string) => [...CONTRACT_SIGNATURE_QUERY_KEYS.ALL, 'contract', contractId] as const,
  PROGRESS: (contractId: string) => [...CONTRACT_SIGNATURE_QUERY_KEYS.ALL, 'progress', contractId] as const,
  PENDING: (userId?: string) => [...CONTRACT_SIGNATURE_QUERY_KEYS.ALL, 'pending', userId] as const,
  STATS: () => [...CONTRACT_SIGNATURE_QUERY_KEYS.ALL, 'stats'] as const,
  AUDIT_TRAIL: (id: string) => [...CONTRACT_SIGNATURE_QUERY_KEYS.ALL, 'audit-trail', id] as const,
};

/**
 * Hook to get signatures list
 */
export const useContractSignatures = (params?: SignatureQueryParams) => {
  return useQuery({
    queryKey: CONTRACT_SIGNATURE_QUERY_KEYS.LIST(params || {}),
    queryFn: () => ContractSignatureService.getSignatures(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook to get signature by ID
 */
export const useContractSignature = (id: string) => {
  return useQuery({
    queryKey: CONTRACT_SIGNATURE_QUERY_KEYS.DETAIL(id),
    queryFn: () => ContractSignatureService.getSignature(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
};

/**
 * Hook to get signatures for a contract
 */
export const useContractSignaturesByContract = (contractId: string) => {
  return useQuery({
    queryKey: CONTRACT_SIGNATURE_QUERY_KEYS.CONTRACT(contractId),
    queryFn: () => ContractSignatureService.getContractSignatures(contractId),
    enabled: !!contractId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

/**
 * Hook to get signature progress for a contract
 */
export const useSignatureProgress = (contractId: string) => {
  return useQuery({
    queryKey: CONTRACT_SIGNATURE_QUERY_KEYS.PROGRESS(contractId),
    queryFn: () => ContractSignatureService.getSignatureProgress(contractId),
    enabled: !!contractId,
    staleTime: 1 * 60 * 1000, // 1 minute
  });
};

/**
 * Hook to get pending signatures for user
 */
export const usePendingSignatures = (userId?: string) => {
  return useQuery({
    queryKey: CONTRACT_SIGNATURE_QUERY_KEYS.PENDING(userId),
    queryFn: () => ContractSignatureService.getPendingSignatures(userId),
    staleTime: 2 * 60 * 1000,
  });
};

/**
 * Hook to get signature statistics
 */
export const useSignatureStats = () => {
  return useQuery({
    queryKey: CONTRACT_SIGNATURE_QUERY_KEYS.STATS(),
    queryFn: () => ContractSignatureService.getSignatureStats(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

/**
 * Hook to get signature audit trail
 */
export const useSignatureAuditTrail = (id: string) => {
  return useQuery({
    queryKey: CONTRACT_SIGNATURE_QUERY_KEYS.AUDIT_TRAIL(id),
    queryFn: () => ContractSignatureService.getSignatureAuditTrail(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
};

/**
 * Hook to create signature
 */
export const useCreateSignature = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: SignatureCreateDto) => ContractSignatureService.createSignature(data),
    onSuccess: (newSignature) => {
      // Invalidate signatures list
      queryClient.invalidateQueries({ queryKey: CONTRACT_SIGNATURE_QUERY_KEYS.ALL });
      // Invalidate contract signatures
      queryClient.invalidateQueries({ 
        queryKey: CONTRACT_SIGNATURE_QUERY_KEYS.CONTRACT(newSignature.contractId) 
      });
    },
  });
};

/**
 * Hook to update signature
 */
export const useUpdateSignature = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: SignatureUpdateDto }) =>
      ContractSignatureService.updateSignature(id, data),
    onSuccess: (updatedSignature) => {
      // Update signature detail cache
      queryClient.setQueryData(
        CONTRACT_SIGNATURE_QUERY_KEYS.DETAIL(updatedSignature.id),
        updatedSignature
      );
      // Invalidate signatures list
      queryClient.invalidateQueries({ queryKey: CONTRACT_SIGNATURE_QUERY_KEYS.ALL });
    },
  });
};

/**
 * Hook to delete signature
 */
export const useDeleteSignature = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => ContractSignatureService.deleteSignature(id),
    onSuccess: (_, deletedId) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: CONTRACT_SIGNATURE_QUERY_KEYS.DETAIL(deletedId) });
      // Invalidate signatures list
      queryClient.invalidateQueries({ queryKey: CONTRACT_SIGNATURE_QUERY_KEYS.ALL });
    },
  });
};

/**
 * Hook to sign contract
 */
export const useSignContract = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: SignatureSignDto }) =>
      ContractSignatureService.signContract(id, data),
    onSuccess: (updatedSignature) => {
      // Update signature detail cache
      queryClient.setQueryData(
        CONTRACT_SIGNATURE_QUERY_KEYS.DETAIL(updatedSignature.id),
        updatedSignature
      );
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: CONTRACT_SIGNATURE_QUERY_KEYS.ALL });
      queryClient.invalidateQueries({ 
        queryKey: CONTRACT_SIGNATURE_QUERY_KEYS.CONTRACT(updatedSignature.contractId) 
      });
      queryClient.invalidateQueries({ 
        queryKey: CONTRACT_SIGNATURE_QUERY_KEYS.PROGRESS(updatedSignature.contractId) 
      });
    },
  });
};

/**
 * Hook to decline signature
 */
export const useDeclineSignature = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: SignatureDeclineDto }) =>
      ContractSignatureService.declineSignature(id, data),
    onSuccess: (updatedSignature) => {
      // Update signature detail cache
      queryClient.setQueryData(
        CONTRACT_SIGNATURE_QUERY_KEYS.DETAIL(updatedSignature.id),
        updatedSignature
      );
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: CONTRACT_SIGNATURE_QUERY_KEYS.ALL });
      queryClient.invalidateQueries({ 
        queryKey: CONTRACT_SIGNATURE_QUERY_KEYS.CONTRACT(updatedSignature.contractId) 
      });
    },
  });
};

/**
 * Hook to delegate signature
 */
export const useDelegateSignature = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: SignatureDelegateDto }) =>
      ContractSignatureService.delegateSignature(id, data),
    onSuccess: (updatedSignature) => {
      // Update signature detail cache
      queryClient.setQueryData(
        CONTRACT_SIGNATURE_QUERY_KEYS.DETAIL(updatedSignature.id),
        updatedSignature
      );
      // Invalidate signatures list
      queryClient.invalidateQueries({ queryKey: CONTRACT_SIGNATURE_QUERY_KEYS.ALL });
    },
  });
};

/**
 * Hook to send reminder
 */
export const useSendSignatureReminder = () => {
  return useMutation({
    mutationFn: ({ id, message }: { id: string; message?: string }) =>
      ContractSignatureService.sendReminder(id, message),
  });
};

/**
 * Hook to resend signature request
 */
export const useResendSignatureRequest = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => ContractSignatureService.resendSignatureRequest(id),
    onSuccess: (updatedSignature) => {
      queryClient.setQueryData(
        CONTRACT_SIGNATURE_QUERY_KEYS.DETAIL(updatedSignature.id),
        updatedSignature
      );
    },
  });
};

/**
 * Hook to cancel signature request
 */
export const useCancelSignatureRequest = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, reason }: { id: string; reason?: string }) =>
      ContractSignatureService.cancelSignatureRequest(id, reason),
    onSuccess: (updatedSignature) => {
      queryClient.setQueryData(
        CONTRACT_SIGNATURE_QUERY_KEYS.DETAIL(updatedSignature.id),
        updatedSignature
      );
      queryClient.invalidateQueries({ queryKey: CONTRACT_SIGNATURE_QUERY_KEYS.ALL });
    },
  });
};

/**
 * Hook to validate signature
 */
export const useValidateSignature = () => {
  return useMutation({
    mutationFn: (id: string) => ContractSignatureService.validateSignature(id),
  });
};
