import { apiClient } from '@/shared/api';

import {
  UserPerformanceDto,
  ProjectPerformanceDto,
  ProjectGanttDto,
} from '../types/statistics.types';

/**
 * Service cho thống kê
 */
export const StatisticsService = {
  /**
   * Lấy hiệu suất người dùng
   * @param userId ID người dùng
   * @param startDate <PERSON><PERSON>y bắt đầu (timestamp)
   * @param endDate <PERSON><PERSON>y kết thúc (timestamp)
   * @param projectId ID dự án (tùy chọn)
   * @returns Promise với phản hồi API chứa hiệu suất người dùng
   */
  getUserPerformance: (
    userId: number,
    startDate?: number,
    endDate?: number,
    projectId?: number
  ) => {
    return apiClient.get<UserPerformanceDto>(`/api/statistics/users/${userId}/performance`, {
      params: { startDate, endDate, projectId },
    });
  },

  /**
   * L<PERSON>y hiệu suất dự án
   * @param projectId ID dự án
   * @param startDate <PERSON><PERSON><PERSON> bắ<PERSON> đầ<PERSON> (timestamp)
   * @param endDate <PERSON><PERSON>y kết thúc (timestamp)
   * @returns Promise với phản hồi API chứa hiệu suất dự án
   */
  getProjectPerformance: (projectId: number, startDate?: number, endDate?: number) => {
    return apiClient.get<ProjectPerformanceDto>(
      `/api/statistics/projects/${projectId}/performance`,
      {
        params: { startDate, endDate },
      }
    );
  },

  /**
   * Lấy dữ liệu biểu đồ Gantt của dự án
   * @param projectId ID dự án
   * @returns Promise với phản hồi API chứa dữ liệu biểu đồ Gantt
   */
  getProjectGantt: (projectId: number) => {
    return apiClient.get<ProjectGanttDto>(`/api/statistics/projects/${projectId}/gantt`);
  },

  /**
   * Lấy tổng quan thống kê
   * @param startDate Ngày bắt đầu (timestamp)
   * @param endDate Ngày kết thúc (timestamp)
   * @returns Promise với phản hồi API chứa tổng quan thống kê
   */
  getStatisticsOverview: (startDate?: number, endDate?: number) => {
    return apiClient.get('/api/statistics/overview', {
      params: { startDate, endDate },
    });
  },
};
