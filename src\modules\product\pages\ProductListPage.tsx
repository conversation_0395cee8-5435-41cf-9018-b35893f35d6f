import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { Badge, Button, Card, Icon, Table, Typography } from '@/shared/components/common';
import ActionMenu from '@/shared/components/common/ActionMenu';
import SlideInForm from '@/shared/components/common/SlideInForm';
import { TableColumn } from '@/shared/components/common/Table/types';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { formatDate } from '@/shared/utils/date';
import { formatCurrency } from '@/shared/utils/format';

import ProductForm from '../components/forms/ProductForm';
import ProductFilter from '../components/product/ProductFilter';
import { ProductDto, ProductQueryDto, ProductStatus, ProductType } from '../types/product.types';

/**
 * Trang danh sách sản phẩm
 */
const ProductListPage: React.FC = () => {
  const { t } = useTranslation(['product']);
  const navigate = useNavigate();

  // Mock data cho danh sách sản phẩm
  const [products, setProducts] = useState<ProductDto[]>([
    {
      id: '1',
      name: 'Laptop Dell XPS 13',
      description: 'Laptop cao cấp với màn hình 13 inch, CPU Intel Core i7, RAM 16GB',
      price: 30000000,
      originalPrice: 32000000,
      discount: 6.25,
      images: ['https://picsum.photos/id/0/200/200'],
      mainImage: 'https://picsum.photos/id/0/200/200',
      status: ProductStatus.ACTIVE,
      type: ProductType.PHYSICAL,
      categoryId: '1',
      categoryName: 'Laptop',
      stock: 10,
      rating: 4.5,
      ratingCount: 120,
      createdAt: '2023-06-01T00:00:00Z',
      updatedAt: '2023-06-15T00:00:00Z',
    },
    {
      id: '2',
      name: 'iPhone 14 Pro Max',
      description: 'Điện thoại cao cấp với màn hình 6.7 inch, chip A16 Bionic',
      price: 28000000,
      originalPrice: 30000000,
      discount: 6.67,
      images: ['https://picsum.photos/id/1/200/200'],
      mainImage: 'https://picsum.photos/id/1/200/200',
      status: ProductStatus.ACTIVE,
      type: ProductType.PHYSICAL,
      categoryId: '2',
      categoryName: 'Điện thoại',
      stock: 15,
      rating: 4.8,
      ratingCount: 250,
      createdAt: '2023-06-02T00:00:00Z',
      updatedAt: '2023-06-16T00:00:00Z',
    },
    {
      id: '3',
      name: 'Adobe Photoshop CC',
      description: 'Phần mềm chỉnh sửa ảnh chuyên nghiệp',
      price: 5000000,
      images: ['https://picsum.photos/id/2/200/200'],
      mainImage: 'https://picsum.photos/id/2/200/200',
      status: ProductStatus.ACTIVE,
      type: ProductType.DIGITAL,
      categoryId: '3',
      categoryName: 'Phần mềm',
      createdAt: '2023-06-03T00:00:00Z',
      updatedAt: '2023-06-17T00:00:00Z',
    },
    {
      id: '4',
      name: 'Dịch vụ thiết kế website',
      description: 'Dịch vụ thiết kế website chuyên nghiệp, tối ưu SEO',
      price: 15000000,
      images: ['https://picsum.photos/id/3/200/200'],
      mainImage: 'https://picsum.photos/id/3/200/200',
      status: ProductStatus.DRAFT,
      type: ProductType.SERVICE,
      categoryId: '4',
      categoryName: 'Dịch vụ',
      createdAt: '2023-06-04T00:00:00Z',
      updatedAt: '2023-06-18T00:00:00Z',
    },
  ]);

  // Mock data cho danh mục
  const categories = [
    { value: '1', label: 'Laptop' },
    { value: '2', label: 'Điện thoại' },
    { value: '3', label: 'Phần mềm' },
    { value: '4', label: 'Dịch vụ' },
  ];

  // State cho filter
  const [filters, setFilters] = useState<Partial<ProductQueryDto>>({});

  // State cho form
  const { isVisible, showForm, hideForm } = useSlideForm();
  const [selectedProduct, setSelectedProduct] = useState<ProductDto | undefined>(undefined);

  // Xử lý filter
  const handleFilter = (newFilters: Partial<ProductQueryDto>) => {
    setFilters(newFilters);
    // Trong thực tế, sẽ gọi API với filter mới
    console.log('Apply filters:', newFilters);
  };

  // Xử lý thêm/sửa sản phẩm
  const handleSubmit = (values: any) => {
    if (selectedProduct) {
      // Cập nhật sản phẩm
      const updatedProducts = products.map(p =>
        p.id === selectedProduct.id ? { ...p, ...values } : p
      );
      setProducts(updatedProducts);
    } else {
      // Thêm sản phẩm mới
      const newProduct: ProductDto = {
        id: `${products.length + 1}`,
        ...values,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      setProducts([...products, newProduct]);
    }
    hideForm();
    setSelectedProduct(undefined);
  };

  // Xử lý xóa sản phẩm
  const handleDelete = (id: string) => {
    const updatedProducts = products.filter(p => p.id !== id);
    setProducts(updatedProducts);
  };

  // Xử lý chỉnh sửa sản phẩm
  const handleEdit = (product: ProductDto) => {
    setSelectedProduct(product);
    showForm();
  };

  // Xử lý thêm sản phẩm mới
  const handleAddProduct = () => {
    setSelectedProduct(undefined);
    showForm();
  };

  // Xử lý xem chi tiết sản phẩm
  const handleViewProduct = (id: string) => {
    navigate(`/product/${id}`);
  };

  // Định nghĩa cột cho bảng
  const columns = useMemo<TableColumn<ProductDto>[]>(
    () => [
      {
        id: 'image',
        header: '',
        cell: ({ row }) => (
          <img src={row.mainImage} alt={row.name} className="w-10 h-10 rounded-md object-cover" />
        ),
        width: '60px',
      },
      {
        id: 'name',
        header: t('product:table.name'),
        cell: ({ row }) => (
          <div>
            <Typography variant="body1" className="font-medium">
              {row.name}
            </Typography>
            <Typography variant="caption" className="text-muted">
              ID: {row.id}
            </Typography>
          </div>
        ),
      },
      {
        id: 'price',
        header: t('product:table.price'),
        cell: ({ row }) => (
          <div>
            <Typography variant="body2" className="font-medium">
              {formatCurrency(row.price)}
            </Typography>
            {row.originalPrice && (
              <Typography variant="caption" className="text-muted line-through">
                {formatCurrency(row.originalPrice)}
              </Typography>
            )}
          </div>
        ),
      },
      {
        id: 'status',
        header: t('product:table.status'),
        cell: ({ row }) => {
          let variant: 'success' | 'warning' | 'danger' | 'info' = 'info';
          switch (row.status) {
            case ProductStatus.ACTIVE:
              variant = 'success';
              break;
            case ProductStatus.INACTIVE:
              variant = 'danger';
              break;
            case ProductStatus.DRAFT:
              variant = 'warning';
              break;
          }
          return <Badge variant={variant}>{t(`product:common.${row.status}`)}</Badge>;
        },
      },
      {
        id: 'type',
        header: t('product:table.type'),
        cell: ({ row }) => t(`product:common.${row.type}`),
      },
      {
        id: 'category',
        header: t('product:table.category'),
        cell: ({ row }) => row.categoryName || '-',
      },
      {
        id: 'createdAt',
        header: t('product:table.createdAt'),
        cell: ({ row }) => formatDate(row.createdAt),
      },
      {
        id: 'actions',
        header: t('product:common.actions'),
        cell: ({ row }) => (
          <ActionMenu
            items={[
              {
                key: 'view',
                label: t('product:common.view'),
                icon: 'eye',
                onClick: () => handleViewProduct(row.id),
              },
              {
                key: 'edit',
                label: t('product:common.edit'),
                icon: 'edit',
                onClick: () => handleEdit(row),
              },
              {
                key: 'delete',
                label: t('product:common.delete'),
                icon: 'trash-2',
                onClick: () => handleDelete(row.id),
                danger: true,
              },
            ]}
            showAllInMenu
          />
        ),
        width: '80px',
        align: 'center',
      },
    ],
    [t]
  );

  // Lọc sản phẩm theo filter
  const filteredProducts = useMemo(() => {
    return products.filter(product => {
      // Lọc theo trạng thái
      if (filters.status && product.status !== filters.status) {
        return false;
      }

      // Lọc theo loại
      if (filters.type && product.type !== filters.type) {
        return false;
      }

      // Lọc theo danh mục
      if (filters.categoryId && product.categoryId !== filters.categoryId) {
        return false;
      }

      // Lọc theo khoảng giá
      if (filters.minPrice && product.price < filters.minPrice) {
        return false;
      }
      if (filters.maxPrice && product.price > filters.maxPrice) {
        return false;
      }

      // Lọc theo từ khóa tìm kiếm
      if (
        filters.search &&
        !product.name.toLowerCase().includes(filters.search.toLowerCase()) &&
        !product.description?.toLowerCase().includes(filters.search.toLowerCase())
      ) {
        return false;
      }

      return true;
    });
  }, [products, filters]);

  return (
    <div>
      <MenuIconBar
        title={t('product:list.title')}
        primaryAction={{
          icon: 'plus',
          label: t('product:list.addProduct'),
          onClick: handleAddProduct,
        }}
        additionalIcons={[
          {
            icon: 'upload',
            label: t('product:list.importProducts'),
            onClick: () => console.log('Import products'),
          },
          {
            icon: 'download',
            label: t('product:list.exportProducts'),
            onClick: () => console.log('Export products'),
          },
        ]}
      />

      <ProductFilter onFilter={handleFilter} currentFilters={filters} categories={categories} />

      <Card>
        <div className="mb-4 flex justify-between items-center">
          <Typography variant="body2" className="text-muted">
            {t('product:list.showingProducts', {
              count: filteredProducts.length,
              total: products.length,
            })}
          </Typography>
          <Button
            variant="primary"
            leftIcon={<Icon name="plus" size="sm" />}
            onClick={handleAddProduct}
          >
            {t('product:list.addProduct')}
          </Button>
        </div>

        <Table
          columns={columns}
          data={filteredProducts}
          emptyMessage={t('product:list.noProducts')}
        />
      </Card>

      <SlideInForm isVisible={isVisible}>
        <ProductForm
          product={selectedProduct}
          onSubmit={handleSubmit}
          onCancel={hideForm}
          categories={categories}
        />
      </SlideInForm>
    </div>
  );
};

export default ProductListPage;
