import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import {
  Card,
  Select,
  RangePicker,
  Typography,
  Button,
  Icon,
} from '@/shared/components/common';

import EmployeeGanttChart from '../components/charts/EmployeeGanttChart';
import { useProjects } from '../hooks/useProjects';
import { useProjectGantt } from '../hooks/useStatistics';
import { useTasks } from '../hooks/useTasks';

/**
 * Trang biểu đồ Gantt nhân viên
 */
const EmployeeGanttPage: React.FC = () => {
  const { t } = useTranslation(['common', 'todolist']);
  const [selectedProjectId, setSelectedProjectId] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null]);
  const [viewMode, setViewMode] = useState<'day' | 'week' | 'month'>('day');

  // Lấy danh sách dự án
  const { data: projectsData, isLoading: isLoadingProjects } = useProjects();

  // Chuyển đổi dateRange thành timestamp
  const startDate = dateRange[0] ? dateRange[0].getTime() : undefined;
  const endDate = dateRange[1] ? dateRange[1].getTime() : undefined;

  // Lấy dữ liệu Gantt của dự án
  const { data: projectGantt, isLoading: isLoadingProjectGantt } = useProjectGantt(
    selectedProjectId ? parseInt(selectedProjectId, 10) : 0
  );

  // Lấy danh sách công việc
  const { data: tasksData, isLoading: isLoadingTasks } = useTasks({
    projectId: selectedProjectId ? parseInt(selectedProjectId, 10) : undefined,
    startDate,
    endDate,
    limit: 1000,
  });

  // Chuẩn bị danh sách nhân viên từ dữ liệu dự án
  const employees = useMemo(() => {
    if (!projectGantt?.members) {return [];}

    return projectGantt.members.map(member => ({
      id: member.userId,
      name: `User ${member.userId}`, // Trong thực tế, cần lấy tên thật của người dùng
    }));
  }, [projectGantt]);

  // Xử lý thay đổi dự án
  const handleProjectChange = (value: string | number | string[] | number[]) => {
    if (typeof value === 'string') {
      setSelectedProjectId(value);
    } else if (typeof value === 'number') {
      setSelectedProjectId(value.toString());
    }
  };

  // Xử lý thay đổi khoảng thời gian
  const handleDateRangeChange = (range: [Date | null, Date | null]) => {
    setDateRange(range);
  };

  // Xử lý thay đổi chế độ xem
  const handleViewModeChange = (mode: 'day' | 'week' | 'month') => {
    setViewMode(mode);
  };

  // Xử lý đặt lại bộ lọc
  const handleResetFilters = () => {
    setSelectedProjectId(null);
    setDateRange([null, null]);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <Typography variant="h3" className="mb-2">
            {t('todolist:employeeGantt.title', 'Biểu đồ Gantt nhân viên')}
          </Typography>
          <Typography variant="body1" color="muted">
            {t('todolist:employeeGantt.description', 'Xem thời gian hoạt động và thời gian trống của nhân viên')}
          </Typography>
        </div>
      </div>

      <Card className="p-4">
        <div className="flex flex-wrap gap-4">
          <div className="w-full md:w-64">
            <Typography variant="body2" className="mb-1">
              {t('todolist:statistics.filters.project', 'Dự án')}
            </Typography>
            <Select
              placeholder={t('todolist:statistics.filters.selectProject', 'Chọn dự án')}
              onChange={handleProjectChange}
              value={selectedProjectId || ''}
              options={
                projectsData?.items.map(project => ({
                  value: project.id.toString(),
                  label: project.title,
                })) || []
              }
              loading={isLoadingProjects}
              fullWidth
            />
          </div>
          <div className="w-full md:w-auto flex-grow">
            <Typography variant="body2" className="mb-1">
              {t('todolist:statistics.filters.dateRange', 'Khoảng thời gian')}
            </Typography>
            <RangePicker
              value={dateRange}
              onChange={handleDateRangeChange}
              placeholder={[
                t('todolist:statistics.filters.startDate', 'Ngày bắt đầu'),
                t('todolist:statistics.filters.endDate', 'Ngày kết thúc'),
              ]}
              fullWidth
            />
          </div>
          <div className="w-full md:w-auto flex items-end">
            <Button
              variant="outline"
              leftIcon={<Icon name="refresh" size="sm" />}
              onClick={handleResetFilters}
            >
              {t('common:reset', 'Đặt lại')}
            </Button>
          </div>
        </div>
      </Card>

      <Card className="p-4">
        <div className="flex justify-between items-center mb-4">
          <Typography variant="h6">
            {t('todolist:employeeGantt.chartTitle', 'Thời gian hoạt động của nhân viên')}
          </Typography>
          <div className="flex space-x-2">
            <Button
              variant={viewMode === 'day' ? 'primary' : 'outline'}
              size="sm"
              onClick={() => handleViewModeChange('day')}
            >
              {t('todolist:employeeGantt.viewMode.day', 'Ngày')}
            </Button>
            <Button
              variant={viewMode === 'week' ? 'primary' : 'outline'}
              size="sm"
              onClick={() => handleViewModeChange('week')}
            >
              {t('todolist:employeeGantt.viewMode.week', 'Tuần')}
            </Button>
            <Button
              variant={viewMode === 'month' ? 'primary' : 'outline'}
              size="sm"
              onClick={() => handleViewModeChange('month')}
            >
              {t('todolist:employeeGantt.viewMode.month', 'Tháng')}
            </Button>
          </div>
        </div>

        <div className="h-[600px]">
          {selectedProjectId ? (
            <EmployeeGanttChart
              tasks={tasksData?.items || []}
              employees={employees}
              startDate={dateRange[0] || undefined}
              endDate={dateRange[1] || undefined}
              height={550}
              isLoading={isLoadingTasks || isLoadingProjectGantt}
            />
          ) : (
            <div className="h-full flex items-center justify-center">
              <Typography variant="body1" color="muted">
                {t('todolist:employeeGantt.selectProject', 'Vui lòng chọn một dự án để xem biểu đồ Gantt')}
              </Typography>
            </div>
          )}
        </div>
      </Card>

      {selectedProjectId && projectGantt && (
        <Card className="p-4">
          <Typography variant="h6" className="mb-4">
            {t('todolist:employeeGantt.legend', 'Chú thích')}
          </Typography>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center">
              <div className="w-4 h-4 rounded bg-[#fff7ed] border border-[#f59e0b] mr-2"></div>
              <Typography variant="body2">
                {t('todolist:employeeGantt.legend.inProgress', 'Đang thực hiện')}
              </Typography>
            </div>
            <div className="flex items-center">
              <div className="w-4 h-4 rounded bg-[#dcfce7] border border-[#22c55e] mr-2"></div>
              <Typography variant="body2">
                {t('todolist:employeeGantt.legend.completed', 'Đã hoàn thành')}
              </Typography>
            </div>
            <div className="flex items-center">
              <div className="w-4 h-4 rounded bg-[#f1f5f9] border border-[#94a3b8] mr-2"></div>
              <Typography variant="body2">
                {t('todolist:employeeGantt.legend.freeTime', 'Thời gian trống')}
              </Typography>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};

export default EmployeeGanttPage;
