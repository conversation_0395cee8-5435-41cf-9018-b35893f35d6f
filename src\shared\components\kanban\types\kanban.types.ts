/**
 * Đ<PERSON><PERSON> nghĩa các types cho Kanban Board
 */

/**
 * Thẻ Kanban
 */
export interface KanbanCard {
  /**
   * ID của thẻ
   */
  id: string;

  /**
   * Tiêu đề thẻ
   */
  title: string;

  /**
   * <PERSON><PERSON> tả thẻ
   */
  description?: string;

  /**
   * Mức độ ưu tiên
   */
  priority?: 'low' | 'medium' | 'high' | 'urgent';

  /**
   * Người được giao
   */
  assignee?: {
    id: string;
    name: string;
    avatar?: string;
  };

  /**
   * Thời hạn
   */
  dueDate?: string;

  /**
   * Nhãn
   */
  labels?: Array<{
    id: string;
    name: string;
    color?: string;
  }>;

  /**
   * Số lượng bình luận
   */
  commentsCount?: number;

  /**
   * Số lượng tệp đính kèm
   */
  attachmentsCount?: number;

  /**
   * Tiến độ (0-100)
   */
  progress?: number;

  /**
   * Metada<PERSON> bổ sung
   */
  metadata?: Record<string, unknown>;
}

/**
 * C<PERSON><PERSON> Kanban
 */
export interface KanbanColumn {
  /**
   * ID của cột
   */
  id: string;

  /**
   * Tiêu đề cột
   */
  title: string;

  /**
   * Màu sắc cột
   */
  color?: string;

  /**
   * Giới hạn số lượng thẻ trong cột
   */
  limit?: number;

  /**
   * Danh sách ID của các thẻ trong cột
   */
  cardIds: string[];

  /**
   * Metadata bổ sung
   */
  metadata?: Record<string, unknown>;
}

/**
 * Dữ liệu Kanban Board
 */
export interface KanbanBoardData {
  /**
   * Danh sách các cột
   */
  columns: KanbanColumn[];

  /**
   * Danh sách các thẻ
   */
  cards: Record<string, KanbanCard>;
}

/**
 * Kết quả của việc kéo thả thẻ
 */
export interface KanbanMoveResult {
  /**
   * ID của thẻ được kéo
   */
  cardId: string;

  /**
   * ID của cột nguồn
   */
  sourceColumnId: string;

  /**
   * ID của cột đích
   */
  targetColumnId: string;

  /**
   * Vị trí mới trong cột đích
   */
  newIndex: number;
}

/**
 * Loại item kéo thả
 */
export enum DragItemType {
  CARD = 'card',
  COLUMN = 'column',
}

/**
 * Item thẻ kéo thả
 */
export interface CardDragItem {
  type: DragItemType.CARD;
  id: string;
  columnId: string;
  index: number;
  card: KanbanCard;
}

/**
 * Item cột kéo thả
 */
export interface ColumnDragItem {
  type: DragItemType.COLUMN;
  id: string;
  index: number;
  column: KanbanColumn;
}

/**
 * Union type cho các item kéo thả
 */
export type DragItem = CardDragItem | ColumnDragItem;

/**
 * Biến thể giao diện của Kanban Board
 */
export enum KanbanVariant {
  DEFAULT = 'default',
  COMPACT = 'compact',
  DETAILED = 'detailed',
  HORIZONTAL = 'horizontal',
  VERTICAL = 'vertical',
  SWIMLANE = 'swimlane',
  TIMELINE = 'timeline',
  GRID = 'grid',
  MASONRY = 'masonry',
}

/**
 * Biến thể chức năng của Kanban Board
 */
export enum KanbanFunctionality {
  SIMPLE = 'simple',
  ADVANCED = 'advanced',
  READONLY = 'readonly',
  INTERACTIVE = 'interactive',
  COLLABORATIVE = 'collaborative',
  FILTERED = 'filtered',
  SEARCHABLE = 'searchable',
  SORTABLE = 'sortable',
}

/**
 * Biến thể theme của Kanban Board
 */
export enum KanbanTheme {
  DEFAULT = 'default',
  COLORFUL = 'colorful',
  MINIMAL = 'minimal',
  BORDERED = 'bordered',
  DARK = 'dark',
  LIGHT = 'light',
  GRADIENT = 'gradient',
  MATERIAL = 'material',
  GLASSMORPHISM = 'glassmorphism',
  NEUMORPHISM = 'neumorphism',
}
