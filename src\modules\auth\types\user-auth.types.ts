
/**
 * Trạng thái người dùng
 */
export enum UserStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  SUSPENDED = 'SUSPENDED',
  DELETED = 'DELETED',
}

/**
 * Thông tin người dùng
 */
export interface User {
  id: number;
  username: string;
  email: string;
  fullName: string | null;
  status: UserStatus | null;
  position: string | null;
  createdAt: number | null;
  avatarUrl: string | null;
  permissions?: string[];
}

/**
 * Yêu cầu đăng nhập người dùng
 */
export interface UserLoginRequest {
  username: string;
  password: string;
  recaptchaToken?: string;
}

/**
 * Phản hồi đăng nhập người dùng
 */
export interface UserLoginResponse {
  accessToken: string;
  user: User;
  permissions: string[];
}
