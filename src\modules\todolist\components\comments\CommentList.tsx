import React from 'react';
import { useTranslation } from 'react-i18next';

import {
  <PERSON><PERSON>,
  Typography,
  Avatar,
  IconButton,
  Tooltip,
  Divider,
} from '@/shared/components/common';
import { useAuth } from '@/shared/hooks/useAuth';
import { formatDate } from '@/shared/utils/date';
import { NotificationUtil } from '@/shared/utils/notification';

import { useDeleteComment } from '../../hooks/useComments';
import { CommentDto } from '../../types/comment.types';

import CommentForm from './CommentForm';

interface CommentListProps {
  taskId: number;
  comments: CommentDto[];
  isLoading: boolean;
  onRefresh: () => void;
}

/**
 * Component to display a list of comments for a task
 */
const CommentList: React.FC<CommentListProps> = ({ taskId, comments, isLoading, onRefresh }) => {
  const { t } = useTranslation(['common', 'todolist']);
  const { user } = useAuth();
  const { mutateAsync: deleteComment } = useDeleteComment();
  const [editingCommentId, setEditingCommentId] = React.useState<number | null>(null);

  // Handle delete comment
  const handleDeleteComment = async (commentId: number) => {
    try {
      await deleteComment({ taskId, commentId });
      NotificationUtil.success(
        t('todolist:comment.notifications.deleteSuccess', 'Comment deleted successfully')
      );
      onRefresh();
    } catch (error) {
      console.error('Error deleting comment:', error);
      NotificationUtil.error(
        t('todolist:comment.notifications.deleteError', 'Error deleting comment')
      );
    }
  };

  // Handle edit comment
  const handleEditComment = (commentId: number) => {
    setEditingCommentId(commentId);
  };

  // Handle cancel edit
  const handleCancelEdit = () => {
    setEditingCommentId(null);
  };

  // Handle submit edit
  const handleSubmitEdit = () => {
    setEditingCommentId(null);
    onRefresh();
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-32">
        <Spinner size="md" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <CommentForm taskId={taskId} onSubmit={onRefresh} />

      <Divider />

      {comments.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          {t('todolist:comment.empty', 'No comments yet. Be the first to comment!')}
        </div>
      ) : (
        <div className="space-y-6">
          {comments.map(comment => (
            <div key={comment.id} className="bg-gray-50 rounded-lg p-4">
              {editingCommentId === comment.id ? (
                <CommentForm
                  taskId={taskId}
                  comment={comment}
                  onSubmit={handleSubmitEdit}
                  onCancel={handleCancelEdit}
                />
              ) : (
                <>
                  <div className="flex justify-between items-start">
                    <div className="flex items-start space-x-3">
                      <Avatar
                        src={comment.userAvatar}
                        alt={comment.userName || `User ${comment.userId}`}
                        size="md"
                        fallback={comment.userName?.[0] || `U${comment.userId}`}
                      />
                      <div>
                        <div className="flex items-center space-x-2">
                          <Typography variant="subtitle1" className="font-medium">
                            {comment.userName || `User ${comment.userId}`}
                          </Typography>
                          <Typography variant="caption" className="text-gray-500">
                            {formatDate(comment.createdAt || 0)}
                          </Typography>
                          {comment.updatedAt !== comment.createdAt && (
                            <Typography variant="caption" className="text-gray-500 italic">
                              ({t('todolist:comment.edited', 'edited')})
                            </Typography>
                          )}
                        </div>
                        <Typography variant="body1" className="mt-1 whitespace-pre-wrap">
                          {comment.content}
                        </Typography>
                      </div>
                    </div>

                    {user?.id === comment.userId && (
                      <div className="flex space-x-1">
                        <Tooltip content={t('common:edit', 'Edit')}>
                          <IconButton
                            icon="edit"
                            size="sm"
                            variant="ghost"
                            onClick={() => handleEditComment(comment.id)}
                          />
                        </Tooltip>
                        <Tooltip content={t('common:delete', 'Delete')}>
                          <IconButton
                            icon="trash"
                            size="sm"
                            variant="ghost"
                            color="error"
                            onClick={() => handleDeleteComment(comment.id)}
                          />
                        </Tooltip>
                      </div>
                    )}
                  </div>
                </>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default CommentList;
