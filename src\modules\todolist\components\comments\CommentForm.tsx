import React from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import * as z from 'zod';

import { Button, Form, FormItem, Textarea, Avatar } from '@/shared/components/common';
import { useAuth } from '@/shared/hooks/useAuth';
import { NotificationUtil } from '@/shared/utils/notification';
import { zodResolver } from '@hookform/resolvers/zod';

import { useCreateComment, useUpdateComment } from '../../hooks/useComments';
import { CommentDto } from '../../types/comment.types';

// Define schema validation
const commentSchema = z.object({
  content: z
    .string()
    .min(1, 'Comment cannot be empty')
    .max(1000, 'Comment cannot exceed 1000 characters'),
});

// Form values type
type CommentFormValues = z.infer<typeof commentSchema>;

// Props interface
interface CommentFormProps {
  taskId: number;
  comment?: CommentDto;
  onSubmit: () => void;
  onCancel?: () => void;
}

/**
 * Form for creating or editing a comment
 */
const CommentForm: React.FC<CommentFormProps> = ({ taskId, comment, onSubmit, onCancel }) => {
  const { t } = useTranslation(['common', 'todolist']);
  const { user } = useAuth();
  const isEditMode = !!comment;

  // Mutations
  const { mutateAsync: createComment, isPending: isCreating } = useCreateComment();
  const { mutateAsync: updateComment, isPending: isUpdating } = useUpdateComment();

  // Form hook
  const form = useForm<CommentFormValues>({
    resolver: zodResolver(commentSchema),
    defaultValues: {
      content: comment?.content || '',
    },
  });

  // Handle form submission
  const handleSubmit = async (values: CommentFormValues) => {
    try {
      if (isEditMode && comment) {
        await updateComment({
          taskId,
          commentId: comment.id,
          data: {
            content: values.content,
          },
        });
        NotificationUtil.success({
          message: t(
            'todolist:comment.notifications.updateSuccess',
            'Comment updated successfully'
          ),
        });
      } else {
        await createComment({
          taskId,
          content: values.content,
        });
        NotificationUtil.success({
          message: t('todolist:comment.notifications.createSuccess', 'Comment added successfully'),
        });
        form.reset({ content: '' });
      }
      onSubmit();
    } catch (error) {
      console.error('Error submitting comment:', error);
      NotificationUtil.error({
        message: isEditMode
          ? t('todolist:comment.notifications.updateError', 'Error updating comment')
          : t('todolist:comment.notifications.createError', 'Error adding comment'),
      });
    }
  };

  return (
    <Form form={form} onSubmit={handleSubmit} className="space-y-4">
      <div className="flex space-x-3">
        <Avatar src={user?.avatar} alt={user?.name?.[0] || 'U'} size="md" />
        <div className="flex-1">
          <FormItem name="content">
            <Textarea
              placeholder={t('todolist:comment.placeholders.content', 'Write a comment...')}
              rows={3}
              className="w-full"
            />
          </FormItem>

          <div className="flex justify-end space-x-2 mt-2">
            {isEditMode && onCancel && (
              <Button variant="outline" onClick={onCancel} disabled={isCreating || isUpdating}>
                {t('common:cancel', 'Cancel')}
              </Button>
            )}
            <Button type="submit" isLoading={isCreating || isUpdating}>
              {isEditMode
                ? t('common:update', 'Update')
                : t('todolist:comment.actions.post', 'Post Comment')}
            </Button>
          </div>
        </div>
      </div>
    </Form>
  );
};

export default CommentForm;
