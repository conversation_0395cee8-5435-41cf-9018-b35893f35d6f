import React from 'react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';

import {
  Button,
  Card,
  Form,
  FormGrid,
  FormItem,
  Input,
  Select,
  Textarea,
  Typography,
} from '@/shared/components/common';
import AsyncSelectWithPagination from '@/shared/components/common/Select/AsyncSelectWithPagination';

import { loadKeyResultsForAsyncSelect } from '../hooks/useKeyResults';
import {
  CheckInFrequency,
  KeyResultFormData,
  KeyResultSubmitHandler,
} from '../types/key-result.types';
import { ObjectiveDto } from '../types/objective.types';

interface KeyResultFormProps {
  objective: ObjectiveDto | null;
  onSubmit: KeyResultSubmitHandler;
  onCancel: () => void;
}

/**
 * Form thêm mới key result
 */
const KeyResultForm: React.FC<KeyResultFormProps> = ({ objective, onSubmit, onCancel }) => {
  const { t } = useTranslation(['okrs', 'common']);

  // Schema validation cho form
  const keyResultSchema = z.object({
    objectiveId: z.number(),
    title: z.string().min(1, t('common:form.required', 'Trường này là bắt buộc')),
    description: z.string().optional(),
    targetValue: z
      .number()
      .min(0, t('common:form.minValue', 'Giá trị tối thiểu là {{min}}', { min: 0 })),
    startValue: z.number().optional(),
    unit: z.string().optional(),
    format: z.string().optional(),
    measurementMethod: z.string().optional(),
    weight: z
      .number()
      .min(0, t('common:form.minValue', 'Giá trị tối thiểu là {{min}}', { min: 0 }))
      .max(100, t('common:form.maxValue', 'Giá trị tối đa là {{max}}', { max: 100 }))
      .optional(),
    checkInFrequency: z.string().optional(),
    supportingKeyResultId: z.number().optional(),
  });

  // Default values cho form
  const defaultValues: KeyResultFormData = {
    objectiveId: objective?.id || 0,
    title: '',
    description: '',
    targetValue: 100,
    startValue: 0,
    unit: '',
    format: 'number',
    measurementMethod: '',
    weight: 100,
    checkInFrequency: CheckInFrequency.WEEKLY,
    supportingKeyResultId: undefined,
  };

  return (
    <Card>
      <Typography variant="h4" className="mb-4">
        {t('okrs:keyResult.form.title', 'Thêm kết quả chính')}
      </Typography>

      {objective && (
        <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <Typography variant="body2" className="text-gray-500 dark:text-gray-400 mb-1">
            {t('okrs:objective.title', 'Mục tiêu')}:
          </Typography>
          <Typography variant="body1" className="font-medium">
            {objective.title || ''}
          </Typography>

          {objective.parentId && (
            <div className="mt-2">
              <Typography variant="body2" className="text-gray-500 dark:text-gray-400 mb-1">
                {t('okrs:objective.parentObjective', 'Mục tiêu phụ trợ')}:
              </Typography>
              <Typography variant="body2" className="text-gray-600 dark:text-gray-300">
                {t('okrs:objective.hasParent', 'Mục tiêu này hỗ trợ cho mục tiêu cấp cao hơn')}
              </Typography>
            </div>
          )}
        </div>
      )}

      <Form
        schema={keyResultSchema}
        // @ts-expect-error - Form component expects FieldValues but we're using KeyResultFormData
        onSubmit={onSubmit}
        defaultValues={defaultValues}
      >
        <FormGrid columns={2} columnsMd={2} columnsSm={1} gap="md">
          <FormItem
            name="title"
            label={t('okrs:keyResult.form.title', 'Tiêu đề')}
            required
            className="col-span-full"
          >
            <Input
              placeholder={t('okrs:keyResult.form.titlePlaceholder', 'Nhập tiêu đề kết quả chính')}
              fullWidth
            />
          </FormItem>

          <FormItem
            name="description"
            label={t('okrs:keyResult.form.description', 'Mô tả')}
            className="col-span-full"
          >
            <Textarea
              rows={3}
              placeholder={t(
                'okrs:keyResult.form.descriptionPlaceholder',
                'Mô tả chi tiết về kết quả chính'
              )}
              fullWidth
            />
          </FormItem>

          <FormItem
            name="targetValue"
            label={t('okrs:keyResult.form.targetValue', 'Giá trị mục tiêu')}
            required
            className="col-span-full md:col-span-1"
          >
            <Input type="number" fullWidth />
          </FormItem>

          <FormItem
            name="startValue"
            label={t('okrs:keyResult.form.startValue', 'Giá trị ban đầu')}
            className="col-span-full md:col-span-1"
          >
            <Input type="number" fullWidth />
          </FormItem>

          <FormItem
            name="unit"
            label={t('okrs:keyResult.form.unit', 'Đơn vị')}
            className="col-span-full md:col-span-1"
          >
            <Input
              placeholder={t('okrs:keyResult.form.unitPlaceholder', 'VD: khách hàng, đơn hàng, %')}
              fullWidth
            />
          </FormItem>

          <FormItem
            name="format"
            label={t('okrs:keyResult.form.format', 'Định dạng hiển thị')}
            className="col-span-full md:col-span-1"
          >
            <div className="relative z-[100]">
              <Select
                options={[
                  { value: 'number', label: t('okrs:keyResult.format.number', 'Số') },
                  {
                    value: 'percentage',
                    label: t('okrs:keyResult.format.percentage', 'Phần trăm'),
                  },
                  { value: 'currency', label: t('okrs:keyResult.format.currency', 'Tiền tệ') },
                  { value: 'boolean', label: t('okrs:keyResult.format.boolean', 'Có/Không') },
                ]}
                fullWidth
              />
            </div>
          </FormItem>

          <FormItem
            name="measurementMethod"
            label={t('okrs:keyResult.form.measurementMethod', 'Phương pháp đo lường')}
            className="col-span-full md:col-span-1"
          >
            <Input
              placeholder={t(
                'okrs:keyResult.form.measurementMethodPlaceholder',
                'VD: Số lượng đăng ký mới'
              )}
              fullWidth
            />
          </FormItem>

          <FormItem
            name="weight"
            label={t('okrs:keyResult.form.weight', 'Trọng số (%)')}
            className="col-span-full md:col-span-1"
          >
            <Input type="number" min={0} max={100} fullWidth />
          </FormItem>

          <FormItem
            name="checkInFrequency"
            label={t('okrs:keyResult.form.checkInFrequency', 'Tần suất cập nhật')}
            className="col-span-full md:col-span-1"
          >
            <div className="relative z-[200]">
              <Select
                options={[
                  {
                    value: CheckInFrequency.DAILY,
                    label: t('okrs:keyResult.frequency.daily', 'Hàng ngày'),
                  },
                  {
                    value: CheckInFrequency.WEEKLY,
                    label: t('okrs:keyResult.frequency.weekly', 'Hàng tuần'),
                  },
                  {
                    value: CheckInFrequency.MONTHLY,
                    label: t('okrs:keyResult.frequency.monthly', 'Hàng tháng'),
                  },
                ]}
                fullWidth
              />
            </div>
          </FormItem>

          <FormItem
            name="supportingKeyResultId"
            label={t('okrs:keyResult.form.supportingKeyResult', 'Key Result phụ trợ')}
            className="col-span-full md:col-span-1"
          >
            <div className="relative z-[300]">
              <AsyncSelectWithPagination
                loadOptions={loadKeyResultsForAsyncSelect}
                placeholder={t(
                  'okrs:keyResult.form.supportingKeyResultPlaceholder',
                  'Chọn Key Result phụ trợ'
                )}
                debounceTime={300}
                noOptionsMessage={t('common:noResults', 'Không tìm thấy Key Result')}
                loadingMessage={t('common:loading', 'Đang tìm kiếm...')}
                autoLoadInitial={true}
                itemsPerPage={10}
                fullWidth
              />
            </div>
          </FormItem>
        </FormGrid>

        <div className="mt-6 flex justify-end space-x-3">
          <Button variant="outline" onClick={onCancel}>
            {t('common:cancel', 'Hủy')}
          </Button>
          <Button type="submit" variant="primary">
            {t('common:save', 'Lưu')}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default KeyResultForm;
