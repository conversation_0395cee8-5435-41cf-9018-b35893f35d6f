import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';

import { formatDate } from '@/shared/utils/date';

import { TaskDto } from '../../types/task.types';

interface TaskProgressChartProps {
  tasks: TaskDto[];
  startDate?: Date;
  endDate?: Date;
}

/**
 * <PERSON>iể<PERSON> đồ hiển thị tiến độ công việc theo thời gian
 */
const TaskProgressChart: React.FC<TaskProgressChartProps> = ({ tasks, startDate, endDate }) => {
  const { t } = useTranslation(['todolist']);

  // Chuẩn bị dữ liệu cho biểu đồ
  const chartData = useMemo(() => {
    if (!tasks || tasks.length === 0) {return [];}

    // Xá<PERSON> định khoảng thời gian
    const start = startDate ? startDate.getTime() : Math.min(...tasks.map(task => task.createdAt || 0));
    const end = endDate ? endDate.getTime() : Math.max(...tasks.map(task => task.completedAt || Date.now()));

    // Tạo mảng các ngày trong khoảng thời gian
    const days: Record<string, { date: string; completed: number; inProgress: number; pending: number; total: number }> = {};
    
    // Khởi tạo mảng ngày
    const currentDate = new Date(start);
    const lastDate = new Date(end);
    
    while (currentDate <= lastDate) {
      const dateStr = currentDate.toISOString().split('T')[0];
      days[dateStr] = { date: dateStr, completed: 0, inProgress: 0, pending: 0, total: 0 };
      currentDate.setDate(currentDate.getDate() + 1);
    }

    // Phân loại công việc theo ngày tạo
    for (const task of tasks) {
      if (!task.createdAt) {continue;}
      
      const createdDate = new Date(task.createdAt).toISOString().split('T')[0];
      const completedDate = task.completedAt ? new Date(task.completedAt).toISOString().split('T')[0] : null;
      
      // Đảm bảo ngày tạo nằm trong khoảng thời gian
      if (days[createdDate]) {
        days[createdDate].total += 1;
        
        if (completedDate && days[completedDate]) {
          days[completedDate].completed += 1;
        } else if (task.status === 'IN_PROGRESS') {
          days[createdDate].inProgress += 1;
        } else if (task.status === 'PENDING') {
          days[createdDate].pending += 1;
        }
      }
    }

    // Tính toán giá trị tích lũy
    let cumulativeCompleted = 0;
    let cumulativeInProgress = 0;
    let cumulativePending = 0;
    let cumulativeTotal = 0;

    return Object.values(days).map(day => {
      cumulativeTotal += day.total;
      cumulativeCompleted += day.completed;
      cumulativeInProgress += day.inProgress;
      cumulativePending += day.pending;

      return {
        date: day.date,
        totalTasks: cumulativeTotal,
        completedTasks: cumulativeCompleted,
        inProgressTasks: cumulativeInProgress,
        pendingTasks: cumulativePending,
      };
    });
  }, [tasks, startDate, endDate]);

  // Nếu không có dữ liệu, hiển thị thông báo
  if (chartData.length === 0) {
    return (
      <div className="h-full flex items-center justify-center text-gray-500">
        {t('todolist:dashboard.charts.noData', 'Không có dữ liệu')}
      </div>
    );
  }

  return (
    <ResponsiveContainer width="100%" height="100%">
      <AreaChart
        data={chartData}
        margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
      >
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis 
          dataKey="date" 
          tickFormatter={(value) => formatDate(value)}
          label={{ value: t('todolist:statistics.charts.date', 'Ngày'), position: 'insideBottomRight', offset: -10 }}
        />
        <YAxis 
          label={{ value: t('todolist:statistics.charts.tasks', 'Số công việc'), angle: -90, position: 'insideLeft' }}
        />
        <Tooltip 
          formatter={(value: number, name: string) => {
            const label = name === 'totalTasks' 
              ? t('todolist:statistics.metrics.totalTasks', 'Tổng số công việc')
              : name === 'completedTasks'
              ? t('todolist:statistics.metrics.completedTasks', 'Đã hoàn thành')
              : name === 'inProgressTasks'
              ? t('todolist:statistics.metrics.inProgressTasks', 'Đang thực hiện')
              : t('todolist:statistics.metrics.pendingTasks', 'Chưa bắt đầu');
            return [value, label];
          }}
          labelFormatter={(label) => formatDate(label)}
        />
        <Legend 
          formatter={(value) => {
            return value === 'totalTasks' 
              ? t('todolist:statistics.metrics.totalTasks', 'Tổng số công việc')
              : value === 'completedTasks'
              ? t('todolist:statistics.metrics.completedTasks', 'Đã hoàn thành')
              : value === 'inProgressTasks'
              ? t('todolist:statistics.metrics.inProgressTasks', 'Đang thực hiện')
              : t('todolist:statistics.metrics.pendingTasks', 'Chưa bắt đầu');
          }}
        />
        <Area 
          type="monotone" 
          dataKey="totalTasks" 
          stroke="#94a3b8" 
          fill="#94a3b8" 
          stackId="1"
          activeDot={{ r: 8 }}
        />
        <Area 
          type="monotone" 
          dataKey="completedTasks" 
          stroke="#22c55e" 
          fill="#22c55e" 
          stackId="2"
        />
        <Area 
          type="monotone" 
          dataKey="inProgressTasks" 
          stroke="#3b82f6" 
          fill="#3b82f6" 
          stackId="2"
        />
        <Area 
          type="monotone" 
          dataKey="pendingTasks" 
          stroke="#f59e0b" 
          fill="#f59e0b" 
          stackId="2"
        />
      </AreaChart>
    </ResponsiveContainer>
  );
};

export default TaskProgressChart;
