import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Card, Icon, Textarea } from '@/shared/components/common';

import { KanbanCard } from '../types/kanban.types';

export interface KanbanAddCardProps {
  /**
   * Callback khi thêm thẻ mới
   */
  onAdd: (card: Partial<KanbanCard>) => void;

  /**
   * Callback khi hủy thêm thẻ
   */
  onCancel: () => void;

  /**
   * Custom class name
   */
  className?: string;
}

/**
 * Component form thêm thẻ mới trong Kanban Board
 */
const KanbanAddCard: React.FC<KanbanAddCardProps> = ({ onAdd, onCancel, className = '' }) => {
  const { t } = useTranslation();
  const [title, setTitle] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Xử lý thêm thẻ mới
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!title.trim()) {return;}

    setIsSubmitting(true);

    const newCard: Partial<KanbanCard> = {
      title: title.trim(),
    };

    onAdd(newCard);
    setTitle('');
    setIsSubmitting(false);
  };

  return (
    <Card className={`p-3 bg-white dark:bg-gray-800 ${className}`}>
      <form onSubmit={handleSubmit}>
        <Textarea
          value={title}
          onChange={e => setTitle(e.target.value)}
          placeholder={t('kanban.cardTitlePlaceholder', 'Enter card title...')}
          className="w-full mb-2 text-sm"
          rows={2}
          autoFocus
        />

        <div className="flex justify-end space-x-2">
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={onCancel}
            disabled={isSubmitting}
          >
            {t('common.cancel', 'Cancel')}
          </Button>

          <Button
            type="submit"
            variant="primary"
            size="sm"
            disabled={!title.trim() || isSubmitting}
          >
            {isSubmitting ? (
              <>
                <Icon name="loading" size="sm" className="animate-spin mr-1" />
                {t('common.adding', 'Adding...')}
              </>
            ) : (
              t('common.add', 'Add')
            )}
          </Button>
        </div>
      </form>
    </Card>
  );
};

export default KanbanAddCard;
