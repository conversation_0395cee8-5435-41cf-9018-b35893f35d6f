import { apiClient } from '@/shared/api/axios';
import type {
  ContractSignature,
  SignatureQueryParams,
  SignatureCreateDto,
  SignatureUpdateDto,
  SignatureSignDto,
  SignatureDeclineDto,
  SignatureDelegateDto,
  SignatureListResponse,
  SignatureValidationResult,
  SignatureProgress,
} from '../types/contract-signature.types';

/**
 * Contract Signature API Service
 */
export class ContractSignatureService {
  private static readonly BASE_URL = '/api/contracts/signatures';

  /**
   * Get signatures with pagination and filtering
   */
  static async getSignatures(params?: SignatureQueryParams): Promise<SignatureListResponse> {
    const response = await apiClient.get(this.BASE_URL, { params });
    return response.data;
  }

  /**
   * Get signature by ID
   */
  static async getSignature(id: string): Promise<ContractSignature> {
    const response = await apiClient.get(`${this.BASE_URL}/${id}`);
    return response.data;
  }

  /**
   * Get signatures for a contract
   */
  static async getContractSignatures(contractId: string): Promise<ContractSignature[]> {
    const response = await apiClient.get(`/api/contracts/${contractId}/signatures`);
    return response.data;
  }

  /**
   * Create new signature request
   */
  static async createSignature(data: SignatureCreateDto): Promise<ContractSignature> {
    const response = await apiClient.post(this.BASE_URL, data);
    return response.data;
  }

  /**
   * Update signature
   */
  static async updateSignature(id: string, data: SignatureUpdateDto): Promise<ContractSignature> {
    const response = await apiClient.put(`${this.BASE_URL}/${id}`, data);
    return response.data;
  }

  /**
   * Delete signature
   */
  static async deleteSignature(id: string): Promise<void> {
    await apiClient.delete(`${this.BASE_URL}/${id}`);
  }

  /**
   * Sign contract
   */
  static async signContract(id: string, data: SignatureSignDto): Promise<ContractSignature> {
    const response = await apiClient.post(`${this.BASE_URL}/${id}/sign`, data);
    return response.data;
  }

  /**
   * Decline to sign contract
   */
  static async declineSignature(id: string, data: SignatureDeclineDto): Promise<ContractSignature> {
    const response = await apiClient.post(`${this.BASE_URL}/${id}/decline`, data);
    return response.data;
  }

  /**
   * Delegate signature
   */
  static async delegateSignature(id: string, data: SignatureDelegateDto): Promise<ContractSignature> {
    const response = await apiClient.post(`${this.BASE_URL}/${id}/delegate`, data);
    return response.data;
  }

  /**
   * Send signature reminder
   */
  static async sendReminder(id: string, message?: string): Promise<void> {
    await apiClient.post(`${this.BASE_URL}/${id}/remind`, { message });
  }

  /**
   * Resend signature request
   */
  static async resendSignatureRequest(id: string): Promise<ContractSignature> {
    const response = await apiClient.post(`${this.BASE_URL}/${id}/resend`);
    return response.data;
  }

  /**
   * Cancel signature request
   */
  static async cancelSignatureRequest(id: string, reason?: string): Promise<ContractSignature> {
    const response = await apiClient.post(`${this.BASE_URL}/${id}/cancel`, { reason });
    return response.data;
  }

  /**
   * Validate signature
   */
  static async validateSignature(id: string): Promise<SignatureValidationResult> {
    const response = await apiClient.get(`${this.BASE_URL}/${id}/validate`);
    return response.data;
  }

  /**
   * Get signature progress for a contract
   */
  static async getSignatureProgress(contractId: string): Promise<SignatureProgress> {
    const response = await apiClient.get(`/api/contracts/${contractId}/signatures/progress`);
    return response.data;
  }

  /**
   * Get signature certificate
   */
  static async getSignatureCertificate(id: string): Promise<Blob> {
    const response = await apiClient.get(`${this.BASE_URL}/${id}/certificate`, {
      responseType: 'blob',
    });
    return response.data;
  }

  /**
   * Download signed document
   */
  static async downloadSignedDocument(contractId: string): Promise<Blob> {
    const response = await apiClient.get(`/api/contracts/${contractId}/signed-document`, {
      responseType: 'blob',
    });
    return response.data;
  }

  /**
   * Get signature audit trail
   */
  static async getSignatureAuditTrail(id: string) {
    const response = await apiClient.get(`${this.BASE_URL}/${id}/audit-trail`);
    return response.data;
  }

  /**
   * Verify signature integrity
   */
  static async verifySignatureIntegrity(id: string): Promise<boolean> {
    const response = await apiClient.get(`${this.BASE_URL}/${id}/verify`);
    return response.data.isValid;
  }

  /**
   * Get pending signatures for user
   */
  static async getPendingSignatures(userId?: string) {
    const response = await apiClient.get(`${this.BASE_URL}/pending`, {
      params: { userId },
    });
    return response.data;
  }

  /**
   * Get signature statistics
   */
  static async getSignatureStats() {
    const response = await apiClient.get(`${this.BASE_URL}/stats`);
    return response.data;
  }

  /**
   * Bulk send signature requests
   */
  static async bulkSendSignatureRequests(contractIds: string[], message?: string) {
    const response = await apiClient.post(`${this.BASE_URL}/bulk-send`, {
      contractIds,
      message,
    });
    return response.data;
  }

  /**
   * Bulk cancel signature requests
   */
  static async bulkCancelSignatureRequests(signatureIds: string[], reason?: string) {
    const response = await apiClient.post(`${this.BASE_URL}/bulk-cancel`, {
      signatureIds,
      reason,
    });
    return response.data;
  }

  /**
   * Get signature templates
   */
  static async getSignatureTemplates() {
    const response = await apiClient.get(`${this.BASE_URL}/templates`);
    return response.data;
  }

  /**
   * Create signature template
   */
  static async createSignatureTemplate(data: any) {
    const response = await apiClient.post(`${this.BASE_URL}/templates`, data);
    return response.data;
  }

  /**
   * Get external signature providers
   */
  static async getExternalProviders() {
    const response = await apiClient.get(`${this.BASE_URL}/providers`);
    return response.data;
  }

  /**
   * Send to external signature provider
   */
  static async sendToExternalProvider(contractId: string, provider: string, config: any) {
    const response = await apiClient.post(`/api/contracts/${contractId}/external-signature`, {
      provider,
      config,
    });
    return response.data;
  }

  /**
   * Get signature status from external provider
   */
  static async getExternalSignatureStatus(contractId: string, externalId: string) {
    const response = await apiClient.get(`/api/contracts/${contractId}/external-signature/${externalId}/status`);
    return response.data;
  }
}
