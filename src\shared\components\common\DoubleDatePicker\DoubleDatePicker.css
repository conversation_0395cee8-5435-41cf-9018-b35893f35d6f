/* DoubleDatePicker specific styles */
.double-datepicker-container {
  display: flex;
  position: relative;
}

.double-datepicker-left {
  position: relative;
}

.double-datepicker-left > div {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
  border-right: none !important;
}

.double-datepicker-right {
  position: relative;
}

.double-datepicker-right > div {
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
  border-left: none !important;
}

/* Ensure seamless connection */
.double-datepicker-left::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 1px;
  background: transparent;
  z-index: 1;
}

/* Remove any shadow between calendars */
.double-datepicker-left > div,
.double-datepicker-right > div {
  box-shadow: none !important;
}

/* Apply shadow only to the outer container */
.double-datepicker-container {
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  border-radius: 0.5rem;
  overflow: hidden;
}

/* Dark mode adjustments */
.dark .double-datepicker-container {
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.2);
}
