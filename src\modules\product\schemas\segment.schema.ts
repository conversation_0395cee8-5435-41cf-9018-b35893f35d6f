import { z } from 'zod';

import { SegmentStatus, SegmentType, ConditionType } from '../types/segment.types';

/**
 * Schema validation cho điều kiện phân đoạn
 */
export const segmentConditionSchema = z.object({
  field: z.string().min(1, 'Trường là bắt buộc'),
  type: z.nativeEnum(ConditionType, {
    errorMap: () => ({ message: 'Loại điều kiện là bắt buộc' }),
  }),
  value: z.union([
    z.string(),
    z.number(),
    z.array(z.union([z.string(), z.number()])),
  ]),
});

/**
 * Schema validation cho nhóm điều kiện phân đoạn
 */
export const segmentConditionGroupSchema: z.ZodType<any> = z.lazy(() =>
  z.object({
    operator: z.enum(['AND', 'OR']),
    conditions: z.array(segmentConditionSchema),
    groups: z.array(segmentConditionGroupSchema).optional(),
  })
);

/**
 * Schema validation cho phân đoạn
 */
export const segmentSchema = z.object({
  name: z
    .string()
    .min(2, 'Tên phân đoạn phải có ít nhất 2 ký tự')
    .max(100, 'Tên phân đoạn không được vượt quá 100 ký tự'),
  description: z
    .string()
    .max(500, 'Mô tả không được vượt quá 500 ký tự')
    .optional()
    .or(z.literal('')),
  type: z.nativeEnum(SegmentType, {
    errorMap: () => ({ message: 'Loại phân đoạn là bắt buộc' }),
  }),
  status: z.nativeEnum(SegmentStatus).default(SegmentStatus.DRAFT),
  conditions: segmentConditionGroupSchema,
});

/**
 * Schema validation cho cập nhật phân đoạn
 */
export const updateSegmentSchema = segmentSchema.partial().extend({
  id: z.string().min(1, 'ID phân đoạn là bắt buộc'),
});

/**
 * Type cho form tạo phân đoạn
 */
export type SegmentFormValues = z.infer<typeof segmentSchema>;

/**
 * Type cho form cập nhật phân đoạn
 */
export type UpdateSegmentFormValues = z.infer<typeof updateSegmentSchema>;
