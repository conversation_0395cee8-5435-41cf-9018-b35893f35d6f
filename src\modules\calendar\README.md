# Calendar Module

Module này cung cấp giao diện lịch đầy đủ tính năng sử dụng thư viện FullCalendar. Hỗ trợ nhiều chế độ xem khác nhau (tháng, tuần, ngà<PERSON>, danh s<PERSON>ch) và các tính năng quản lý sự kiện.

## Tính năng

- Nhiều chế độ xem lịch (tháng, tuần, ngày, danh sách)
- <PERSON><PERSON><PERSON>, chỉnh sửa và xóa sự kiện
- Quản lý sự kiện bằng kéo thả
- Thiết kế responsive với hỗ trợ chế độ sáng/tối
- Hỗ trợ đa ngôn ngữ
- Các component dễ tái sử dụng
- Hook quản lý state và logic

## Cấu trúc thư mục

```
src/modules/calendar/
├── components/           # Các component lịch
│   ├── core/             # Component cốt lõi
│   │   └── CalendarWrapper.tsx  # Component wrapper chính
│   ├── events/           # Component liên quan đến sự kiện
│   │   ├── EventBadge.tsx       # Badge hiển thị loại sự kiện
│   │   ├── EventCard.tsx        # Card hiển thị sự kiện
│   │   └── EventForm.tsx        # Form thêm/sửa sự kiện
│   ├── ui/               # Component UI phụ trợ
│   │   ├── CalendarHeader.tsx   # Header của calendar
│   │   └── CalendarToolbar.tsx  # Thanh công cụ của calendar
│   ├── Calendar.tsx      # Component lịch cũ (legacy)
│   └── ViewSelect.tsx    # Component chọn chế độ xem
├── hooks/                # Custom hooks
│   └── useCalendar.ts    # Hook quản lý state và logic
├── pages/                # Các trang lịch
│   └── CalendarPage.tsx  # Trang lịch chính
├── styles/               # Styles cho lịch
│   └── calendar.css      # CSS cho lịch
├── types.ts              # Định nghĩa kiểu dữ liệu
├── calendarRoutes.tsx    # Cấu hình route
├── index.ts              # Export module
└── README.md             # Tài liệu
```

## Cách sử dụng

### 1. Component CalendarWrapper

Component chính với đầy đủ tính năng:

```tsx
import { CalendarWrapper } from '@/modules/calendar';

const MyComponent = () => {
  const events = [
    {
      id: '1',
      title: 'Cuộc họp',
      start: '2023-01-01T10:00:00',
      end: '2023-01-01T12:00:00',
      extendedProps: { type: 'meeting' },
    },
    {
      id: '2',
      title: 'Ăn trưa',
      start: '2023-01-02T12:30:00',
      end: '2023-01-02T13:30:00',
      extendedProps: { type: 'lunch' },
    },
  ];

  return (
    <CalendarWrapper
      events={events}
      initialView="dayGridMonth"
      editable={true}
      selectable={true}
      onEventClick={info => console.log('Event clicked:', info.event.title)}
      onAddEvent={event => saveEventToAPI(event)}
    />
  );
};
```

### 2. Sử dụng useCalendar hook

Hook quản lý state và logic cho calendar:

```tsx
import { useCalendar, CalendarHeader, EventCard } from '@/modules/calendar';

const MyCalendarComponent = () => {
  const {
    events,
    currentView,
    calendarRef,
    calendarTitle,
    handleDateSelect,
    handleEventClick,
    handleViewChange,
    handlePrev,
    handleNext,
    handleToday,
    addEvent,
    updateEvent,
    deleteEvent,
  } = useCalendar({
    initialEvents: myEvents,
    onAddEvent: event => saveEventToAPI(event),
  });

  return (
    <div>
      <CalendarHeader
        title={calendarTitle}
        currentView={currentView}
        onPrev={handlePrev}
        onNext={handleNext}
        onToday={handleToday}
        onViewChange={handleViewChange}
        onAddEvent={() => {
          /* Xử lý thêm sự kiện */
        }}
      />

      {/* Hiển thị FullCalendar hoặc danh sách sự kiện */}
      {selectedEvent && <EventCard event={selectedEvent} detailed />}
    </div>
  );
};
```

### 3. Sử dụng các component riêng lẻ

```tsx
import { EventBadge, EventCard } from '@/modules/calendar';

const EventList = ({ events }) => {
  return (
    <div className="event-list">
      <h2>Sự kiện sắp tới</h2>
      {events.map(event => (
        <div key={event.id} className="event-item">
          <EventBadge type={event.extendedProps?.type || 'default'} showLabel />
          <EventCard event={event} />
        </div>
      ))}
    </div>
  );
};
```

### 4. Trang Calendar

Module này bao gồm trang lịch đã được cấu hình sẵn với đầy đủ tính năng quản lý sự kiện. Bạn có thể truy cập tại route `/calendar`.

## Tùy chỉnh

Các component calendar chấp nhận nhiều props để tùy chỉnh:

### CalendarWrapper Props

| Prop          | Kiểu dữ liệu                                                      | Mô tả                                            |
| ------------- | ----------------------------------------------------------------- | ------------------------------------------------ |
| `events`      | `CalendarEvent[]`                                                 | Mảng các sự kiện hiển thị                        |
| `initialDate` | `Date`                                                            | Ngày ban đầu để hiển thị                         |
| `initialView` | `'dayGridMonth' \| 'timeGridWeek' \| 'timeGridDay' \| 'listWeek'` | Chế độ xem ban đầu                               |
| `weekends`    | `boolean`                                                         | Hiển thị ngày cuối tuần hay không                |
| `editable`    | `boolean`                                                         | Cho phép chỉnh sửa sự kiện hay không             |
| `selectable`  | `boolean`                                                         | Cho phép chọn ngày hay không                     |
| `allDaySlot`  | `boolean`                                                         | Hiển thị slot cả ngày trong chế độ xem thời gian |
| `height`      | `string \| number`                                                | Chiều cao của calendar                           |
| `className`   | `string`                                                          | CSS class bổ sung                                |

### useCalendar Options

| Option          | Kiểu dữ liệu                                                      | Mô tả                         |
| --------------- | ----------------------------------------------------------------- | ----------------------------- |
| `initialEvents` | `CalendarEvent[]`                                                 | Sự kiện ban đầu               |
| `initialView`   | `'dayGridMonth' \| 'timeGridWeek' \| 'timeGridDay' \| 'listWeek'` | Chế độ xem ban đầu            |
| `onAddEvent`    | `(event: CalendarEvent) => void`                                  | Callback khi thêm sự kiện     |
| `onUpdateEvent` | `(event: CalendarEvent) => void`                                  | Callback khi cập nhật sự kiện |
| `onDeleteEvent` | `(eventId: string) => void`                                       | Callback khi xóa sự kiện      |

## Xử lý sự kiện

Các component calendar cung cấp nhiều callback props để xử lý sự kiện:

- `onDateSelect`: Được gọi khi chọn một khoảng thời gian
- `onEventClick`: Được gọi khi click vào một sự kiện
- `onEventChange`: Được gọi khi một sự kiện được thay đổi (kéo, thay đổi kích thước)
- `onAddEvent`: Được gọi để thêm một sự kiện mới
- `onUpdateEvent`: Được gọi để cập nhật một sự kiện
- `onDeleteEvent`: Được gọi để xóa một sự kiện

## Styling

Calendar bao gồm các style tùy chỉnh cho cả chế độ sáng và tối. Bạn có thể tùy chỉnh giao diện bằng cách sửa đổi các biến CSS trong `calendar.css`.

## Các component dễ tái sử dụng

Module calendar cung cấp các component dễ tái sử dụng:

- `CalendarWrapper`: Component wrapper chính với đầy đủ tính năng
- `CalendarHeader`: Header của calendar với các nút điều hướng
- `CalendarToolbar`: Thanh công cụ với các tùy chọn lọc
- `EventBadge`: Badge hiển thị loại sự kiện
- `EventCard`: Card hiển thị thông tin sự kiện
- `useCalendar`: Hook quản lý state và logic cho calendar
