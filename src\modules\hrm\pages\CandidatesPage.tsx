/**
 * Trang quản lý ứng viên
 */
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { Button, Card, Table, Typography } from '@/shared/components/common';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { useCandidates } from '../hooks/useRecruitment';
import { Candidate, CandidateStatus } from '../types/recruitment.types';

/**
 * Component trang quản lý ứng viên
 */
const CandidatesPage: React.FC = () => {
  const { t } = useTranslation(['common', 'hrm']);

  // State cho filters
  const [filters, setFilters] = useState({
    status: '',
    jobPositionId: '',
    rating: '',
  });

  // Định nghĩa columns cho table
  const columns = useMemo(
    () => [
      {
        title: t('hrm:recruitment.candidate.name', 'Họ tên'),
        dataIndex: 'firstName',
        key: 'name',
        sortable: true,
        render: (value: string, record: Candidate) => (
          <div>
            <Typography variant="body1" className="font-medium">
              {`${record.firstName} ${record.lastName}`}
            </Typography>
            <Typography variant="body2" className="text-muted-foreground">
              {record.email}
            </Typography>
          </div>
        ),
      },
      {
        title: t('hrm:recruitment.candidate.jobPosition', 'Vị trí ứng tuyển'),
        dataIndex: 'jobPositionTitle',
        key: 'jobPositionTitle',
        render: (value: string) => (
          <Typography variant="body2">
            {value || t('hrm:recruitment.candidate.noPosition', 'Chưa xác định')}
          </Typography>
        ),
      },
      {
        title: t('hrm:recruitment.candidate.status', 'Trạng thái'),
        dataIndex: 'status',
        key: 'status',
        render: (value: CandidateStatus) => {
          const statusConfig = {
            [CandidateStatus.NEW]: {
              label: t('hrm:recruitment.candidateStatus.new', 'Mới'),
              color: 'blue',
            },
            [CandidateStatus.SCREENING]: {
              label: t('hrm:recruitment.candidateStatus.screening', 'Sàng lọc'),
              color: 'yellow',
            },
            [CandidateStatus.INTERVIEW]: {
              label: t('hrm:recruitment.candidateStatus.interview', 'Phỏng vấn'),
              color: 'purple',
            },
            [CandidateStatus.TECHNICAL_TEST]: {
              label: t('hrm:recruitment.candidateStatus.technicalTest', 'Kiểm tra kỹ thuật'),
              color: 'orange',
            },
            [CandidateStatus.FINAL_INTERVIEW]: {
              label: t('hrm:recruitment.candidateStatus.finalInterview', 'Phỏng vấn cuối'),
              color: 'indigo',
            },
            [CandidateStatus.OFFER]: {
              label: t('hrm:recruitment.candidateStatus.offer', 'Đề nghị'),
              color: 'green',
            },
            [CandidateStatus.HIRED]: {
              label: t('hrm:recruitment.candidateStatus.hired', 'Đã tuyển'),
              color: 'green',
            },
            [CandidateStatus.REJECTED]: {
              label: t('hrm:recruitment.candidateStatus.rejected', 'Từ chối'),
              color: 'red',
            },
            [CandidateStatus.WITHDRAWN]: {
              label: t('hrm:recruitment.candidateStatus.withdrawn', 'Rút lui'),
              color: 'gray',
            },
          };
          const config = statusConfig[value];
          return (
            <span
              className={`px-2 py-1 bg-${config.color}-100 text-${config.color}-800 rounded-full text-xs`}
            >
              {config.label}
            </span>
          );
        },
      },
      {
        title: t('hrm:recruitment.candidate.rating', 'Đánh giá'),
        dataIndex: 'rating',
        key: 'rating',
        sortable: true,
        render: (value: number) => {
          if (!value) return <span className="text-muted-foreground">-</span>;
          return (
            <div className="flex items-center gap-1">
              {[1, 2, 3, 4, 5].map(star => (
                <span
                  key={star}
                  className={`text-sm ${star <= value ? 'text-yellow-400' : 'text-gray-300'}`}
                >
                  ★
                </span>
              ))}
              <span className="ml-1 text-sm text-muted-foreground">({value}/5)</span>
            </div>
          );
        },
      },
      {
        title: t('hrm:recruitment.candidate.phone', 'Điện thoại'),
        dataIndex: 'phone',
        key: 'phone',
      },
      {
        title: t('hrm:recruitment.candidate.appliedAt', 'Ngày ứng tuyển'),
        dataIndex: 'appliedAt',
        key: 'appliedAt',
        sortable: true,
        render: (value: string) => new Date(value).toLocaleDateString('vi-VN'),
      },
      {
        title: t('hrm:recruitment.candidate.resume', 'CV'),
        dataIndex: 'resumeUrl',
        key: 'resumeUrl',
        render: (value: string) => {
          if (!value) return <span className="text-muted-foreground">-</span>;
          return (
            <Button variant="outline" size="sm" onClick={() => window.open(value, '_blank')}>
              {t('hrm:recruitment.candidate.viewResume', 'Xem CV')}
            </Button>
          );
        },
      },
      {
        title: t('common:actions', 'Thao tác'),
        key: 'actions',
        render: (record: Candidate) => (
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              {t('common:edit', 'Sửa')}
            </Button>
            <Button variant="outline" size="sm">
              {t('common:view', 'Xem')}
            </Button>
          </div>
        ),
      },
    ],
    [t]
  );

  // Setup data table
  const dataTable = useDataTable(useDataTableConfig({ columns }));

  // Fetch data
  const { data, isLoading } = useCandidates({
    ...dataTable.queryParams,
    ...filters,
  });

  // Menu items cho filter
  const filterMenuItems = [
    {
      label: t('hrm:recruitment.filter.candidateStatus', 'Lọc theo trạng thái'),
      items: [
        { label: t('common:all', 'Tất cả'), value: '' },
        { label: t('hrm:recruitment.candidateStatus.new', 'Mới'), value: CandidateStatus.NEW },
        {
          label: t('hrm:recruitment.candidateStatus.screening', 'Sàng lọc'),
          value: CandidateStatus.SCREENING,
        },
        {
          label: t('hrm:recruitment.candidateStatus.interview', 'Phỏng vấn'),
          value: CandidateStatus.INTERVIEW,
        },
        {
          label: t('hrm:recruitment.candidateStatus.offer', 'Đề nghị'),
          value: CandidateStatus.OFFER,
        },
        {
          label: t('hrm:recruitment.candidateStatus.hired', 'Đã tuyển'),
          value: CandidateStatus.HIRED,
        },
        {
          label: t('hrm:recruitment.candidateStatus.rejected', 'Từ chối'),
          value: CandidateStatus.REJECTED,
        },
      ],
    },
  ];

  return (
    <div className="w-full bg-background text-foreground">
      {/* Header */}
      <div className="mb-6">
        <Typography variant="h1" className="mb-2">
          {t('hrm:recruitment.candidates.title', 'Quản lý ứng viên')}
        </Typography>
        <Typography variant="body1" className="text-muted-foreground">
          {t('hrm:recruitment.candidates.description', 'Quản lý hồ sơ và tiến trình ứng viên')}
        </Typography>
      </div>

      {/* Content */}
      <Card>
        {/* Menu Bar */}
        <MenuIconBar
          onSearch={dataTable.handleSearch}
          onAdd={() => console.log('Add new candidate')}
          items={[]}
          onColumnVisibilityChange={dataTable.columnVisibility.setColumns}
          columns={dataTable.columnVisibility.columns}
          showDateFilter={false}
          showColumnFilter={true}
        />

        {/* Table */}
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={data?.items || []}
          loading={isLoading}
          pagination={{
            current: data?.page || 1,
            pageSize: data?.limit || 10,
            total: data?.total || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              t('common:pagination.showTotal', `Hiển thị {{start}}-{{end}} của {{total}} mục`, {
                start: range[0],
                end: range[1],
                total,
              }),
            onChange: dataTable.pagination.handlePageChange,
            onShowSizeChange: dataTable.pagination.handlePageSizeChange,
          }}
        />
      </Card>
    </div>
  );
};

export default CandidatesPage;
