import React, { useState } from 'react';
import { YearMonthSelectorProps } from './types';
import { getMonthNames, changeMonth, changeYear, getYear, getMonth } from './utils';
import { useTheme } from '@/shared/contexts/theme';
import { useTranslation } from 'react-i18next';
import { Icon } from '@/shared/components/common';

/**
 * Component cho phép chọn tháng và năm
 */
const YearMonthSelector: React.FC<YearMonthSelectorProps> = ({
  month,
  onMonthChange,
  monthNames: propMonthNames,
  className = '',
  minDate,
  maxDate,
}) => {
  const { t, i18n } = useTranslation();
  useTheme(); // Sử dụng hook theme mới

  // State để theo dõi mode hiển thị (month/year)
  const [mode, setMode] = useState<'month' | 'year'>('month');

  // State để theo dõi năm hiển thị khi ở chế độ chọn năm
  const [yearDecade, setYearDecade] = useState(() => {
    const currentYear = getYear(month);
    return Math.floor(currentYear / 10) * 10;
  });

  // Lấy tháng và năm hiện tại
  const currentMonth = getMonth(month);
  const currentYear = getYear(month);

  // Lấy danh sách tháng
  const monthNames = propMonthNames || getMonthNames(i18n.language, 'short');

  // Xử lý chọn tháng
  const handleSelectMonth = (monthIndex: number) => {
    const newDate = changeMonth(month, monthIndex);
    onMonthChange(newDate);
  };

  // Xử lý chọn năm
  const handleSelectYear = (year: number) => {
    const newDate = changeYear(month, year);
    onMonthChange(newDate);
    setMode('month');
  };

  // Xử lý chuyển đổi giữa chọn tháng và chọn năm
  const handleToggleMode = () => {
    setMode(mode === 'month' ? 'year' : 'month');
  };

  // Xử lý chuyển thập kỷ trước
  const handlePrevDecade = () => {
    setYearDecade(yearDecade - 10);
  };

  // Xử lý chuyển thập kỷ sau
  const handleNextDecade = () => {
    setYearDecade(yearDecade + 10);
  };

  // Kiểm tra tháng có bị disabled không
  const isMonthDisabled = (monthIndex: number): boolean => {
    if (!minDate && !maxDate) return false;

    // Não estamos usando monthDate, mas podemos criar para verificação
    // const monthDate = changeMonth(changeYear(month, currentYear), monthIndex);

    if (minDate) {
      const minYear = getYear(minDate);
      const minMonth = getMonth(minDate);

      if (currentYear < minYear || (currentYear === minYear && monthIndex < minMonth)) {
        return true;
      }
    }

    if (maxDate) {
      const maxYear = getYear(maxDate);
      const maxMonth = getMonth(maxDate);

      if (currentYear > maxYear || (currentYear === maxYear && monthIndex > maxMonth)) {
        return true;
      }
    }

    return false;
  };

  // Kiểm tra năm có bị disabled không
  const isYearDisabled = (year: number): boolean => {
    if (!minDate && !maxDate) return false;

    if (minDate && year < getYear(minDate)) {
      return true;
    }

    if (maxDate && year > getYear(maxDate)) {
      return true;
    }

    return false;
  };

  // Base classes
  const baseClasses = 'p-2';

  // Combine all classes
  const selectorClasses = [baseClasses, className].join(' ');

  return (
    <div className={selectorClasses}>
      {/* Header */}
      <div className="flex items-center justify-between mb-2">
        {mode === 'month' ? (
          <>
            <button
              type="button"
              className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
              onClick={handleToggleMode}
              aria-label={t('datepicker.selectYear', 'Select year')}
            >
              <span className="font-medium">{currentYear}</span>
            </button>
            <div></div> {/* Spacer */}
          </>
        ) : (
          <>
            <button
              type="button"
              className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
              onClick={handlePrevDecade}
              aria-label={t('datepicker.previousDecade', 'Previous decade')}
            >
              <Icon name="chevron-left" size="sm" />
            </button>
            <button
              type="button"
              className="px-2 py-1 rounded hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors font-medium"
              onClick={handleToggleMode}
            >
              {yearDecade} - {yearDecade + 9}
            </button>
            <button
              type="button"
              className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
              onClick={handleNextDecade}
              aria-label={t('datepicker.nextDecade', 'Next decade')}
            >
              <Icon name="chevron-right" size="sm" />
            </button>
          </>
        )}
      </div>

      {/* Grid tháng */}
      {mode === 'month' && (
        <div className="grid grid-cols-3 gap-2">
          {monthNames.map((name, index) => {
            const isSelected = index === currentMonth;
            const disabled = isMonthDisabled(index);

            return (
              <button
                key={index}
                type="button"
                className={`
                  py-2 rounded text-sm transition-colors
                  ${isSelected ? 'bg-primary text-white dark:bg-primary-light dark:text-dark font-medium' : 'hover:bg-gray-100 dark:hover:bg-gray-800'}
                  ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                `}
                onClick={() => !disabled && handleSelectMonth(index)}
                disabled={disabled}
                aria-selected={isSelected}
                aria-disabled={disabled}
              >
                {name}
              </button>
            );
          })}
        </div>
      )}

      {/* Grid năm */}
      {mode === 'year' && (
        <div className="grid grid-cols-3 gap-2">
          {Array.from({ length: 12 }, (_, i) => {
            const year = yearDecade + i - 1;
            const isSelected = year === currentYear;
            const disabled = isYearDisabled(year);

            return (
              <button
                key={i}
                type="button"
                className={`
                  py-2 rounded text-sm transition-colors
                  ${isSelected ? 'bg-primary text-white dark:bg-primary-light dark:text-dark font-medium' : 'hover:bg-gray-100 dark:hover:bg-gray-800'}
                  ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                  ${i === 0 || i === 11 ? 'text-gray-500 dark:text-gray-400' : ''}
                `}
                onClick={() => !disabled && handleSelectYear(year)}
                disabled={disabled}
                aria-selected={isSelected}
                aria-disabled={disabled}
              >
                {year}
              </button>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default YearMonthSelector;
