import React from 'react';
import { useTranslation } from 'react-i18next';

import { Card, Typography } from '@/shared/components/common';

/**
 * Contract Viewer Component
 * Component xem nội dung hợp đồng
 */
const ContractViewer: React.FC = () => {
  const { t } = useTranslation(['common', 'contracts']);

  return (
    <Card className="p-6">
      <Typography variant="h2" className="mb-4">
        {t('contracts:view')}
      </Typography>
      <div className="text-center py-8">
        <Typography variant="body1" className="text-muted-foreground">
          Trình xem hợp đồng đang được phát triển...
        </Typography>
      </div>
    </Card>
  );
};

export default ContractViewer;
