import React from 'react';
import { useTranslation } from 'react-i18next';

import { Card, Typography } from '@/shared/components/common';

/**
 * Approval Workflow Component
 * Component quy trình phê duyệt hợp đồng
 */
const ApprovalWorkflow: React.FC = () => {
  const { t } = useTranslation(['common', 'contracts']);

  return (
    <Card className="p-6">
      <Typography variant="h2" className="mb-4">
        {t('contracts:approval_workflow')}
      </Typography>
      <div className="text-center py-8">
        <Typography variant="body1" className="text-muted-foreground">
          Quy trình phê duyệt đang được phát triển...
        </Typography>
      </div>
    </Card>
  );
};

export default ApprovalWorkflow;
