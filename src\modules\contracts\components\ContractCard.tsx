import React from 'react';
import { useTranslation } from 'react-i18next';

import { Card, Typography, Badge } from '@/shared/components/common';
import type { Contract } from '../types/contract.types';

interface ContractCardProps {
  contract: Contract;
  onClick?: () => void;
  className?: string;
}

/**
 * Contract Card Component
 * Component hiển thị thông tin hợp đồng dạng card
 */
const ContractCard: React.FC<ContractCardProps> = ({
  contract,
  onClick,
  className = '',
}) => {
  const { t } = useTranslation(['common', 'contracts']);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'pending_signature':
        return 'warning';
      case 'pending_approval':
        return 'info';
      case 'expired':
        return 'error';
      case 'cancelled':
        return 'secondary';
      default:
        return 'default';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'error';
      case 'high':
        return 'warning';
      case 'medium':
        return 'info';
      case 'low':
        return 'secondary';
      default:
        return 'default';
    }
  };

  const formatCurrency = (value?: number, currency?: string) => {
    if (!value) return '';
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: currency || 'VND',
    }).format(value);
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('vi-VN');
  };

  return (
    <Card 
      className={`p-4 hover:shadow-md transition-shadow cursor-pointer ${className}`}
      onClick={onClick}
    >
      {/* Header */}
      <div className="flex justify-between items-start mb-3">
        <div className="flex-1">
          <Typography variant="h3" className="text-lg font-semibold mb-1">
            {contract.title}
          </Typography>
          <Typography variant="body2" className="text-muted-foreground">
            {contract.contractNumber}
          </Typography>
        </div>
        <div className="flex flex-col items-end space-y-1">
          <Badge variant={getStatusColor(contract.status)}>
            {t(`contracts:status_${contract.status}`)}
          </Badge>
          <Badge variant={getPriorityColor(contract.priority)} size="sm">
            {t(`contracts:priority_${contract.priority}`)}
          </Badge>
        </div>
      </div>

      {/* Description */}
      {contract.description && (
        <Typography variant="body2" className="text-muted-foreground mb-3 line-clamp-2">
          {contract.description}
        </Typography>
      )}

      {/* Contract Info */}
      <div className="space-y-2 mb-3">
        <div className="flex justify-between items-center">
          <Typography variant="body2" className="text-muted-foreground">
            {t('contracts:contract_type')}:
          </Typography>
          <Typography variant="body2" className="font-medium">
            {t(`contracts:type_${contract.type}`)}
          </Typography>
        </div>

        {contract.value && (
          <div className="flex justify-between items-center">
            <Typography variant="body2" className="text-muted-foreground">
              {t('contracts:contract_value')}:
            </Typography>
            <Typography variant="body2" className="font-medium text-green-600">
              {formatCurrency(contract.value, contract.currency)}
            </Typography>
          </div>
        )}

        {contract.endDate && (
          <div className="flex justify-between items-center">
            <Typography variant="body2" className="text-muted-foreground">
              {t('contracts:end_date')}:
            </Typography>
            <Typography 
              variant="body2" 
              className={`font-medium ${contract.isNearExpiry ? 'text-orange-600' : ''}`}
            >
              {formatDate(contract.endDate)}
            </Typography>
          </div>
        )}
      </div>

      {/* Parties */}
      {contract.parties && (
        <div className="border-t pt-3">
          <Typography variant="body2" className="text-muted-foreground mb-2">
            {t('contracts:parties')}:
          </Typography>
          <div className="space-y-1">
            <Typography variant="body2" className="text-sm">
              <span className="font-medium">{t('contracts:party1')}:</span> {contract.parties.party1.name}
            </Typography>
            <Typography variant="body2" className="text-sm">
              <span className="font-medium">{t('contracts:party2')}:</span> {contract.parties.party2.name}
            </Typography>
          </div>
        </div>
      )}

      {/* Footer */}
      <div className="flex justify-between items-center mt-3 pt-3 border-t">
        <Typography variant="body2" className="text-muted-foreground text-xs">
          {t('contracts:created_at')}: {formatDate(contract.createdAt)}
        </Typography>
        
        {contract.isNearExpiry && (
          <div className="flex items-center space-x-1">
            <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
            <Typography variant="body2" className="text-orange-600 text-xs">
              {t('contracts:expiring_soon')}
            </Typography>
          </div>
        )}
      </div>
    </Card>
  );
};

export default ContractCard;
