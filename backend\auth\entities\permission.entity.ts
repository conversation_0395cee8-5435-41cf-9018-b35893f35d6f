import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

/**
 * Entity representing permissions in the system
 */
@Entity('permissions')
export class Permission {
  /**
   * Unique identifier for the permission
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * System module (user, project, task, etc.)
   */
  @Column({ type: 'varchar', length: 255, nullable: false })
  module: string;

  /**
   * Allowed action (view, create, update, delete, etc.)
   */
  @Column({ type: 'varchar', length: 255, nullable: false })
  action: string;

  /**
   * Detailed description of the permission
   */
  @Column({ type: 'text', nullable: true })
  description: string | null;

  /**
   * Creation timestamp (in milliseconds)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number | null;

  /**
   * ID of the company/organization that owns this record
   */
  @Column({ name: 'tenant_id', type: 'integer', nullable: true })
  tenantId: number | null;
}
