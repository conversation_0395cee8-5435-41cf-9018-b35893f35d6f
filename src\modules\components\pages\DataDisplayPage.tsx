import React, { useEffect, useState } from 'react';
import { FiCalendar, FiPackage, FiShoppingCart, FiUser } from 'react-icons/fi';

import { Card, Container, Typography } from '@/shared/components/common';
import { DataDisplay } from '@/shared/components/common/DataDisplay';
import { ViewMode } from '@/shared/components/common/DataDisplay/types';
import { TableColumn } from '@/shared/components/common/Table/types';

import ComponentDemo from '../components/ComponentDemo';

// Tạo dữ liệu mẫu
interface Product {
  id: number;
  name: string;
  category: string;
  price: number;
  stock: number;
  createdAt: string;
  status: 'in-stock' | 'low-stock' | 'out-of-stock';
}

const generateFakeProducts = (count: number, startIndex = 0): Product[] => {
  const categories = ['Điện tử', 'Quần áo', 'Đồ gia dụng', 'Thực phẩm', 'Sách'];
  const statuses: Array<'in-stock' | 'low-stock' | 'out-of-stock'> = [
    'in-stock',
    'low-stock',
    'out-of-stock',
  ];

  return Array.from({ length: count }).map((_, index) => {
    const productIndex = startIndex + index;
    return {
      id: productIndex + 1,
      name: `Sản phẩm ${productIndex + 1}`,
      category: categories[Math.floor(Math.random() * categories.length)],
      price: Math.floor(Math.random() * 10000000) + 10000,
      stock: Math.floor(Math.random() * 100),
      createdAt: new Date(Date.now() - Math.floor(Math.random() * 10000000000)).toISOString(),
      status: statuses[Math.floor(Math.random() * statuses.length)],
    };
  });
};

// Định nghĩa các cột cho bảng
const productColumns: TableColumn<Product>[] = [
  {
    key: 'id',
    title: 'ID',
    dataIndex: 'id',
    width: 60,
  },
  {
    key: 'name',
    title: 'Tên sản phẩm',
    dataIndex: 'name',
  },
  {
    key: 'category',
    title: 'Danh mục',
    dataIndex: 'category',
  },
  {
    key: 'price',
    title: 'Giá',
    dataIndex: 'price',
    render: (price: unknown) => `${(price as number).toLocaleString('vi-VN')} ₫`,
  },
  {
    key: 'stock',
    title: 'Tồn kho',
    dataIndex: 'stock',
  },
  {
    key: 'status',
    title: 'Trạng thái',
    dataIndex: 'status',
    render: (status: unknown) => {
      const statusConfig = {
        'in-stock': {
          label: 'Còn hàng',
          color: 'text-green-600 bg-green-100 dark:bg-green-900/30',
        },
        'low-stock': {
          label: 'Sắp hết',
          color: 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30',
        },
        'out-of-stock': { label: 'Hết hàng', color: 'text-red-600 bg-red-100 dark:bg-red-900/30' },
      };
      const statusKey = status as keyof typeof statusConfig;
      const config = statusConfig[statusKey];
      return <span className={`px-2 py-1 rounded text-xs ${config.color}`}>{config.label}</span>;
    },
  },
];

// Hàm render item tùy chỉnh
const renderProductCard = ({ item }: { item: Product; index: number }) => {
  const statusConfig = {
    'in-stock': { label: 'Còn hàng', color: 'text-green-600' },
    'low-stock': { label: 'Sắp hết', color: 'text-yellow-600' },
    'out-of-stock': { label: 'Hết hàng', color: 'text-red-600' },
  };

  // Lấy icon phù hợp với danh mục
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'Điện tử':
        return <FiPackage size={20} />;
      case 'Quần áo':
        return <FiUser size={20} />;
      case 'Đồ gia dụng':
        return <FiShoppingCart size={20} />;
      default:
        return <FiCalendar size={20} />;
    }
  };

  return (
    <div className="p-4 h-full flex flex-col">
      <div className="flex items-start justify-between mb-2">
        <div className="flex items-center">
          <div className="mr-2 text-primary">{getCategoryIcon(item.category)}</div>
          <Typography variant="h4" className="font-medium">
            {item.name}
          </Typography>
        </div>
        <span
          className={`px-2 py-1 rounded text-xs ${statusConfig[item.status as keyof typeof statusConfig].color}`}
        >
          {statusConfig[item.status as keyof typeof statusConfig].label}
        </span>
      </div>

      <div className="text-gray-600 dark:text-gray-300 mb-2">{item.category}</div>

      <div className="flex items-center justify-between mt-auto pt-4 border-t">
        <Typography variant="h5" className="font-semibold">
          {item.price.toLocaleString('vi-VN')} ₫
        </Typography>
        <div className="text-gray-600 dark:text-gray-300">
          <span className="text-sm">Tồn kho: {item.stock}</span>
        </div>
      </div>
    </div>
  );
};

/**
 * Trang giới thiệu DataDisplay component
 */
const DataDisplayPage: React.FC = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [viewMode, setViewMode] = useState<ViewMode>('table');
  const PAGE_SIZE = 12;

  // Tải dữ liệu ban đầu
  useEffect(() => {
    setProducts(generateFakeProducts(PAGE_SIZE));
  }, []);

  // Xử lý tải thêm dữ liệu
  const handleLoadMore = () => {
    if (loading) {return;}

    setLoading(true);

    // Giả lập API call
    setTimeout(() => {
      // Giới hạn ở 100 sản phẩm
      if (products.length >= 100) {
        setHasMore(false);
        setLoading(false);
        return;
      }

      const newProducts = generateFakeProducts(PAGE_SIZE, products.length);
      setProducts(prevProducts => [...prevProducts, ...newProducts]);
      setLoading(false);
    }, 1000);
  };

  return (
    <Container>
      <Typography variant="h1" className="mb-6">
        DataDisplay Component
      </Typography>

      <Card className="p-4 mb-8">
        <Typography variant="h2" className="mb-4">
          Giới thiệu
        </Typography>
        <Typography className="mb-2">
          Component <code>DataDisplay</code> là một component hiển thị dữ liệu đa năng, cho phép:
        </Typography>
        <ul className="list-disc pl-6 mb-4">
          <li>Chuyển đổi giữa chế độ hiển thị bảng và card</li>
          <li>Tự động tải thêm dữ liệu khi cuộn (infinite scroll) trong chế độ card</li>
          <li>
            Tùy chỉnh cách hiển thị dữ liệu với prop <code>renderItem</code>
          </li>
          <li>Thêm icon cho từng item</li>
        </ul>
      </Card>

      <Typography variant="h2" className="mb-4">
        Demo
      </Typography>

      <ComponentDemo title="DataDisplay - Chuyển đổi chế độ hiển thị" className="mb-8">
        <div className="mb-4">
          <Typography className="mb-2">
            Chế độ hiển thị hiện tại: <strong>{viewMode === 'table' ? 'Bảng' : 'Card'}</strong>
          </Typography>
          <Typography className="text-sm text-gray-600 dark:text-gray-400">
            (Bạn có thể chuyển đổi bằng cách nhấn vào các nút ở góc trên bên phải của component)
          </Typography>
        </div>

        <DataDisplay
          data={products}
          columns={productColumns}
          renderItem={renderProductCard}
          keyField="id"
          defaultViewMode={viewMode}
          onViewModeChange={setViewMode}
          onLoadMore={handleLoadMore}
          loading={loading}
          hasMore={hasMore}
          maxHeight="600px"
          gridColumns={3}
          gridGap={4}
        />
      </ComponentDemo>

      <Card className="p-4 mb-8">
        <Typography variant="h2" className="mb-4">
          Cách sử dụng
        </Typography>
        <div className="rounded-md bg-gray-100 dark:bg-gray-800 p-4 mb-4 overflow-x-auto">
          <pre className="text-sm text-gray-800 dark:text-gray-200">
            {`import { DataDisplay } from '@/shared/components/common/DataDisplay';

// Định nghĩa các cột cho chế độ bảng
const columns = [
  { key: 'id', title: 'ID', dataIndex: 'id' },
  { key: 'name', title: 'Tên', dataIndex: 'name' },
  // Các cột khác...
];

// Hàm render item tùy chỉnh cho chế độ card
const renderItem = ({ item }) => (
  <div>
    <h3>{item.name}</h3>
    <p>{item.description}</p>
  </div>
);

// Sử dụng component
<DataDisplay
  data={data}
  columns={columns}
  renderItem={renderItem}
  keyField="id"
  defaultViewMode="table"
  onViewModeChange={handleViewModeChange}
  onLoadMore={handleLoadMore}
  loading={loading}
  hasMore={hasMore}
  maxHeight="600px"
  gridColumns={3}
  gridGap={4}
/>`}
          </pre>
        </div>
      </Card>
    </Container>
  );
};

export default DataDisplayPage;
