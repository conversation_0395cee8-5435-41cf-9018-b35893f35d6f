/**
 * Link Message Component
 * Renders link previews with metadata
 */

import React, { useState } from 'react';
import { Typography, Button, Icon } from '@/shared/components/common';
import { LinkContentData } from '@/shared/websocket/types/chat-message.types';

// Props interface
export interface LinkMessageProps {
  data: LinkContentData;
  className?: string;
}

/**
 * Link Message Component
 */
const LinkMessage: React.FC<LinkMessageProps> = ({ data, className = '' }) => {
  const {
    url,
    title,
    description,
    image,
    domain,
    favicon,
  } = data;

  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);

  // Handle link click
  const handleLinkClick = () => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  // Handle image load
  const handleImageLoad = () => {
    setImageLoaded(true);
    setImageError(false);
  };

  // Handle image error
  const handleImageError = () => {
    setImageError(true);
    setImageLoaded(false);
  };

  // Get domain from URL if not provided
  const getDomain = () => {
    if (domain) return domain;
    
    try {
      const urlObj = new URL(url);
      return urlObj.hostname;
    } catch {
      return url;
    }
  };

  // Truncate text
  const truncateText = (text: string, maxLength: number) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  // Render favicon
  const renderFavicon = () => {
    if (favicon) {
      return (
        <img
          src={favicon}
          alt=""
          className="w-4 h-4 rounded"
          onError={(e) => {
            e.currentTarget.style.display = 'none';
          }}
        />
      );
    }
    
    return <Icon name="globe" size="sm" className="text-gray-400" />;
  };

  // Render preview image
  const renderPreviewImage = () => {
    if (!image) return null;

    return (
      <div className="relative overflow-hidden rounded-t-lg">
        {!imageLoaded && !imageError && (
          <div className="w-full h-48 bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
            <Icon name="image" size="lg" className="text-gray-400" />
          </div>
        )}
        
        {!imageError && (
          <img
            src={image}
            alt={title || 'Link preview'}
            className={`w-full h-48 object-cover transition-opacity duration-200 ${
              imageLoaded ? 'opacity-100' : 'opacity-0'
            }`}
            style={{ display: imageLoaded ? 'block' : 'none' }}
            onLoad={handleImageLoad}
            onError={handleImageError}
          />
        )}

        {imageError && (
          <div className="w-full h-48 bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
            <Icon name="image-off" size="lg" className="text-gray-400" />
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={`rounded-lg overflow-hidden bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 transition-colors cursor-pointer ${className}`}>
      {/* Preview image */}
      {renderPreviewImage()}

      {/* Link content */}
      <div className="p-4" onClick={handleLinkClick}>
        {/* Domain and favicon */}
        <div className="flex items-center space-x-2 mb-2">
          {renderFavicon()}
          <Typography variant="caption" className="text-gray-500 dark:text-gray-400">
            {getDomain()}
          </Typography>
          <Icon name="external-link" size="xs" className="text-gray-400" />
        </div>

        {/* Title */}
        {title && (
          <Typography variant="body2" className="font-medium text-gray-900 dark:text-gray-100 mb-2 line-clamp-2">
            {truncateText(title, 100)}
          </Typography>
        )}

        {/* Description */}
        {description && (
          <Typography variant="caption" className="text-gray-600 dark:text-gray-400 line-clamp-3">
            {truncateText(description, 200)}
          </Typography>
        )}

        {/* URL display */}
        <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
          <Typography variant="caption" className="text-blue-600 dark:text-blue-400 break-all">
            {truncateText(url, 60)}
          </Typography>
        </div>
      </div>

      {/* Hover overlay */}
      <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-5 transition-all duration-200 pointer-events-none" />
    </div>
  );
};

export default LinkMessage;
