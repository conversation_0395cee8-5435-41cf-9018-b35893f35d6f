{"extends": ["stylelint-config-standard"], "rules": {"at-rule-no-unknown": [true, {"ignoreAtRules": ["tailwind", "apply", "variants", "responsive", "screen", "layer"]}], "no-descending-specificity": null, "selector-class-pattern": null, "selector-pseudo-class-no-unknown": [true, {"ignorePseudoClasses": ["global"]}], "property-no-unknown": [true, {"ignoreProperties": ["composes"]}], "function-no-unknown": [true, {"ignoreFunctions": ["theme"]}], "color-function-notation": null, "alpha-value-notation": null, "selector-not-notation": null, "import-notation": null, "media-feature-range-notation": null, "keyframes-name-pattern": null, "custom-property-pattern": null, "declaration-empty-line-before": null, "value-keyword-case": null, "color-function-alias-notation": null, "color-hex-length": null, "rule-empty-line-before": null, "selector-pseudo-element-colon-notation": null, "no-duplicate-selectors": null}}