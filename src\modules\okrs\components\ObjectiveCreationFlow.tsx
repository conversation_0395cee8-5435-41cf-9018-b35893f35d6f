import React, { useState } from 'react';

import { ObjectiveType } from '../types/objective.types';

import CompanyObjectiveForm from './CompanyObjectiveForm';
import DepartmentObjectiveForm from './DepartmentObjectiveForm';
import ObjectiveForm from './ObjectiveForm';
import ObjectiveTypeSelection from './ObjectiveTypeSelection';

export interface ObjectiveCreationFlowProps {
  /**
   * Callback khi form được submit thành công
   */
  onSuccess?: () => void;

  /**
   * Callback khi form bị đóng
   */
  onCancel?: () => void;

  /**
   * ID của chu kỳ OKR mặc định (nếu có)
   */
  defaultCycleId?: string;
}

/**
 * Component quản lý quy trình tạo mục tiêu 2 bước
 */
const ObjectiveCreationFlow: React.FC<ObjectiveCreationFlowProps> = ({
  onSuccess,
  onCancel,
  defaultCycleId,
}) => {
  // State quản lý bước hiện tại và loại mục tiêu đã chọn
  const [currentStep, setCurrentStep] = useState<'selection' | 'form'>('selection');
  const [selectedType, setSelectedType] = useState<ObjectiveType | null>(null);

  // Xử lý khi chọn loại mục tiêu
  const handleSelectType = (type: ObjectiveType) => {
    setSelectedType(type);
    setCurrentStep('form');
  };

  // Xử lý khi form được submit thành công
  const handleFormSuccess = () => {
    // Reset state
    setCurrentStep('selection');
    setSelectedType(null);

    // Gọi callback onSuccess
    if (onSuccess) {
      onSuccess();
    }
  };

  // Xử lý khi hủy form
  const handleFormCancel = () => {
    // Reset state
    setCurrentStep('selection');
    setSelectedType(null);

    // Gọi callback onCancel
    if (onCancel) {
      onCancel();
    }
  };

  // Render form tương ứng với loại mục tiêu đã chọn
  const renderForm = () => {
    switch (selectedType) {
      case ObjectiveType.INDIVIDUAL:
        return (
          <ObjectiveForm
            onSuccess={handleFormSuccess}
            onCancel={handleFormCancel}
            defaultCycleId={defaultCycleId}
          />
        );

      case ObjectiveType.DEPARTMENT:
        return (
          <DepartmentObjectiveForm
            onSuccess={handleFormSuccess}
            onCancel={handleFormCancel}
            defaultCycleId={defaultCycleId}
          />
        );

      case ObjectiveType.COMPANY:
        return (
          <CompanyObjectiveForm
            onSuccess={handleFormSuccess}
            onCancel={handleFormCancel}
            defaultCycleId={defaultCycleId}
          />
        );

      default:
        return null;
    }
  };

  // Render bước chọn loại mục tiêu
  if (currentStep === 'selection') {
    return <ObjectiveTypeSelection onSelectType={handleSelectType} />;
  }

  // Render bước form tạo mục tiêu
  return (
    <div className="space-y-4">
      {/* Form tương ứng */}
      {renderForm()}
    </div>
  );
};

export default ObjectiveCreationFlow;
