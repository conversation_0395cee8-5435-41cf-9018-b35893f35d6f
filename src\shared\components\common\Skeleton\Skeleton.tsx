import React from 'react';
import { cn } from '@/shared/utils/cn';

export interface SkeletonProps {
  /**
   * Chiều cao của skeleton
   */
  height?: string | number;

  /**
   * Chiều rộng của skeleton
   */
  width?: string | number;

  /**
   * <PERSON><PERSON> làm tròn các góc hay không
   * @default false
   */
  rounded?: boolean;

  /**
   * <PERSON>ó hiển thị dạng hình tròn hay không
   * @default false
   */
  circle?: boolean;

  /**
   * Lớp CSS tùy chỉnh
   */
  className?: string;
}

/**
 * Component hiển thị skeleton loading
 */
const Skeleton: React.FC<SkeletonProps> = ({
  height,
  width,
  rounded = false,
  circle = false,
  className,
}) => {
  return (
    <div
      className={cn(
        'animate-pulse bg-gray-200 dark:bg-gray-700',
        {
          'rounded-md': rounded && !circle,
          'rounded-full': circle,
        },
        className
      )}
      style={{
        height,
        width,
        ...(circle && { aspectRatio: '1/1' }),
      }}
    />
  );
};

export default Skeleton;
