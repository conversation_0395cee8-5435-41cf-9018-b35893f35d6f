{"product": {"title": "产品", "description": "管理产品列表", "common": {"all": "全部", "active": "活跃", "inactive": "不活跃", "draft": "草稿", "search": "搜索", "filter": "筛选", "add": "新增", "edit": "编辑", "delete": "删除", "view": "查看", "save": "保存", "cancel": "取消", "back": "返回", "actions": "操作", "confirm": "确认", "confirmDelete": "确认删除", "deleteSuccess": "删除成功", "deleteError": "删除失败", "updateSuccess": "更新成功", "updateError": "更新失败", "createSuccess": "创建成功", "createError": "创建失败", "required": "此字段为必填项", "invalidNumber": "无效数字", "physical": "实物", "digital": "数字", "service": "服务", "category": "类别", "tag": "标签", "priceRange": "价格范围", "custom": "自定义"}, "list": {"title": "产品列表", "addProduct": "添加产品", "importProducts": "导入产品", "exportProducts": "导出产品", "bulkActions": "批量操作", "filterByStatus": "按状态筛选", "filterByType": "按类型筛选", "filterByCategory": "按类别筛选", "filterByPriceRange": "按价格范围筛选", "searchPlaceholder": "搜索产品...", "noProducts": "未找到产品", "showingProducts": "显示 {{count}} 个产品，共 {{total}} 个"}, "detail": {"title": "产品详情", "basicInfo": "基本信息", "pricing": "价格", "inventory": "库存", "images": "图片", "categories": "类别", "tags": "标签", "relatedProducts": "相关产品", "statistics": "统计", "reviews": "评价", "history": "历史"}, "form": {"createTitle": "添加新产品", "editTitle": "编辑产品", "nameLabel": "产品名称", "namePlaceholder": "输入产品名称", "descriptionLabel": "描述", "descriptionPlaceholder": "输入产品描述", "priceLabel": "价格", "pricePlaceholder": "输入产品价格", "originalPriceLabel": "原价", "originalPricePlaceholder": "输入原价", "statusLabel": "状态", "typeLabel": "产品类型", "categoryLabel": "类别", "categoryPlaceholder": "选择类别", "tagsLabel": "标签", "tagsPlaceholder": "输入标签", "stockLabel": "库存数量", "stockPlaceholder": "输入库存数量", "imagesLabel": "图片", "addImage": "添加图片", "mainImageLabel": "主图", "selectMainImage": "选择主图", "dragToReorder": "拖动重新排序"}, "segment": {"title": "产品细分", "description": "管理产品细分", "list": {"title": "细分列表", "addSegment": "添加细分", "filterByStatus": "按状态筛选", "filterByType": "按类型筛选", "searchPlaceholder": "搜索细分...", "noSegments": "未找到细分", "showingSegments": "显示 {{count}} 个细分，共 {{total}} 个"}, "form": {"createTitle": "添加新细分", "editTitle": "编辑细分", "nameLabel": "细分名称", "namePlaceholder": "输入细分名称", "descriptionLabel": "描述", "descriptionPlaceholder": "输入细分描述", "typeLabel": "细分类型", "statusLabel": "状态", "conditionsLabel": "条件", "addCondition": "添加条件", "addConditionGroup": "添加条件组", "fieldLabel": "字段", "operatorLabel": "运算符", "valueLabel": "值", "valuePlaceholder": "输入值", "andOperator": "且", "orOperator": "或", "previewResults": "预览结果", "matchingProducts": "匹配产品: {{count}}"}, "conditions": {"equals": "等于", "notEquals": "不等于", "contains": "包含", "notContains": "不包含", "greaterThan": "大于", "lessThan": "小于", "between": "介于", "in": "在列表中", "notIn": "不在列表中"}}, "table": {"id": "ID", "name": "名称", "price": "价格", "originalPrice": "原价", "discount": "折扣", "status": "状态", "type": "类型", "category": "类别", "stock": "库存", "createdAt": "创建时间", "updatedAt": "更新时间"}, "validation": {"nameRequired": "产品名称为必填项", "nameMinLength": "产品名称至少需要2个字符", "nameMaxLength": "产品名称不能超过100个字符", "priceRequired": "产品价格为必填项", "priceInvalid": "产品价格无效", "priceMin": "产品价格不能为负", "typeRequired": "产品类型为必填项", "stockInvalid": "库存数量无效", "stockMin": "库存数量不能为负"}}}