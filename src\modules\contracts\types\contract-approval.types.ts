/**
 * Contract Approval Types for Frontend
 */

export enum ApprovalStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  DELEGATED = 'delegated',
  EXPIRED = 'expired',
}

export enum ApprovalType {
  LEGAL_REVIEW = 'legal_review',
  FINANCIAL_REVIEW = 'financial_review',
  TECHNICAL_REVIEW = 'technical_review',
  MANAGEMENT_APPROVAL = 'management_approval',
  COMPLIANCE_CHECK = 'compliance_check',
  CUSTOM = 'custom',
}

export interface ApprovalConditions {
  minContractValue?: number;
  maxContractValue?: number;
  contractTypes?: string[];
  departments?: string[];
  customRules?: Record<string, any>;
}

export interface ApprovalData {
  approvedBy: {
    userId: string;
    userName: string;
    approvedAt: string;
    comments?: string;
  }[];
  rejectedBy?: {
    userId: string;
    userName: string;
    rejectedAt: string;
    reason: string;
  };
}

export interface ContractApproval {
  id: string;
  status: ApprovalStatus;
  type: ApprovalType;
  stepOrder: number;
  stepName: string;
  stepDescription?: string;
  required: boolean;
  requiredApprovals: number;
  currentApprovals: number;
  requestedAt?: string;
  respondedAt?: string;
  expiresAt?: string;
  comments?: string;
  rejectionReason?: string;
  conditions?: ApprovalConditions;
  approvalData?: ApprovalData;
  autoApproved: boolean;
  autoApprovalRule?: string;
  metadata?: Record<string, any>;
  
  // Relations
  contractId: string;
  requestedById: string;
  assignedToId?: string;
  delegatedToId?: string;
  
  // Timestamps
  createdAt: string;
  updatedAt: string;
  
  // Related data
  requestedBy?: {
    id: string;
    email: string;
    name: string;
  };
  assignedTo?: {
    id: string;
    email: string;
    name: string;
  };
  delegatedTo?: {
    id: string;
    email: string;
    name: string;
  };
}

export interface ApprovalQueryParams {
  page?: number;
  limit?: number;
  contractId?: string;
  status?: ApprovalStatus[];
  type?: ApprovalType[];
  assignedToId?: string;
  requestedById?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface ApprovalCreateDto {
  contractId: string;
  type: ApprovalType;
  stepName: string;
  stepDescription?: string;
  stepOrder?: number;
  required?: boolean;
  requiredApprovals?: number;
  assignedToId?: string;
  expiresAt?: string;
  conditions?: ApprovalConditions;
  metadata?: Record<string, any>;
}

export interface ApprovalUpdateDto {
  stepName?: string;
  stepDescription?: string;
  stepOrder?: number;
  required?: boolean;
  requiredApprovals?: number;
  assignedToId?: string;
  expiresAt?: string;
  conditions?: ApprovalConditions;
  metadata?: Record<string, any>;
}

export interface ApprovalActionDto {
  comments?: string;
}

export interface ApprovalRejectDto {
  reason: string;
  comments?: string;
}

export interface ApprovalDelegateDto {
  toUserId: string;
  message?: string;
}

export interface ApprovalListResponse {
  items: ContractApproval[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface ApprovalProgress {
  current: number;
  required: number;
  percentage: number;
  isComplete: boolean;
}

export interface ApprovalWorkflowStep {
  id: string;
  order: number;
  name: string;
  description?: string;
  type: ApprovalType;
  required: boolean;
  requiredApprovals: number;
  currentApprovals: number;
  status: ApprovalStatus;
  assignedTo?: {
    id: string;
    name: string;
    email: string;
  };
  conditions?: ApprovalConditions;
  progress: ApprovalProgress;
}

export interface ApprovalWorkflow {
  contractId: string;
  steps: ApprovalWorkflowStep[];
  currentStep: number;
  totalSteps: number;
  isComplete: boolean;
  overallProgress: number;
}
