import { useState, useCallback } from 'react';

import { KanbanBoardData, KanbanCard, KanbanColumn, KanbanMoveResult } from '../types/kanban.types';

interface UseKanbanBoardProps {
  /**
   * Dữ liệu ban đầu của Kanban Board
   */
  initialData: KanbanBoardData;

  /**
   * Callback khi có thay đổi dữ liệu
   */
  onChange?: (data: KanbanBoardData) => void;

  /**
   * Callback khi kéo thả thẻ
   */
  onCardMove?: (result: KanbanMoveResult) => void;

  /**
   * Callback khi thêm thẻ mới
   */
  onCardAdd?: (columnId: string, card: Partial<KanbanCard>) => void;

  /**
   * Callback khi sửa thẻ
   */
  onCardEdit?: (cardId: string, columnId: string, updates: Partial<KanbanCard>) => void;

  /**
   * Callback khi xóa thẻ
   */
  onCardDelete?: (cardId: string, columnId: string) => void;

  /**
   * Callback khi thêm cột mới
   */
  onColumnAdd?: (column: Partial<KanbanColumn>) => void;

  /**
   * Callback khi sửa cột
   */
  onColumnEdit?: (columnId: string, updates: Partial<KanbanColumn>) => void;

  /**
   * Callback khi xóa cột
   */
  onColumnDelete?: (columnId: string) => void;

  /**
   * Callback khi thay đổi thứ tự cột
   */
  onColumnReorder?: (columnId: string, newIndex: number) => void;
}

/**
 * Hook để quản lý trạng thái của Kanban Board
 */
export const useKanbanBoard = ({
  initialData,
  onChange,
  onCardMove,
  onCardAdd,
  onCardEdit,
  onCardDelete,
  // Các callback cho cột sẽ được sử dụng trong tương lai
  // onColumnAdd,
  // onColumnEdit,
  // onColumnDelete,
  // onColumnReorder,
}: UseKanbanBoardProps) => {
  // State cho dữ liệu Kanban Board
  const [boardData, setBoardData] = useState<KanbanBoardData>(initialData);

  // Lấy danh sách thẻ cho mỗi cột
  const getCardsForColumn = useCallback(
    (columnId: string) => {
      const column = boardData.columns.find(col => col.id === columnId);
      if (!column) {return [];}

      return column.cardIds.map(cardId => boardData.cards[cardId]).filter(Boolean);
    },
    [boardData]
  );

  // Xử lý kéo thả thẻ
  const handleCardMove = useCallback(
    (result: KanbanMoveResult) => {
      const { cardId, sourceColumnId, targetColumnId, newIndex } = result;

      // Nếu có callback bên ngoài, gọi nó
      if (onCardMove) {
        onCardMove(result);
      }

      // Cập nhật state nội bộ
      setBoardData(prev => {
        // Clone dữ liệu để tránh thay đổi trực tiếp state
        const newData = { ...prev };
        const newColumns = [...prev.columns];

        // Tìm cột nguồn và cột đích
        const sourceColumnIndex = newColumns.findIndex(col => col.id === sourceColumnId);
        const targetColumnIndex = newColumns.findIndex(col => col.id === targetColumnId);

        if (sourceColumnIndex === -1 || targetColumnIndex === -1) {
          return prev;
        }

        const sourceColumn = { ...newColumns[sourceColumnIndex] };
        const targetColumn =
          sourceColumnId === targetColumnId ? sourceColumn : { ...newColumns[targetColumnIndex] };

        // Xóa cardId khỏi cột nguồn
        const newSourceCardIds = [...sourceColumn.cardIds];
        const cardIndex = newSourceCardIds.indexOf(cardId);

        // Chỉ xóa nếu cardId tồn tại trong mảng
        if (cardIndex !== -1) {
          newSourceCardIds.splice(cardIndex, 1);
        }

        sourceColumn.cardIds = newSourceCardIds;

        // Thêm cardId vào cột đích
        if (sourceColumnId === targetColumnId) {
          // Nếu di chuyển trong cùng một cột
          sourceColumn.cardIds.splice(newIndex, 0, cardId);
        } else {
          // Nếu di chuyển giữa các cột khác nhau
          const newTargetCardIds = [...targetColumn.cardIds];
          newTargetCardIds.splice(newIndex, 0, cardId);
          targetColumn.cardIds = newTargetCardIds;
        }

        // Cập nhật cột trong mảng columns
        newColumns[sourceColumnIndex] = sourceColumn;
        if (sourceColumnId !== targetColumnId) {
          newColumns[targetColumnIndex] = targetColumn;
        }

        newData.columns = newColumns;

        // Gọi callback onChange nếu có
        if (onChange) {
          onChange(newData);
        }

        return newData;
      });
    },
    [onChange, onCardMove]
  );

  // Xử lý thêm thẻ mới
  const handleCardAdd = useCallback(
    (columnId: string, card: Partial<KanbanCard>) => {
      // Nếu có callback bên ngoài, gọi nó
      if (onCardAdd) {
        onCardAdd(columnId, card);
        return;
      }

      // Cập nhật state nội bộ
      setBoardData(prev => {
        // Clone dữ liệu để tránh thay đổi trực tiếp state
        const newData = { ...prev };
        const newColumns = [...prev.columns];

        // Tìm cột cần thêm thẻ
        const columnIndex = newColumns.findIndex(col => col.id === columnId);
        if (columnIndex === -1) {
          return prev;
        }

        // Tạo ID mới cho thẻ
        const newCardId = `card-${Date.now()}`;

        // Tạo thẻ mới
        const newCard: KanbanCard = {
          id: newCardId,
          title: card.title || 'New Card',
          ...card,
        };

        // Thêm thẻ vào danh sách cards
        newData.cards = {
          ...newData.cards,
          [newCardId]: newCard,
        };

        // Thêm ID của thẻ vào cột
        const column = { ...newColumns[columnIndex] };
        column.cardIds = [...column.cardIds, newCardId];
        newColumns[columnIndex] = column;
        newData.columns = newColumns;

        // Gọi callback onChange nếu có
        if (onChange) {
          onChange(newData);
        }

        return newData;
      });
    },
    [onChange, onCardAdd]
  );

  // Xử lý sửa thẻ
  const handleCardEdit = useCallback(
    (cardId: string, columnId: string, updates: Partial<KanbanCard>) => {
      // Nếu có callback bên ngoài, gọi nó
      if (onCardEdit) {
        onCardEdit(cardId, columnId, updates);
        return;
      }

      // Cập nhật state nội bộ
      setBoardData(prev => {
        // Clone dữ liệu để tránh thay đổi trực tiếp state
        const newData = { ...prev };

        // Kiểm tra xem thẻ có tồn tại không
        if (!newData.cards[cardId]) {
          return prev;
        }

        // Cập nhật thẻ
        newData.cards = {
          ...newData.cards,
          [cardId]: {
            ...newData.cards[cardId],
            ...updates,
          },
        };

        // Gọi callback onChange nếu có
        if (onChange) {
          onChange(newData);
        }

        return newData;
      });
    },
    [onChange, onCardEdit]
  );

  // Xử lý xóa thẻ
  const handleCardDelete = useCallback(
    (cardId: string, columnId: string) => {
      // Nếu có callback bên ngoài, gọi nó
      if (onCardDelete) {
        onCardDelete(cardId, columnId);
        return;
      }

      // Cập nhật state nội bộ
      setBoardData(prev => {
        // Clone dữ liệu để tránh thay đổi trực tiếp state
        const newData = { ...prev };
        const newColumns = [...prev.columns];

        // Tìm cột chứa thẻ
        const columnIndex = newColumns.findIndex(col => col.id === columnId);
        if (columnIndex === -1) {
          return prev;
        }

        // Xóa ID của thẻ khỏi cột
        const column = { ...newColumns[columnIndex] };
        column.cardIds = column.cardIds.filter(id => id !== cardId);
        newColumns[columnIndex] = column;
        newData.columns = newColumns;

        // Xóa thẻ khỏi danh sách cards
        const newCards = { ...newData.cards };
        delete newCards[cardId];
        newData.cards = newCards;

        // Gọi callback onChange nếu có
        if (onChange) {
          onChange(newData);
        }

        return newData;
      });
    },
    [onChange, onCardDelete]
  );

  return {
    boardData,
    getCardsForColumn,
    handleCardMove,
    handleCardAdd,
    handleCardEdit,
    handleCardDelete,
  };
};
