import { useMutation, useQueryClient } from '@tanstack/react-query';

import { EmployeeService } from '../services/employee.service';
import { CreateUserForEmployeeDto } from '../types/employee.types';

// Key cho React Query
const EMPLOYEES_QUERY_KEY = 'employees';
const USERS_QUERY_KEY = 'users';

/**
 * Hook để tạo tài khoản người dùng cho nhân viên
 * @returns Mutation result cho việc tạo tài khoản người dùng
 */
export const useCreateUserForEmployee = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateUserForEmployeeDto) => EmployeeService.createUserForEmployee(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [EMPLOYEES_QUERY_KEY] });
      queryClient.invalidateQueries({ queryKey: [USERS_QUERY_KEY] });
    },
  });
};
