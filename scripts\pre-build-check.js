#!/usr/bin/env node

/**
 * Script kiểm tra toàn diện trước khi build
 * Chạy tất cả các kiểm tra lỗi và chỉ cho phép build nếu tất cả các kiểm tra đều thành công
 */

import chalk from 'chalk';
import { execSync } from 'child_process';

// Hàm chạy lệnh và xử lý lỗi
function runCommand(command, name) {
  console.log(chalk.blue(`\n🔍 Đang chạy kiểm tra ${name}...\n`));

  try {
    execSync(command, { stdio: 'inherit' });
    console.log(chalk.green(`\n✅ Kiểm tra ${name} thành công!\n`));
    return true;
  } catch (error) {
    console.error(chalk.red(`\n❌ Kiểm tra ${name} thất bại!\n`));
    console.error(chalk.yellow(`Chi tiết lỗi: ${error.message}\n`));
    return false;
  }
}

// Danh sách các kiểm tra cần thực hiện
const checks = [
  { command: 'npm run type-check', name: 'TypeScript', required: true },
  {
    command:
      'npx eslint src/modules/hrm/components/employee/EmployeeForm.tsx --ext ts,tsx --max-warnings 0',
    name: 'ESLint cho EmployeeForm',
    required: true,
  },
  { command: 'npm run format:check', name: 'Prettier', required: false },
  { command: 'npm run lint:style', name: 'Stylelint', required: false },
  { command: 'npm run test', name: 'Unit Tests', required: false },
];

// Chạy tất cả các kiểm tra
console.log(chalk.blue.bold('🚀 Bắt đầu kiểm tra toàn diện trước khi build...\n'));

let allRequiredPassed = true;
let allPassed = true;
const failedRequiredChecks = [];
const failedOptionalChecks = [];

for (const check of checks) {
  const passed = runCommand(check.command, check.name);
  if (!passed) {
    if (check.required) {
      allRequiredPassed = false;
      failedRequiredChecks.push(check.name);
    } else {
      failedOptionalChecks.push(check.name);
    }
    allPassed = false;
  }
}

// Kết quả kiểm tra
if (allPassed) {
  console.log(
    chalk.green.bold('\n✅ Tất cả các kiểm tra đều thành công! Có thể tiến hành build.\n')
  );
  process.exit(0);
} else if (allRequiredPassed) {
  console.log(
    chalk.yellow.bold(
      '\n⚠️ Một số kiểm tra không bắt buộc thất bại, nhưng tất cả kiểm tra bắt buộc đều thành công.\n'
    )
  );
  console.log(
    chalk.yellow(`Các kiểm tra không bắt buộc thất bại: ${failedOptionalChecks.join(', ')}\n`)
  );
  console.log(chalk.green.bold('Có thể tiến hành build.\n'));
  process.exit(0);
} else {
  console.error(
    chalk.red.bold('\n❌ Một số kiểm tra bắt buộc thất bại! Không thể tiến hành build.\n')
  );
  console.error(chalk.red(`Các kiểm tra bắt buộc thất bại: ${failedRequiredChecks.join(', ')}\n`));
  if (failedOptionalChecks.length > 0) {
    console.error(
      chalk.yellow(`Các kiểm tra không bắt buộc thất bại: ${failedOptionalChecks.join(', ')}\n`)
    );
  }
  console.error(chalk.yellow('Vui lòng sửa các lỗi trên trước khi build.\n'));
  process.exit(1);
}
