import { ApiProperty } from '@nestjs/swagger';
import { 
  IsString, 
  IsInt, 
  IsOptional, 
  IsEnum, 
  IsDate, 
  <PERSON><PERSON>ength, 
  Min
} from 'class-validator';
import { Type } from 'class-transformer';
import { EmployeeStatus } from '../enum/employee-status.enum';
import { EmploymentType } from '../enum/employment-type.enum';
import { MaritalStatus } from '../enum/marital-status.enum';

/**
 * DTO for updating an employee
 */
export class UpdateEmployeeDto {
  /**
   * ID of the department the employee belongs to
   * @example 1
   */
  @ApiProperty({ required: false, description: 'ID of the department the employee belongs to', example: 1 })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  departmentId?: number;

  /**
   * Employee's job title
   * @example "Senior Software Engineer"
   */
  @ApiProperty({ required: false, description: 'Employee\'s job title', example: 'Senior Software Engineer' })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  jobTitle?: string;

  /**
   * Employee's job level
   * @example "Lead"
   */
  @ApiProperty({ required: false, description: 'Employee\'s job level', example: 'Lead' })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  jobLevel?: string;

  /**
   * Employee's manager's ID
   * @example 3
   */
  @ApiProperty({ required: false, description: 'Employee\'s manager\'s ID', example: 3 })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  managerId?: number;

  /**
   * Employee's employment type
   * @example "full_time"
   */
  @ApiProperty({ 
    required: false, 
    description: 'Employee\'s employment type', 
    enum: EmploymentType,
    example: EmploymentType.FULL_TIME 
  })
  @IsOptional()
  @IsEnum(EmploymentType)
  employmentType?: EmploymentType;

  /**
   * Employee's status
   * @example "active"
   */
  @ApiProperty({ 
    required: false, 
    description: 'Employee\'s status', 
    enum: EmployeeStatus,
    example: EmployeeStatus.ACTIVE 
  })
  @IsOptional()
  @IsEnum(EmployeeStatus)
  status?: EmployeeStatus;

  /**
   * Employee's hire date
   * @example "2023-01-15"
   */
  @ApiProperty({ required: false, description: 'Employee\'s hire date', example: '2023-01-15' })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  hireDate?: Date;

  /**
   * Employee's termination date
   * @example "2023-12-31"
   */
  @ApiProperty({ required: false, description: 'Employee\'s termination date', example: '2023-12-31' })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  terminationDate?: Date;

  /**
   * Reason for termination
   * @example "Resigned for better opportunity"
   */
  @ApiProperty({ required: false, description: 'Reason for termination', example: 'Resigned for better opportunity' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  terminationReason?: string;

  /**
   * Employee's probation end date
   * @example "2023-04-15"
   */
  @ApiProperty({ required: false, description: 'Employee\'s probation end date', example: '2023-04-15' })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  probationEndDate?: Date;

  /**
   * Employee's marital status
   * @example "married"
   */
  @ApiProperty({ 
    required: false, 
    description: 'Employee\'s marital status', 
    enum: MaritalStatus,
    example: MaritalStatus.MARRIED 
  })
  @IsOptional()
  @IsEnum(MaritalStatus)
  maritalStatus?: MaritalStatus;

  /**
   * Number of dependents
   * @example 2
   */
  @ApiProperty({ required: false, description: 'Number of dependents', example: 2 })
  @IsOptional()
  @IsInt()
  @Min(0)
  @Type(() => Number)
  numberOfDependents?: number;

  /**
   * Emergency contact name
   * @example "Jane Doe"
   */
  @ApiProperty({ required: false, description: 'Emergency contact name', example: 'Jane Doe' })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  emergencyContactName?: string;

  /**
   * Emergency contact phone
   * @example "0987654321"
   */
  @ApiProperty({ required: false, description: 'Emergency contact phone', example: '0987654321' })
  @IsOptional()
  @IsString()
  @MaxLength(20)
  emergencyContactPhone?: string;

  /**
   * Emergency contact relationship
   * @example "Spouse"
   */
  @ApiProperty({ required: false, description: 'Emergency contact relationship', example: 'Spouse' })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  emergencyContactRelationship?: string;

  /**
   * Notes about the employee
   * @example "Promoted to team lead in 2023"
   */
  @ApiProperty({ required: false, description: 'Notes about the employee', example: 'Promoted to team lead in 2023' })
  @IsOptional()
  @IsString()
  notes?: string;
}
