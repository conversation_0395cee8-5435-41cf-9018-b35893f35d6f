import { useQuery } from '@tanstack/react-query';

import { StatisticsService } from '../services/statistics.service';

// Keys cho React Query
const USER_PERFORMANCE_KEY = 'user-performance';
const PROJECT_PERFORMANCE_KEY = 'project-performance';
const PROJECT_GANTT_KEY = 'project-gantt';
const STATISTICS_OVERVIEW_KEY = 'statistics-overview';

/**
 * Hook để lấy hiệu suất người dùng
 * @param userId ID người dùng
 * @param startDate Ngày bắt đầu (timestamp)
 * @param endDate Ngày kết thúc (timestamp)
 * @param projectId ID dự án (tùy chọn)
 * @returns Query result với hiệu suất người dùng
 */
export const useUserPerformance = (
  userId: number,
  startDate?: number,
  endDate?: number,
  projectId?: number
) => {
  return useQuery({
    queryKey: [USER_PERFORMANCE_KEY, userId, startDate, endDate, projectId],
    queryFn: () => StatisticsService.getUserPerformance(userId, startDate, endDate, projectId),
    select: data => data.result,
    enabled: !!userId,
  });
};

/**
 * Hook để lấy hiệu suất dự án
 * @param projectId ID dự án
 * @param startDate Ngày bắt đầu (timestamp)
 * @param endDate Ngày kết thúc (timestamp)
 * @returns Query result với hiệu suất dự án
 */
export const useProjectPerformance = (projectId: number, startDate?: number, endDate?: number) => {
  return useQuery({
    queryKey: [PROJECT_PERFORMANCE_KEY, projectId, startDate, endDate],
    queryFn: () => StatisticsService.getProjectPerformance(projectId, startDate, endDate),
    select: data => data.result,
    enabled: !!projectId,
  });
};

/**
 * Hook để lấy dữ liệu biểu đồ Gantt của dự án
 * @param projectId ID dự án
 * @returns Query result với dữ liệu biểu đồ Gantt
 */
export const useProjectGantt = (projectId: number) => {
  return useQuery({
    queryKey: [PROJECT_GANTT_KEY, projectId],
    queryFn: () => StatisticsService.getProjectGantt(projectId),
    select: data => data.result,
    enabled: !!projectId,
  });
};

/**
 * Hook để lấy tổng quan thống kê
 * @param startDate Ngày bắt đầu (timestamp)
 * @param endDate Ngày kết thúc (timestamp)
 * @returns Query result với tổng quan thống kê
 */
export const useStatisticsOverview = (startDate?: number, endDate?: number) => {
  return useQuery({
    queryKey: [STATISTICS_OVERVIEW_KEY, startDate, endDate],
    queryFn: () => StatisticsService.getStatisticsOverview(startDate, endDate),
    select: data => data.result,
  });
};
