import { useContext } from 'react';
import { CalendarThemeContext } from '../contexts/CalendarThemeContext';
import { CalendarThemeContextType } from '../types/theme.types';

/**
 * Hook để sử dụng Calendar Theme Context
 */
export const useCalendarThemeContext = (): CalendarThemeContextType => {
  const context = useContext(CalendarThemeContext);

  if (!context) {
    throw new Error(
      'useCalendarThemeContext must be used within a CalendarThemeProvider'
    );
  }

  return context;
};

export type { CalendarThemeContextType };
