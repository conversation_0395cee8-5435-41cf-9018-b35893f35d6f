import React, { useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import {
  Card,
  Form,
  FormItem,
  Input,
  Button,
  Select,
  Textarea,
  FormGrid,
  FormSection,
  Icon,
} from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/types';

import { productSchema, ProductFormValues } from '../../schemas/product.schema';
import { ProductStatus, ProductType, ProductDto } from '../../types/product.types';

interface ProductFormProps {
  /**
   * Dữ liệu sản phẩm (nếu là chỉnh sửa)
   */
  product?: ProductDto;

  /**
   * Callback khi submit form
   */
  onSubmit: (values: ProductFormValues) => void;

  /**
   * Callback khi hủy
   */
  onCancel: () => void;

  /**
   * Đang xử lý submit
   */
  isSubmitting?: boolean;

  /**
   * Danh sách danh mục
   */
  categories?: Array<{ value: string; label: string }>;
}

/**
 * Component form thêm/sửa sản phẩm
 */
const ProductForm: React.FC<ProductFormProps> = ({
  product,
  onSubmit,
  onCancel,
  isSubmitting = false,
  categories = [],
}) => {
  const { t } = useTranslation(['product']);
  const formRef = useRef<FormRef<ProductFormValues>>(null);
  const isEditMode = !!product;

  // State cho hình ảnh
  const [images, setImages] = useState<string[]>(product?.images || []);
  const [mainImage, setMainImage] = useState<string>(product?.mainImage || '');

  // Xử lý submit form
  const handleSubmit = (values: ProductFormValues) => {
    // Thêm hình ảnh vào values
    const formValues = {
      ...values,
      images,
      mainImage: mainImage || (images.length > 0 ? images[0] : ''),
    };
    onSubmit(formValues);
  };

  // Xử lý thêm hình ảnh
  const handleAddImage = (url: string) => {
    if (url && !images.includes(url)) {
      const newImages = [...images, url];
      setImages(newImages);
      if (!mainImage) {
        setMainImage(url);
      }
    }
  };

  // Xử lý xóa hình ảnh
  const handleRemoveImage = (url: string) => {
    const newImages = images.filter(img => img !== url);
    setImages(newImages);
    if (mainImage === url) {
      setMainImage(newImages.length > 0 ? newImages[0] : '');
    }
  };

  // Xử lý đặt hình ảnh chính
  const handleSetMainImage = (url: string) => {
    setMainImage(url);
  };

  return (
    <Card
      title={isEditMode ? t('product:form.editTitle') : t('product:form.createTitle')}
      className="mb-4"
    >
      <Form
        ref={formRef}
        schema={productSchema}
        onSubmit={handleSubmit}
        className="space-y-6"
        defaultValues={{
          name: product?.name || '',
          description: product?.description || '',
          price: product?.price || 0,
          originalPrice: product?.originalPrice || undefined,
          status: product?.status || ProductStatus.DRAFT,
          type: product?.type || ProductType.PHYSICAL,
          categoryId: product?.categoryId || '',
          stock: product?.stock || 0,
        }}
      >
        <FormSection title={t('product:detail.basicInfo')}>
          <FormGrid columns={2} columnsSm={1} gap="md">
            <FormItem name="name" label={t('product:form.nameLabel')} required>
              <Input
                placeholder={t('product:form.namePlaceholder')}
                fullWidth
                leftIcon={<Icon name="package" size="sm" />}
              />
            </FormItem>

            <FormItem name="type" label={t('product:form.typeLabel')} required>
              <Select
                options={[
                  { value: ProductType.PHYSICAL, label: t('product:common.physical') },
                  { value: ProductType.DIGITAL, label: t('product:common.digital') },
                  { value: ProductType.SERVICE, label: t('product:common.service') },
                ]}
                placeholder={t('product:form.typeLabel')}
                fullWidth
              />
            </FormItem>
          </FormGrid>

          <FormItem name="description" label={t('product:form.descriptionLabel')}>
            <Textarea
              placeholder={t('product:form.descriptionPlaceholder')}
              rows={4}
              fullWidth
            />
          </FormItem>

          <FormGrid columns={2} columnsSm={1} gap="md">
            <FormItem name="categoryId" label={t('product:form.categoryLabel')}>
              <Select
                options={categories}
                placeholder={t('product:form.categoryPlaceholder')}
                fullWidth
              />
            </FormItem>

            <FormItem name="status" label={t('product:form.statusLabel')}>
              <Select
                options={[
                  { value: ProductStatus.ACTIVE, label: t('product:common.active') },
                  { value: ProductStatus.INACTIVE, label: t('product:common.inactive') },
                  { value: ProductStatus.DRAFT, label: t('product:common.draft') },
                ]}
                placeholder={t('product:form.statusLabel')}
                fullWidth
              />
            </FormItem>
          </FormGrid>
        </FormSection>

        <FormSection title={t('product:detail.pricing')}>
          <FormGrid columns={2} columnsSm={1} gap="md">
            <FormItem name="price" label={t('product:form.priceLabel')} required>
              <Input
                type="number"
                placeholder={t('product:form.pricePlaceholder')}
                fullWidth
                leftIcon={<Icon name="dollar-sign" size="sm" />}
              />
            </FormItem>

            <FormItem name="originalPrice" label={t('product:form.originalPriceLabel')}>
              <Input
                type="number"
                placeholder={t('product:form.originalPricePlaceholder')}
                fullWidth
                leftIcon={<Icon name="dollar-sign" size="sm" />}
              />
            </FormItem>
          </FormGrid>
        </FormSection>

        <FormSection title={t('product:detail.inventory')}>
          <FormItem name="stock" label={t('product:form.stockLabel')}>
            <Input
              type="number"
              placeholder={t('product:form.stockPlaceholder')}
              fullWidth
              leftIcon={<Icon name="box" size="sm" />}
            />
          </FormItem>
        </FormSection>

        <FormSection title={t('product:detail.images')}>
          <div className="space-y-4">
            <div className="flex flex-wrap gap-2 mb-4">
              {images.map((url, index) => (
                <div
                  key={index}
                  className={`relative w-24 h-24 border rounded overflow-hidden ${
                    mainImage === url ? 'ring-2 ring-primary' : ''
                  }`}
                >
                  <img src={url} alt={`Product ${index}`} className="w-full h-full object-cover" />
                  <div className="absolute top-0 right-0 flex space-x-1 p-1">
                    <button
                      type="button"
                      className="bg-white rounded-full p-1 shadow-sm hover:bg-gray-100"
                      onClick={() => handleSetMainImage(url)}
                      title={t('product:form.selectMainImage')}
                    >
                      <Icon name="star" size="xs" className={mainImage === url ? 'text-yellow-500' : ''} />
                    </button>
                    <button
                      type="button"
                      className="bg-white rounded-full p-1 shadow-sm hover:bg-gray-100"
                      onClick={() => handleRemoveImage(url)}
                    >
                      <Icon name="x" size="xs" />
                    </button>
                  </div>
                </div>
              ))}
            </div>

            <div className="flex items-center space-x-2">
              <Input
                placeholder="https://example.com/image.jpg"
                className="flex-grow"
                id="image-url"
              />
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  const input = document.getElementById('image-url') as HTMLInputElement;
                  if (input.value) {
                    handleAddImage(input.value);
                    input.value = '';
                  }
                }}
              >
                {t('product:form.addImage')}
              </Button>
            </div>
          </div>
        </FormSection>

        <div className="flex justify-end space-x-2 pt-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            {t('product:common.cancel')}
          </Button>
          <Button type="submit" variant="primary" loading={isSubmitting}>
            {t('product:common.save')}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default ProductForm;
