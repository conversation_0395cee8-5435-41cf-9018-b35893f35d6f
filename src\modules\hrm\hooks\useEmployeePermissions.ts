import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

import { PermissionService } from '../services/permission.service';

// Key cho React Query
const USER_PERMISSIONS_QUERY_KEY = 'user-permissions';

/**
 * Hook để lấy danh sách quyền của người dùng
 * @param userId ID của người dùng
 * @returns Query result với danh sách quyền của người dùng
 */
export const useUserPermissions = (userId: number) => {
  return useQuery({
    queryKey: [USER_PERMISSIONS_QUERY_KEY, userId],
    queryFn: () => PermissionService.getUserPermissions(userId),
    select: data => data.result,
    enabled: !!userId,
  });
};

/**
 * Hook để cập nhật quyền trực tiếp cho nhân viên
 * @returns Mutation result cho việc cập nhật quyền
 */
export const useUpdateEmployeePermission = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ employeeId, data }: { employeeId: number; data: { permissionIds: number[] } }) =>
      PermissionService.updateEmployeePermission(employeeId, data),
    onSuccess: (_, variables) => {
      // Invalidate các query liên quan
      queryClient.invalidateQueries({
        queryKey: [USER_PERMISSIONS_QUERY_KEY],
      });
    },
  });
};
