{"product": {"title": "Products", "description": "Manage product list", "common": {"all": "All", "active": "Active", "inactive": "Inactive", "draft": "Draft", "search": "Search", "filter": "Filter", "add": "Add new", "edit": "Edit", "delete": "Delete", "view": "View", "save": "Save", "cancel": "Cancel", "back": "Back", "actions": "Actions", "confirm": "Confirm", "confirmDelete": "Confirm delete", "deleteSuccess": "Delete successful", "deleteError": "Delete failed", "updateSuccess": "Update successful", "updateError": "Update failed", "createSuccess": "Create successful", "createError": "Create failed", "required": "This field is required", "invalidNumber": "Invalid number", "physical": "Physical", "digital": "Digital", "service": "Service", "category": "Category", "tag": "Tag", "priceRange": "Price Range", "custom": "Custom"}, "list": {"title": "Product List", "addProduct": "Add Product", "importProducts": "Import Products", "exportProducts": "Export Products", "bulkActions": "Bulk Actions", "filterByStatus": "Filter by Status", "filterByType": "Filter by Type", "filterByCategory": "Filter by Category", "filterByPriceRange": "Filter by Price Range", "searchPlaceholder": "Search products...", "noProducts": "No products found", "showingProducts": "Showing {{count}} products out of {{total}}"}, "detail": {"title": "Product Details", "basicInfo": "Basic Information", "pricing": "Pricing", "inventory": "Inventory", "images": "Images", "categories": "Categories", "tags": "Tags", "relatedProducts": "Related Products", "statistics": "Statistics", "reviews": "Reviews", "history": "History"}, "form": {"createTitle": "Add New Product", "editTitle": "Edit Product", "nameLabel": "Product Name", "namePlaceholder": "Enter product name", "descriptionLabel": "Description", "descriptionPlaceholder": "Enter product description", "priceLabel": "Price", "pricePlaceholder": "Enter product price", "originalPriceLabel": "Original Price", "originalPricePlaceholder": "Enter original price", "statusLabel": "Status", "typeLabel": "Product Type", "categoryLabel": "Category", "categoryPlaceholder": "Select category", "tagsLabel": "Tags", "tagsPlaceholder": "Enter tags", "stockLabel": "Stock Quantity", "stockPlaceholder": "Enter stock quantity", "imagesLabel": "Images", "addImage": "Add Image", "mainImageLabel": "Main Image", "selectMainImage": "Select main image", "dragToReorder": "Drag to reorder"}, "segment": {"title": "Product Segments", "description": "Manage product segments", "list": {"title": "Segment List", "addSegment": "Add Segment", "filterByStatus": "Filter by Status", "filterByType": "Filter by Type", "searchPlaceholder": "Search segments...", "noSegments": "No segments found", "showingSegments": "Showing {{count}} segments out of {{total}}"}, "form": {"createTitle": "Add New Segment", "editTitle": "Edit Segment", "nameLabel": "Segment Name", "namePlaceholder": "Enter segment name", "descriptionLabel": "Description", "descriptionPlaceholder": "Enter segment description", "typeLabel": "Segment Type", "statusLabel": "Status", "conditionsLabel": "Conditions", "addCondition": "Add Condition", "addConditionGroup": "Add Condition Group", "fieldLabel": "Field", "operatorLabel": "Operator", "valueLabel": "Value", "valuePlaceholder": "Enter value", "andOperator": "AND", "orOperator": "OR", "previewResults": "Preview Results", "matchingProducts": "Matching Products: {{count}}"}, "conditions": {"equals": "Equals", "notEquals": "Not Equals", "contains": "Contains", "notContains": "Not Contains", "greaterThan": "Greater Than", "lessThan": "Less Than", "between": "Between", "in": "In List", "notIn": "Not In List"}}, "table": {"id": "ID", "name": "Name", "price": "Price", "originalPrice": "Original Price", "discount": "Discount", "status": "Status", "type": "Type", "category": "Category", "stock": "Stock", "createdAt": "Created At", "updatedAt": "Updated At"}, "validation": {"nameRequired": "Product name is required", "nameMinLength": "Product name must be at least 2 characters", "nameMaxLength": "Product name cannot exceed 100 characters", "priceRequired": "Product price is required", "priceInvalid": "Product price is invalid", "priceMin": "Product price cannot be negative", "typeRequired": "Product type is required", "stockInvalid": "Stock quantity is invalid", "stockMin": "Stock quantity cannot be negative"}}}