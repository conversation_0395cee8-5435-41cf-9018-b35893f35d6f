import React, { useState, useRef, useEffect } from 'react';
import { LanguageFlag, Icon } from '@/shared/components/common';
import { useLanguage } from '@/shared/contexts';

/**
 * Custom Language Dropdown component with precise positioning
 */
const LanguageDropdown: React.FC = () => {
  const { language, setLanguage, availableLanguages } = useLanguage();
  const [isOpen, setIsOpen] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0 });
  const dropdownRef = useRef<HTMLDivElement>(null);
  const triggerRef = useRef<HTMLDivElement>(null);

  // Get current language info
  const currentLanguage = availableLanguages.find(lang => lang.code === language);

  // Calculate dropdown position
  const calculatePosition = () => {
    if (triggerRef.current) {
      const rect = triggerRef.current.getBoundingClientRect();
      const viewportHeight = window.innerHeight;
      const viewportWidth = window.innerWidth;
      const dropdownHeight = 120; // Estimated dropdown height
      const dropdownWidth = 144; // w-36 = 144px

      let top = rect.bottom + 4; // 4px gap below trigger
      let left = rect.right - dropdownWidth; // Align right edge

      // Check if dropdown would go below viewport
      if (top + dropdownHeight > viewportHeight) {
        top = rect.top - dropdownHeight - 4; // Show above trigger
      }

      // Check if dropdown would go outside left edge
      if (left < 8) {
        left = 8; // 8px margin from left edge
      }

      // Check if dropdown would go outside right edge
      if (left + dropdownWidth > viewportWidth - 8) {
        left = viewportWidth - dropdownWidth - 8; // 8px margin from right edge
      }

      setDropdownPosition({ top, left });
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    const handleScroll = () => {
      if (isOpen) {
        calculatePosition();
      }
    };

    const handleResize = () => {
      if (isOpen) {
        calculatePosition();
      }
    };

    if (isOpen) {
      calculatePosition();
      document.addEventListener('mousedown', handleClickOutside);
      window.addEventListener('scroll', handleScroll, true);
      window.addEventListener('resize', handleResize);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      window.removeEventListener('scroll', handleScroll, true);
      window.removeEventListener('resize', handleResize);
    };
  }, [isOpen]);

  const handleLanguageSelect = (langCode: string) => {
    setLanguage(langCode as 'vi' | 'en' | 'zh');
    setIsOpen(false);
  };

  return (
    <>
      {/* Trigger */}
      <div
        ref={triggerRef}
        className="flex items-center space-x-1 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 px-2 py-1 rounded-md transition-colors"
        onClick={() => setIsOpen(!isOpen)}
      >
        <LanguageFlag code={language as 'vi' | 'en' | 'zh'} size="sm" />
        <span className="text-sm text-gray-600 dark:text-gray-400">{currentLanguage?.name}</span>
        <Icon
          name="chevron-down"
          size="sm"
          className={`text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`}
        />
      </div>

      {/* Dropdown Menu - Fixed positioned */}
      {isOpen && (
        <div
          ref={dropdownRef}
          className="fixed w-36 bg-white dark:bg-gray-800 rounded-md shadow-lg z-[9999]"
          style={{
            top: dropdownPosition.top,
            left: dropdownPosition.left,
          }}
        >
          <div className="py-1">
            {availableLanguages.map(lang => (
              <button
                key={lang.code}
                className="w-full px-3 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center transition-colors"
                onClick={() => handleLanguageSelect(lang.code)}
              >
                <LanguageFlag code={lang.code as 'vi' | 'en' | 'zh'} size="sm" />
                <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">{lang.name}</span>
                {language === lang.code && (
                  <Icon name="check" size="sm" className="ml-auto text-primary" />
                )}
              </button>
            ))}
          </div>
        </div>
      )}
    </>
  );
};

export default LanguageDropdown;
