import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';

import { Company } from '../../auth/entities/company.entity';
import { User } from '../../auth/entities/user.entity';
import { Contract } from './contract.entity';
import { ContractType } from './contract.entity';

export enum TemplateStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  ARCHIVED = 'archived',
}

/**
 * Entity template hợp đồng
 */
@Entity('contract_templates')
export class ContractTemplate {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'enum', enum: ContractType, default: ContractType.CUSTOM })
  type: ContractType;

  @Column({ type: 'enum', enum: TemplateStatus, default: TemplateStatus.DRAFT })
  status: TemplateStatus;

  @Column({ type: 'varchar', length: 50, unique: true })
  templateCode: string;

  @Column({ type: 'text' })
  content: string;

  @Column({ type: 'json', nullable: true })
  variables: {
    name: string;
    type: 'text' | 'number' | 'date' | 'boolean' | 'select';
    label: string;
    required: boolean;
    defaultValue?: any;
    options?: string[]; // For select type
    placeholder?: string;
    validation?: {
      min?: number;
      max?: number;
      pattern?: string;
    };
  }[];

  @Column({ type: 'json', nullable: true })
  defaultTerms: {
    paymentTerms?: string;
    deliveryTerms?: string;
    cancellationPolicy?: string;
    disputeResolution?: string;
    governingLaw?: string;
  };

  @Column({ type: 'json', nullable: true })
  approvalWorkflow: {
    steps: {
      order: number;
      roleId?: string;
      userId?: string;
      required: boolean;
      description: string;
    }[];
    autoApprove?: boolean;
    approvalThreshold?: number;
  };

  @Column({ type: 'json', nullable: true })
  signatureSettings: {
    requiredSignatures: {
      party: 'party1' | 'party2' | 'both';
      roleId?: string;
      userId?: string;
      order: number;
    }[];
    signatureType: 'electronic' | 'digital' | 'wet';
    allowDelegation: boolean;
  };

  @Column({ type: 'boolean', default: true })
  isPublic: boolean;

  @Column({ type: 'int', default: 0 })
  usageCount: number;

  @Column({ type: 'varchar', length: 20, default: '1.0' })
  version: string;

  @Column({ type: 'json', nullable: true })
  tags: string[];

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  // Relations
  @ManyToOne(() => Company, { nullable: false })
  @JoinColumn({ name: 'companyId' })
  company: Company;

  @Column({ type: 'uuid' })
  companyId: string;

  @ManyToOne(() => User, { nullable: false })
  @JoinColumn({ name: 'createdById' })
  createdBy: User;

  @Column({ type: 'uuid' })
  createdById: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'lastModifiedById' })
  lastModifiedBy: User;

  @Column({ type: 'uuid', nullable: true })
  lastModifiedById: string;

  @OneToMany(() => Contract, (contract) => contract.template)
  contracts: Contract[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Methods
  incrementUsage(): void {
    this.usageCount += 1;
  }

  generateContent(variables: Record<string, any>): string {
    let content = this.content;
    
    // Replace variables in content
    this.variables?.forEach((variable) => {
      const value = variables[variable.name] || variable.defaultValue || '';
      const placeholder = `{{${variable.name}}}`;
      content = content.replace(new RegExp(placeholder, 'g'), value);
    });

    return content;
  }

  validateVariables(variables: Record<string, any>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    this.variables?.forEach((variable) => {
      const value = variables[variable.name];

      // Check required fields
      if (variable.required && (value === undefined || value === null || value === '')) {
        errors.push(`${variable.label} is required`);
        return;
      }

      // Type validation
      if (value !== undefined && value !== null && value !== '') {
        switch (variable.type) {
          case 'number':
            if (isNaN(Number(value))) {
              errors.push(`${variable.label} must be a number`);
            }
            break;
          case 'date':
            if (isNaN(Date.parse(value))) {
              errors.push(`${variable.label} must be a valid date`);
            }
            break;
          case 'boolean':
            if (typeof value !== 'boolean') {
              errors.push(`${variable.label} must be true or false`);
            }
            break;
          case 'select':
            if (variable.options && !variable.options.includes(value)) {
              errors.push(`${variable.label} must be one of: ${variable.options.join(', ')}`);
            }
            break;
        }

        // Validation rules
        if (variable.validation) {
          const { min, max, pattern } = variable.validation;
          
          if (min !== undefined && value.length < min) {
            errors.push(`${variable.label} must be at least ${min} characters`);
          }
          
          if (max !== undefined && value.length > max) {
            errors.push(`${variable.label} must be no more than ${max} characters`);
          }
          
          if (pattern && !new RegExp(pattern).test(value)) {
            errors.push(`${variable.label} format is invalid`);
          }
        }
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}
