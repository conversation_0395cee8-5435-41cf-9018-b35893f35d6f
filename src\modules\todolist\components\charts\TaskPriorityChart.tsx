import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Legend, Tooltip } from 'recharts';

import { TaskDto, TaskPriority } from '../../types/task.types';

interface TaskPriorityChartProps {
  tasks: TaskDto[];
}

/**
 * Chart component to display task distribution by priority
 */
const TaskPriorityChart: React.FC<TaskPriorityChartProps> = ({ tasks }) => {
  const { t } = useTranslation(['todolist']);

  // Prepare data for the chart
  const chartData = useMemo(() => {
    const priorityCounts = {
      [TaskPriority.LOW]: 0,
      [TaskPriority.MEDIUM]: 0,
      [TaskPriority.HIGH]: 0,
      [TaskPriority.URGENT]: 0,
    };

    // Count tasks by priority
    for (const task of tasks) {
      if (task.priority) {
        priorityCounts[task.priority] = (priorityCounts[task.priority] || 0) + 1;
      }
    }

    // Create data array for the chart
    return [
      {
        name: t('todolist:task.priority.low', 'Low'),
        value: priorityCounts[TaskPriority.LOW],
        color: '#22c55e', // green-500
      },
      {
        name: t('todolist:task.priority.medium', 'Medium'),
        value: priorityCounts[TaskPriority.MEDIUM],
        color: '#3b82f6', // blue-500
      },
      {
        name: t('todolist:task.priority.high', 'High'),
        value: priorityCounts[TaskPriority.HIGH],
        color: '#f59e0b', // amber-500
      },
      {
        name: t('todolist:task.priority.urgent', 'Urgent'),
        value: priorityCounts[TaskPriority.URGENT],
        color: '#ef4444', // red-500
      },
    ].filter(item => item.value > 0); // Only include priorities with tasks
  }, [tasks, t]);

  // If no tasks, show empty state
  if (chartData.length === 0) {
    return (
      <div className="h-full flex items-center justify-center text-gray-500">
        {t('todolist:dashboard.charts.noData', 'No data available')}
      </div>
    );
  }

  return (
    <ResponsiveContainer width="100%" height="100%">
      <PieChart>
        <Pie
          data={chartData}
          cx="50%"
          cy="50%"
          labelLine={false}
          outerRadius={80}
          fill="#8884d8"
          dataKey="value"
          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
        >
          {chartData.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={entry.color} />
          ))}
        </Pie>
        <Tooltip
          formatter={(value: number) => [
            `${value} ${t('todolist:dashboard.charts.tasks', 'tasks')}`,
            t('todolist:dashboard.charts.count', 'Count'),
          ]}
        />
        <Legend />
      </PieChart>
    </ResponsiveContainer>
  );
};

export default TaskPriorityChart;
