import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';

import { Company } from '../../auth/entities/company.entity';
import { User } from '../../auth/entities/user.entity';
import { ContractVersion } from './contract-version.entity';
import { ContractSignature } from './contract-signature.entity';
import { ContractApproval } from './contract-approval.entity';
import { ContractAttachment } from './contract-attachment.entity';
import { ContractComment } from './contract-comment.entity';
import { ContractTemplate } from './contract-template.entity';

export enum ContractStatus {
  DRAFT = 'draft',
  UNDER_REVIEW = 'under_review',
  PENDING_APPROVAL = 'pending_approval',
  PENDING_SIGNATURE = 'pending_signature',
  ACTIVE = 'active',
  EXPIRED = 'expired',
  TERMINATED = 'terminated',
  CANCELLED = 'cancelled',
}

export enum ContractType {
  EMPLOYMENT = 'employment',
  SERVICE = 'service',
  PURCHASE = 'purchase',
  LEASE = 'lease',
  NDA = 'nda',
  PARTNERSHIP = 'partnership',
  CUSTOM = 'custom',
}

export enum ContractPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

/**
 * Entity hợp đồng chính
 */
@Entity('contracts')
export class Contract {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  title: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'enum', enum: ContractType, default: ContractType.CUSTOM })
  type: ContractType;

  @Column({ type: 'enum', enum: ContractStatus, default: ContractStatus.DRAFT })
  status: ContractStatus;

  @Column({ type: 'enum', enum: ContractPriority, default: ContractPriority.MEDIUM })
  priority: ContractPriority;

  @Column({ type: 'varchar', length: 100, unique: true })
  contractNumber: string;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  value: number;

  @Column({ type: 'varchar', length: 10, nullable: true })
  currency: string;

  @Column({ type: 'date', nullable: true })
  startDate: Date;

  @Column({ type: 'date', nullable: true })
  endDate: Date;

  @Column({ type: 'date', nullable: true })
  signedDate: Date;

  @Column({ type: 'boolean', default: false })
  autoRenewal: boolean;

  @Column({ type: 'int', nullable: true })
  renewalPeriodDays: number;

  @Column({ type: 'int', nullable: true })
  notificationDaysBefore: number;

  @Column({ type: 'text', nullable: true })
  terms: string;

  @Column({ type: 'json', nullable: true })
  customFields: Record<string, any>;

  @Column({ type: 'json', nullable: true })
  parties: {
    party1: {
      name: string;
      email: string;
      address?: string;
      phone?: string;
      representative?: string;
    };
    party2: {
      name: string;
      email: string;
      address?: string;
      phone?: string;
      representative?: string;
    };
  };

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  // Relations
  @ManyToOne(() => Company, { nullable: false })
  @JoinColumn({ name: 'companyId' })
  company: Company;

  @Column({ type: 'uuid' })
  companyId: string;

  @ManyToOne(() => User, { nullable: false })
  @JoinColumn({ name: 'createdById' })
  createdBy: User;

  @Column({ type: 'uuid' })
  createdById: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'assignedToId' })
  assignedTo: User;

  @Column({ type: 'uuid', nullable: true })
  assignedToId: string;

  @ManyToOne(() => ContractTemplate, { nullable: true })
  @JoinColumn({ name: 'templateId' })
  template: ContractTemplate;

  @Column({ type: 'uuid', nullable: true })
  templateId: string;

  @OneToMany(() => ContractVersion, (version) => version.contract, { cascade: true })
  versions: ContractVersion[];

  @OneToMany(() => ContractSignature, (signature) => signature.contract, { cascade: true })
  signatures: ContractSignature[];

  @OneToMany(() => ContractApproval, (approval) => approval.contract, { cascade: true })
  approvals: ContractApproval[];

  @OneToMany(() => ContractAttachment, (attachment) => attachment.contract, { cascade: true })
  attachments: ContractAttachment[];

  @OneToMany(() => ContractComment, (comment) => comment.contract, { cascade: true })
  comments: ContractComment[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Virtual fields
  get isExpired(): boolean {
    return this.endDate && new Date() > this.endDate;
  }

  get daysUntilExpiry(): number | null {
    if (!this.endDate) return null;
    const today = new Date();
    const diffTime = this.endDate.getTime() - today.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  get isNearExpiry(): boolean {
    const days = this.daysUntilExpiry;
    return days !== null && days <= (this.notificationDaysBefore || 30);
  }
}
