import { Suspense, lazy } from 'react';
import { RouteObject } from 'react-router-dom';

import { Loading } from '@/shared/components/common';
import MainLayout from '@/shared/layouts/MainLayout';

// Lazy load pages
const ProductListPage = lazy(() => import('./pages/ProductListPage'));
const ProductDetailPage = lazy(() => import('./pages/ProductDetailPage'));
const SegmentListPage = lazy(() => import('./pages/SegmentListPage'));

/**
 * Product module routes
 */
const productRoutes: RouteObject[] = [
  {
    path: '/product',
    element: (
      <MainLayout title="Sản phẩm">
        <Suspense fallback={<Loading />}>
          <ProductListPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/product/:id',
    element: (
      <MainLayout title="Chi tiết sản phẩm">
        <Suspense fallback={<Loading />}>
          <ProductDetailPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/product/segment',
    element: (
      <MainLayout title="Phân đoạn sản phẩm">
        <Suspense fallback={<Loading />}>
          <SegmentListPage />
        </Suspense>
      </MainLayout>
    ),
  },
];

export default productRoutes;
