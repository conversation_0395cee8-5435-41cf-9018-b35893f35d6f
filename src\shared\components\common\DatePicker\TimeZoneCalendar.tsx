import React, { useState, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { CalendarProps } from './types';
import Calendar from './Calendar';
import {
  // useCalendar, // Unused for now
  useCalendarResponsive,
  useCalendarTimeZone,
} from './hooks';

/**
 * Props cho TimeZoneCalendar component
 */
export interface TimeZoneCalendarProps extends CalendarProps {
  /**
   * Time zone mặc định
   */
  defaultTimeZone?: string;
  
  /**
   * Hiển thị time zone selector
   */
  showTimeZoneSelector?: boolean;
  
  /**
   * Hiển thị current time
   */
  showCurrentTime?: boolean;
  
  /**
   * Hiển thị multiple time zones
   */
  showMultipleTimeZones?: boolean;
  
  /**
   * Danh sách time zones để hiển thị
   */
  displayTimeZones?: string[];
  
  /**
   * Callback khi time zone thay đổi
   */
  onTimeZoneChange?: (timeZone: string) => void;
  
  /**
   * Format hiển thị thời gian
   */
  timeFormat?: string;
  
  /**
   * Hiển thị UTC time
   */
  showUTC?: boolean;
  
  /**
   * Compact mode cho time zone selector
   */
  compactTimeZoneSelector?: boolean;
}

/**
 * TimeZoneCalendar component với hỗ trợ multiple time zones
 */
const TimeZoneCalendar: React.FC<TimeZoneCalendarProps> = ({
  selectedDate,
  onSelectDate,
  month: propMonth,
  onMonthChange,
  defaultTimeZone,
  showTimeZoneSelector = true,
  showCurrentTime = true,
  showMultipleTimeZones = false,
  displayTimeZones = [],
  onTimeZoneChange,
  timeFormat = 'HH:mm:ss',
  showUTC = false,
  compactTimeZoneSelector = false,
  className = '',
  ...calendarProps
}) => {
  const { t } = useTranslation();
  
  // Hooks
  const responsive = useCalendarResponsive();
  // const { theme } = useCalendarTheme(); // Unused for now
  const timeZone = useCalendarTimeZone({
    defaultTimeZone,
    autoDetect: true,
    persistTimeZone: true,
  });

  // State cho current time
  const [currentTime, setCurrentTime] = useState(new Date());

  // Update current time every second
  React.useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // Calendar logic hook với time zone conversion
  // Commented out for now to avoid unused variable warning
  // const calendar = useCalendar({
  //   initialDate: propMonth,
  //   selectedDate: selectedDate ? timeZone.convertFromTimeZone(selectedDate) : null,
  //   onSelectDate: (date) => {
  //     const convertedDate = timeZone.convertToTimeZone(date);
  //     onSelectDate?.(convertedDate);
  //   },
  //   onMonthChange: (date) => {
  //     const convertedDate = timeZone.convertToTimeZone(date);
  //     onMonthChange?.(convertedDate);
  //   },
  // });

  // Handle time zone change
  const handleTimeZoneChange = useCallback((newTimeZone: string) => {
    timeZone.setTimeZone(newTimeZone);
    onTimeZoneChange?.(newTimeZone);
  }, [timeZone, onTimeZoneChange]);

  // Get display time zones
  const displayTimeZonesList = useMemo(() => {
    if (displayTimeZones.length > 0) {
      return displayTimeZones.map(tz => timeZone.getTimeZoneInfo(tz));
    }
    
    if (showMultipleTimeZones) {
      return timeZone.popularTimeZones.slice(0, 5);
    }
    
    return [timeZone.getTimeZoneInfo()];
  }, [displayTimeZones, showMultipleTimeZones, timeZone]);

  // Format time for display
  const formatTimeForTimeZone = useCallback((date: Date, targetTimeZone: string) => {
    return timeZone.formatInTargetTimeZone(date, targetTimeZone, timeFormat);
  }, [timeZone, timeFormat]);

  // Base classes
  const containerClasses = `
    bg-white dark:bg-gray-800 rounded-lg shadow-lg p-4
    ${responsive.compactMode ? 'w-72' : 'w-80'}
    ${className}
  `.trim();

  return (
    <div className={containerClasses}>
      {/* Time Zone Selector */}
      {showTimeZoneSelector && (
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {t('calendar.timeZone', 'Time Zone')}
          </label>
          
          {compactTimeZoneSelector ? (
            <select
              value={timeZone.timeZone}
              onChange={(e) => handleTimeZoneChange(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              {timeZone.popularTimeZones.map((tz) => (
                <option key={tz.id} value={tz.id}>
                  {tz.name} ({tz.offset})
                </option>
              ))}
            </select>
          ) : (
            <div className="grid grid-cols-1 gap-2 max-h-32 overflow-y-auto">
              {timeZone.popularTimeZones.map((tz) => (
                <button
                  key={tz.id}
                  onClick={() => handleTimeZoneChange(tz.id)}
                  className={`
                    text-left px-3 py-2 rounded-md transition-colors text-sm
                    ${tz.id === timeZone.timeZone
                      ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
                      : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'
                    }
                  `}
                >
                  <div className="flex justify-between items-center">
                    <span className="font-medium">{tz.name}</span>
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {tz.offset}
                    </span>
                  </div>
                </button>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Current Time Display */}
      {showCurrentTime && (
        <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {t('calendar.currentTime', 'Current Time')}
          </h4>
          
          <div className="space-y-2">
            {displayTimeZonesList.map((tz) => (
              <div key={tz.id} className="flex justify-between items-center">
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  {tz.name}
                </span>
                <span className="text-sm font-mono font-medium text-gray-900 dark:text-white">
                  {formatTimeForTimeZone(currentTime, tz.id)}
                </span>
              </div>
            ))}
            
            {showUTC && (
              <div className="flex justify-between items-center pt-2 border-t border-gray-200 dark:border-gray-600">
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  UTC
                </span>
                <span className="text-sm font-mono font-medium text-gray-900 dark:text-white">
                  {formatTimeForTimeZone(currentTime, 'UTC')}
                </span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Calendar */}
      <Calendar
        selectedDate={selectedDate}
        onSelectDate={onSelectDate}
        month={propMonth}
        onMonthChange={onMonthChange}
        {...calendarProps}
      />

      {/* Time Zone Info */}
      <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
          <span>
            {t('calendar.selectedTimeZone', 'Selected Time Zone')}:
          </span>
          <span className="font-medium">
            {timeZone.timeZoneName} ({timeZone.timeZoneOffset})
          </span>
        </div>
        
        {selectedDate && (
          <div className="mt-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">
                {t('calendar.selectedDate', 'Selected Date')}:
              </span>
              <span className="font-medium text-gray-900 dark:text-white">
                {timeZone.formatInCurrentTimeZone(selectedDate, 'yyyy-MM-dd HH:mm:ss')}
              </span>
            </div>
            
            {showUTC && (
              <div className="flex justify-between mt-1">
                <span className="text-gray-600 dark:text-gray-400">
                  UTC:
                </span>
                <span className="font-medium text-gray-900 dark:text-white">
                  {timeZone.formatInTargetTimeZone(selectedDate, 'UTC', 'yyyy-MM-dd HH:mm:ss')}
                </span>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default TimeZoneCalendar;
