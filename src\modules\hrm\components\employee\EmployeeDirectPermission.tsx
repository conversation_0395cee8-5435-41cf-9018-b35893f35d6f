import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { <PERSON>ton, Card, Checkbox, Typography } from '@/shared/components/common';

import { useUpdateEmployeePermission } from '../../hooks/useEmployeePermissions';
import { usePermissionGroups } from '../../hooks/usePermissions';
import { PermissionDto, PermissionGroupDto } from '../../types/permission.types';

interface EmployeeDirectPermissionProps {
  employeeId: number;
  onClose?: () => void;
}

/**
 * Component phân quyền trực tiếp cho nhân viên
 */
const EmployeeDirectPermission: React.FC<EmployeeDirectPermissionProps> = ({
  employeeId,
  onClose,
}) => {
  const { t } = useTranslation(['hrm', 'common']);
  const [selectedPermissionIds, setSelectedPermissionIds] = useState<number[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // L<PERSON><PERSON> danh sách nhóm quyền
  const { data: permissionGroupsData, isLoading: isLoadingPermissions } = usePermissionGroups();

  // Mutation để cập nhật quyền trực tiếp cho nhân viên
  const updatePermissionMutation = useUpdateEmployeePermission();

  // Xử lý khi chọn/bỏ chọn quyền
  const handlePermissionChange = (permissionId: number, checked: boolean) => {
    console.log('Permission change:', permissionId, checked);
    if (checked) {
      setSelectedPermissionIds(prev => [...prev, permissionId]);
    } else {
      setSelectedPermissionIds(prev => prev.filter(id => id !== permissionId));
    }
  };

  // Xử lý khi chọn/bỏ chọn tất cả quyền trong một nhóm
  const handleGroupChange = (groupKey: string, permissions: PermissionDto[], checked: boolean) => {
    console.log('Group change:', groupKey, checked);
    const permissionIds = permissions.map(p => Number(p.id));

    if (checked) {
      // Thêm tất cả quyền trong nhóm (nếu chưa có)
      setSelectedPermissionIds(prev => {
        const newPermissions = [...prev];
        for (const id of permissionIds) {
          if (!newPermissions.includes(id)) {
            newPermissions.push(id);
          }
        }
        return newPermissions;
      });
    } else {
      // Loại bỏ tất cả quyền trong nhóm
      setSelectedPermissionIds(prev => prev.filter(id => !permissionIds.includes(id)));
    }
  };

  // Kiểm tra xem một nhóm có được chọn hoàn toàn không
  const isGroupSelected = (permissions: PermissionDto[]) => {
    if (!permissions || permissions.length === 0) {return false;}
    const permissionIds = permissions.map(p => Number(p.id));
    return permissionIds.every(id => selectedPermissionIds.includes(id));
  };

  // Kiểm tra xem một nhóm có được chọn một phần không
  const isGroupPartiallySelected = (permissions: PermissionDto[]) => {
    if (!permissions || permissions.length === 0) {return false;}
    const permissionIds = permissions.map(p => Number(p.id));
    const hasSelected = permissionIds.some(id => selectedPermissionIds.includes(id));
    return hasSelected && !isGroupSelected(permissions);
  };

  // Xử lý khi lưu phân quyền
  const handleSave = async () => {
    if (!employeeId) {return;}

    setIsSubmitting(true);
    try {
      await updatePermissionMutation.mutateAsync({
        employeeId,
        data: { permissionIds: selectedPermissionIds },
      });

      if (onClose) {
        onClose();
      }
    } catch (error) {
      console.error('Error updating permissions:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-4">
      <div className="mb-4">
        <Typography variant="h4" className="mb-2 text-white dark:text-white">
          {t('hrm:employee.directPermission.title', 'Phân quyền trực tiếp cho nhân viên')}
        </Typography>
        <Typography variant="body2" className="text-white dark:text-white opacity-80">
          {t(
            'hrm:employee.directPermission.description',
            'Chọn các quyền để gán trực tiếp cho nhân viên này'
          )}
        </Typography>
      </div>

      {isLoadingPermissions ? (
        <div className="py-4 text-center">{t('common:loading', 'Đang tải...')}</div>
      ) : (
        <div className="space-y-4">
          {permissionGroupsData?.groups &&
            permissionGroupsData.groups.map((group: PermissionGroupDto) => (
              <Card key={group.key} className="p-4 mb-3 bg-primary-900/30 dark:bg-primary-900/30">
                <div className="mb-2">
                  <Checkbox
                    id={`group-${group.key}`}
                    checked={isGroupSelected(group.permissions || [])}
                    indeterminate={isGroupPartiallySelected(group.permissions || [])}
                    onChange={checked =>
                      handleGroupChange(group.key, group.permissions || [], checked)
                    }
                    variant="filled"
                  />
                  <label
                    htmlFor={`group-${group.key}`}
                    className="ml-2 font-medium text-base text-white dark:text-white"
                  >
                    {group.name}
                  </label>
                </div>
                <div className="ml-6 space-y-2 mt-2">
                  {group.permissions &&
                    group.permissions.map((permission: PermissionDto) => (
                      <div
                        key={permission.key}
                        className="flex items-center p-2 hover:bg-primary-800/50 dark:hover:bg-primary-800/50 rounded-md transition-colors"
                      >
                        <Checkbox
                          id={`permission-${permission.id}`}
                          checked={selectedPermissionIds.includes(Number(permission.id))}
                          onChange={checked =>
                            handlePermissionChange(Number(permission.id), checked)
                          }
                          variant="filled"
                        />
                        <label
                          htmlFor={`permission-${permission.id}`}
                          className="ml-2 text-sm text-white dark:text-white"
                        >
                          {permission.name}
                          {permission.description && (
                            <span className="text-xs text-white dark:text-white opacity-80 ml-1">
                              ({permission.description})
                            </span>
                          )}
                        </label>
                      </div>
                    ))}
                </div>
              </Card>
            ))}

          {(!permissionGroupsData?.groups || permissionGroupsData.groups.length === 0) && (
            <div className="py-4 text-center text-white dark:text-white">
              {t('hrm:permission.noPermissions', 'Không có quyền nào')}
            </div>
          )}
        </div>
      )}

      <div className="flex justify-end space-x-2 mt-4">
        {onClose && (
          <Button variant="outline" onClick={onClose} className="text-white dark:text-white">
            {t('common:cancel', 'Hủy')}
          </Button>
        )}
        <Button onClick={handleSave} disabled={isSubmitting} className="bg-primary text-white">
          {isSubmitting ? t('common:saving', 'Đang lưu...') : t('common:save', 'Lưu')}
        </Button>
      </div>
    </div>
  );
};

export default EmployeeDirectPermission;
