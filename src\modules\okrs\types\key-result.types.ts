import { SortDirection } from '@/shared/dto/request/query.dto';
import { SubmitHandler } from 'react-hook-form';

// Enum cho trạng thái key result
export enum KeyResultStatus {
  ACTIVE = 'ACTIVE',
  COMPLETED = 'COMPLETED',
  PAUSED = 'PAUSED',
  CANCELED = 'CANCELED',
}

// Enum cho tần suất check-in
export enum CheckInFrequency {
  DAILY = 'DAILY',
  WEEKLY = 'WEEKLY',
  MONTHLY = 'MONTHLY',
}

// Interface cho dữ liệu key result
export interface KeyResultDto {
  id: number;
  objectiveId: number | null;
  title: string;
  description: string | null;
  targetValue: number;
  currentValue: number | null;
  startValue: number | null;
  unit: string | null;
  format: string | null;
  progress: number | null;
  status: KeyResultStatus | null;
  measurementMethod: string | null;
  weight: number | null;
  checkInFrequency: CheckInFrequency | null;
  createdAt: number | null;
  updatedAt: number | null;
  supportingKeyResults?: KeyResultSupportInfoDto[];
}

// Interface cho thông tin key result hỗ trợ
export interface KeyResultSupportInfoDto {
  id: number;
  title: string;
  objectiveId: number | null;
}

// Interface cho tham số truy vấn danh sách key results
export interface KeyResultQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortDirection?: SortDirection;
  objectiveId?: number;
  status?: KeyResultStatus;
}

// Interface cho dữ liệu tạo key result mới
export interface CreateKeyResultDto {
  objectiveId: number;
  title: string;
  description?: string;
  targetValue: number;
  startValue?: number;
  unit?: string;
  format?: string;
  measurementMethod?: string;
  weight?: number;
  checkInFrequency?: CheckInFrequency;
  supportingKeyResultIds?: number[];
}

// Interface cho dữ liệu cập nhật key result
export interface UpdateKeyResultDto {
  title?: string;
  description?: string;
  targetValue?: number;
  currentValue?: number;
  startValue?: number;
  unit?: string;
  format?: string;
  status?: KeyResultStatus;
  measurementMethod?: string;
  weight?: number;
  checkInFrequency?: CheckInFrequency;
  supportingKeyResultIds?: number[];
}

// Interface cho form thêm key result
export interface KeyResultFormData {
  objectiveId: number;
  title: string;
  description?: string;
  targetValue: number;
  startValue?: number;
  unit?: string;
  format?: string;
  measurementMethod?: string;
  weight?: number;
  checkInFrequency?: CheckInFrequency;
  supportingKeyResultId?: number; // Thêm trường key result phụ trợ
}

// Type cho hàm xử lý submit form
export type KeyResultSubmitHandler = SubmitHandler<KeyResultFormData>;
