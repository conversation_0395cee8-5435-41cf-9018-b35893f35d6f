import React, { useRef } from 'react';
import {
  Input,
  Textarea,
  DatePicker,
  Icon,
  Button,
  Modal,
  FormGrid,
  Toggle,
  FormItem,
  ScrollArea,
  IconButton,
} from '@/shared/components/common';
import type { IconName } from '@/shared/components/common/Icon';
import { CalendarEvent } from '../../types';
import { useTranslation } from 'react-i18next';

export interface EventFormProps {
  /**
   * Sự kiện đang được chỉnh sửa hoặc tạo mới
   */
  event: CalendarEvent;

  /**
   * <PERSON>h sách các loại sự kiện
   */
  eventTypes: Array<{
    value: string;
    label: string;
    color: string;
    icon: IconName;
  }>;

  /**
   * Trạng thái hiển thị của modal
   */
  isOpen: boolean;

  /**
   * Tiêu đề của modal
   */
  title: string;

  /**
   * Callback khi đóng modal
   */
  onClose: () => void;

  /**
   * Callback khi lưu sự kiện
   */
  onSave: () => void;

  /**
   * Callback khi cập nhật trường của sự kiện
   */
  onUpdateField: (field: keyof CalendarEvent, value: string | Date | boolean | undefined) => void;

  /**
   * Callback khi cập nhật loại sự kiện
   */
  onUpdateEventType: (
    type: 'meeting' | 'appointment' | 'deadline' | 'training' | 'workshop' | 'planning' | 'event' | 'leave' | 'client' | 'personal'
  ) => void;
}

/**
 * Component form thêm/sửa sự kiện
 */
const EventForm: React.FC<EventFormProps> = ({
  event,
  eventTypes,
  isOpen,
  title,
  onClose,
  onSave,
  onUpdateField,
  onUpdateEventType,
}) => {
  const { t } = useTranslation();
  const scrollRef = useRef<HTMLDivElement>(null);

  // Scroll functions for event types
  const scrollLeft = () => {
    if (scrollRef.current) {
      const scrollAmount = 200;
      scrollRef.current.scrollBy({ left: -scrollAmount, behavior: 'smooth' });
    }
  };

  const scrollRight = () => {
    if (scrollRef.current) {
      const scrollAmount = 200;
      scrollRef.current.scrollBy({ left: scrollAmount, behavior: 'smooth' });
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      size="md"
      closeOnEsc={true}
      footer={
        <div className="flex justify-end gap-2">
          <Button
            variant="outline"
            onClick={onClose}
            size="sm"
            className="px-3 py-1 rounded-md hover:bg-card-hover transition-colors"
          >
            <span className="text-sm">{t('common.cancel', 'Hủy')}</span>
          </Button>
          <Button
            variant="primary"
            disabled={!event.title.trim()}
            onClick={onSave}
            leftIcon={<Icon name="save" size="xs" />}
            size="sm"
            className="px-3 py-1 rounded-md shadow-sm hover:shadow transition-all"
          >
            <span className="text-sm">{t('common.save', 'Lưu')}</span>
          </Button>
        </div>
      }
    >
      <ScrollArea height="400px" autoHide={false} className="pr-2">
        <div className="space-y-4">
        {/* Tiêu đề sự kiện */}
        <FormItem label={t('calendar.eventTitle', 'Tiêu đề')} required>
          <Input
            id="event-title"
            value={event.title}
            onChange={e => onUpdateField('title', e.target.value)}
            placeholder={t('calendar.enterTitle', 'Nhập tiêu đề sự kiện')}
            fullWidth
            autoFocus
          />
        </FormItem>

        {/* Loại sự kiện */}
        <FormItem label={t('calendar.eventType', 'Loại sự kiện')}>
          <div className="relative">
            {/* Navigation buttons */}
            <div className="absolute left-0 top-1/2 transform -translate-y-1/2 z-10">
              <IconButton
                icon="chevron-left"
                onClick={scrollLeft}
                variant="outline"
                size="sm"
                className="bg-white/90 shadow-sm"
              />
            </div>
            <div className="absolute right-0 top-1/2 transform -translate-y-1/2 z-10">
              <IconButton
                icon="chevron-right"
                onClick={scrollRight}
                variant="outline"
                size="sm"
                className="bg-white/90 shadow-sm"
              />
            </div>

            {/* Scrollable event types */}
            <ScrollArea
              ref={scrollRef}
              direction="horizontal"
              autoHide={false}
              className="mx-8"
              height="120px"
            >
              <div className="flex gap-3 py-2" style={{ minWidth: 'max-content' }}>
                {eventTypes.map(type => {
                  const isSelected = event.extendedProps?.type === type.value;
                  const colorClass = type.color;

                  return (
                    <div
                      key={type.value}
                      onClick={() =>
                        onUpdateEventType(
                          type.value as
                            | 'meeting'
                            | 'appointment'
                            | 'deadline'
                            | 'training'
                            | 'workshop'
                            | 'planning'
                            | 'event'
                            | 'leave'
                            | 'client'
                            | 'personal'
                        )
                      }
                      className={`
                        cursor-pointer rounded-lg p-3 flex flex-col items-center gap-2 transition-all min-h-[80px] min-w-[100px] flex-shrink-0
                        ${
                          isSelected
                            ? `bg-${colorClass}/10 shadow-sm ring-2 ring-${colorClass}/30`
                            : 'bg-card hover:bg-card-hover hover:shadow-sm'
                        }
                      `}
                      title={type.label}
                    >
                      <Icon
                        name={type.icon}
                        size="md"
                        className={isSelected ? `text-${colorClass}` : 'text-muted-foreground'}
                      />
                      <span className={`text-xs text-center font-medium ${
                        isSelected ? `text-${colorClass}` : 'text-foreground'
                      }`}>
                        {type.label}
                      </span>
                    </div>
                  );
                })}
              </div>
            </ScrollArea>
          </div>
        </FormItem>

        {/* Thời gian */}
        <div className="bg-card rounded-lg p-3">
          <div className="flex items-center justify-between mb-2">
            <label className="text-sm font-medium">{t('calendar.dateAndTime', 'Thời gian')}</label>
            <div className="flex items-center gap-2">
              <span className="text-xs">{t('calendar.allDay', 'Cả ngày')}</span>
              <Toggle
                checked={event.allDay || false}
                onChange={checked => onUpdateField('allDay', checked)}
                size="sm"
              />
            </div>
          </div>

          <FormGrid columns={2} columnsSm={1} gap="md" className="mt-2">
            <div>
              <label className="block text-xs mb-1">{t('calendar.startDate', 'Bắt đầu')}</label>
              <DatePicker
                value={typeof event.start === 'string' ? new Date(event.start) : event.start}
                onChange={date => onUpdateField('start', date || new Date())}
                format={event.allDay ? 'dd/MM/yyyy' : 'dd/MM/yyyy HH:mm'}
                showCalendarIcon={true}
                clearable={false}
                className="w-full"
              />
            </div>

            <div>
              <label className="block text-xs mb-1">{t('calendar.endDate', 'Kết thúc')}</label>
              <DatePicker
                value={
                  event.end
                    ? typeof event.end === 'string'
                      ? new Date(event.end)
                      : event.end
                    : undefined
                }
                onChange={date => onUpdateField('end', date || undefined)}
                format={event.allDay ? 'dd/MM/yyyy' : 'dd/MM/yyyy HH:mm'}
                showCalendarIcon={true}
                clearable={false}
                minDate={typeof event.start === 'string' ? new Date(event.start) : event.start}
                className="w-full"
              />
            </div>
          </FormGrid>
        </div>

        {/* Địa điểm và Mô tả */}
        <FormGrid columns={2} columnsSm={1} gap="md">
          <FormItem label={t('calendar.location', 'Địa điểm')}>
            <Input
              id="event-location"
              value={event.location || ''}
              onChange={e => onUpdateField('location', e.target.value)}
              placeholder={t('calendar.enterLocation', 'Nhập địa điểm')}
              fullWidth
              leftIcon={<Icon name="map-pin" size="sm" />}
            />
          </FormItem>

          <FormItem label={t('calendar.description', 'Mô tả')}>
            <Textarea
              id="event-description"
              value={event.description || ''}
              onChange={e => onUpdateField('description', e.target.value)}
              placeholder={t('calendar.enterDescription', 'Nhập mô tả')}
              rows={1}
              fullWidth
              className="resize-none"
            />
          </FormItem>
        </FormGrid>
        </div>
      </ScrollArea>
    </Modal>
  );
};

export default EventForm;
