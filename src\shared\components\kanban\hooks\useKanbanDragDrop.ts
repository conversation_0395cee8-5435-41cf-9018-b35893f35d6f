import { useCallback } from 'react';
import { useDrag, useDrop } from 'react-dnd';

import {
  DragItemType,
  CardDragItem,
  ColumnDragItem,
  KanbanCard,
  KanbanColumn,
  KanbanMoveResult,
} from '../types/kanban.types';

/**
 * Hook để xử lý drag & drop cho thẻ Kanban
 */
export const useCardDragDrop = (
  card: KanbanCard,
  columnId: string,
  index: number,
  onCardMove?: (result: KanbanMoveResult) => void,
  allowDrag = true
) => {
  // Cấu hình drag source
  const [{ isDragging }, dragRef] = useDrag({
    type: DragItemType.CARD,
    item: (): CardDragItem => ({
      type: DragItemType.CARD,
      id: card.id,
      columnId,
      index,
      card,
    }),
    collect: monitor => ({
      isDragging: monitor.isDragging(),
    }),
    canDrag: allowDrag,
  });

  // Cấu hình drop target
  const [{ isOver, canDrop }, dropRef] = useDrop({
    accept: DragItemType.CARD,
    hover: (item: CardDragItem, monitor) => {
      // Không làm gì nếu hover vào chính nó
      if (item.id === card.id) {
        return;
      }

      // Không làm gì nếu không có callback
      if (!onCardMove) {
        return;
      }

      // Chỉ di chuyển khi con trỏ vượt qua nửa chiều cao của item
      const dragIndex = item.index;
      const hoverIndex = index;

      // Xác định vị trí của con trỏ
      const hoverBoundingRect = dropRef.current?.getBoundingClientRect();
      if (!hoverBoundingRect) {return;}

      const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
      const clientOffset = monitor.getClientOffset();
      if (!clientOffset) {return;}

      const hoverClientY = clientOffset.y - hoverBoundingRect.top;

      // Chỉ thực hiện di chuyển khi con trỏ vượt qua nửa chiều cao
      if (
        (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) ||
        (dragIndex > hoverIndex && hoverClientY > hoverMiddleY)
      ) {
        return;
      }

      // Thực hiện di chuyển
      onCardMove({
        cardId: item.id,
        sourceColumnId: item.columnId,
        targetColumnId: columnId,
        newIndex: hoverIndex,
      });

      // Cập nhật vị trí của item đang kéo
      item.index = hoverIndex;
      item.columnId = columnId;
    },
    collect: monitor => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop(),
    }),
  });

  // Kết hợp ref cho cả drag và drop
  const ref = useCallback(
    (node: HTMLElement | null) => {
      dragRef(dropRef(node));
    },
    [dragRef, dropRef]
  );

  return { ref, isDragging, isOver, canDrop };
};

/**
 * Hook để xử lý drop cho cột Kanban
 */
export const useColumnDrop = (
  columnId: string,
  onCardMove?: (result: KanbanMoveResult) => void
) => {
  const [{ isOver, canDrop }, dropRef] = useDrop({
    accept: DragItemType.CARD,
    hover: (item: CardDragItem, monitor) => {
      // Bỏ qua nếu đang hover vào cột hiện tại
      if (item.columnId === columnId) {
        return;
      }

      // Bỏ qua nếu không có callback
      if (!onCardMove) {
        return;
      }

      // Cập nhật columnId của item để chuẩn bị cho việc drop
      // Điều này giúp khi kéo thẻ qua nhiều cột
      const clientOffset = monitor.getClientOffset();
      const hoverBoundingRect = dropRef.current?.getBoundingClientRect();

      if (clientOffset && hoverBoundingRect) {
        // Kiểm tra xem con trỏ có nằm trong phạm vi của cột không
        const isOverColumn =
          clientOffset.x >= hoverBoundingRect.left &&
          clientOffset.x <= hoverBoundingRect.right &&
          clientOffset.y >= hoverBoundingRect.top &&
          clientOffset.y <= hoverBoundingRect.bottom;

        if (isOverColumn) {
          // Cập nhật columnId của item
          item.columnId = columnId;
        }
      }
    },
    drop: (item: CardDragItem) => {
      // Bỏ qua nếu thả vào cột hiện tại
      if (item.columnId === columnId) {
        return;
      }

      // Bỏ qua nếu không có callback
      if (!onCardMove) {
        return;
      }

      // Thực hiện di chuyển thẻ sang cột mới
      onCardMove({
        cardId: item.id,
        sourceColumnId: item.columnId,
        targetColumnId: columnId,
        newIndex: 0, // Thêm vào đầu cột
      });

      // Thêm hiệu ứng khi thả thẻ
      const dropElement = dropRef.current;
      if (dropElement) {
        // Thêm class để tạo hiệu ứng
        dropElement.classList.add('drop-success');

        // Xóa class sau khi hiệu ứng hoàn thành
        setTimeout(() => {
          dropElement.classList.remove('drop-success');
        }, 500);
      }

      return { moved: true };
    },
    collect: monitor => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop(),
    }),
  });

  return { dropRef, isOver, canDrop };
};

/**
 * Hook để xử lý drag & drop cho cột Kanban
 */
export const useColumnDragDrop = (
  column: KanbanColumn,
  index: number,
  onColumnReorder?: (columnId: string, newIndex: number) => void,
  allowDrag = true
) => {
  // Cấu hình drag source
  const [{ isDragging }, dragRef] = useDrag({
    type: DragItemType.COLUMN,
    item: (): ColumnDragItem => ({
      type: DragItemType.COLUMN,
      id: column.id,
      index,
      column,
    }),
    collect: monitor => ({
      isDragging: monitor.isDragging(),
    }),
    canDrag: allowDrag,
  });

  // Cấu hình drop target
  const [{ isOver }, dropRef] = useDrop({
    accept: DragItemType.COLUMN,
    hover: (item: ColumnDragItem, monitor) => {
      // Không làm gì nếu hover vào chính nó
      if (item.id === column.id) {
        return;
      }

      // Không làm gì nếu không có callback
      if (!onColumnReorder) {
        return;
      }

      // Chỉ di chuyển khi con trỏ vượt qua nửa chiều rộng của cột
      const dragIndex = item.index;
      const hoverIndex = index;

      // Xác định vị trí của con trỏ
      const hoverBoundingRect = dropRef.current?.getBoundingClientRect();
      if (!hoverBoundingRect) {return;}

      const hoverMiddleX = (hoverBoundingRect.right - hoverBoundingRect.left) / 2;
      const clientOffset = monitor.getClientOffset();
      if (!clientOffset) {return;}

      const hoverClientX = clientOffset.x - hoverBoundingRect.left;

      // Chỉ thực hiện di chuyển khi con trỏ vượt qua nửa chiều rộng
      if (
        (dragIndex < hoverIndex && hoverClientX < hoverMiddleX) ||
        (dragIndex > hoverIndex && hoverClientX > hoverMiddleX)
      ) {
        return;
      }

      // Thực hiện di chuyển
      onColumnReorder(item.id, hoverIndex);

      // Cập nhật vị trí của item đang kéo
      item.index = hoverIndex;
    },
    collect: monitor => ({
      isOver: monitor.isOver(),
    }),
  });

  // Kết hợp ref cho cả drag và drop
  const ref = useCallback(
    (node: HTMLElement | null) => {
      dragRef(dropRef(node));
    },
    [dragRef, dropRef]
  );

  return { ref, isDragging, isOver };
};
