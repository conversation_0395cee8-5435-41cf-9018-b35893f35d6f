/**
 * Form Synchronization Types for WebSocket Chat
 */

import { FormSchema, FormField } from './chat-message.types';

// Form sync configuration
export interface FormSyncConfig {
  formId: string;
  messageId: string;
  roomId: string;
  enableRealTimeSync: boolean;
  enableValidation: boolean;
  syncDebounceMs: number;
  validationDebounceMs: number;
}

// Form field sync event
export interface FormFieldSyncEvent {
  formId: string;
  messageId: string;
  fieldName: string;
  fieldValue: unknown;
  timestamp: string;
  source: 'user' | 'server' | 'system';
  userId?: string;
}

// Form validation event
export interface FormValidationEvent {
  formId: string;
  messageId: string;
  fieldName?: string; // undefined means validate entire form
  formData: Record<string, unknown>;
  timestamp: string;
}

// Form validation result
export interface FormValidationResult {
  formId: string;
  messageId: string;
  isValid: boolean;
  errors: Record<string, string>;
  warnings?: Record<string, string>;
  fieldName?: string;
  timestamp: string;
}

// Form submission event
export interface FormSubmissionEvent {
  formId: string;
  messageId: string;
  formData: Record<string, unknown>;
  timestamp: string;
  userId: string;
  submitEndpoint: string;
  submitMethod: string;
}

// Form submission result
export interface FormSubmissionResult {
  formId: string;
  messageId: string;
  success: boolean;
  data?: unknown;
  error?: string;
  validationErrors?: Record<string, string>;
  timestamp: string;
}

// Form prefill event
export interface FormPrefillEvent {
  formId: string;
  messageId: string;
  data: Record<string, unknown>;
  source: 'server' | 'api' | 'cache';
  timestamp: string;
}

// Form state
export interface FormState {
  formId: string;
  messageId: string;
  data: Record<string, unknown>;
  errors: Record<string, string>;
  warnings: Record<string, string>;
  isValid: boolean;
  isDirty: boolean;
  isSubmitting: boolean;
  lastUpdated: string;
  lastValidated: string;
}

// Form field state
export interface FormFieldState {
  name: string;
  value: unknown;
  error?: string;
  warning?: string;
  isValid: boolean;
  isDirty: boolean;
  isTouched: boolean;
  isValidating: boolean;
  lastUpdated: string;
}

// Form sync manager interface
export interface FormSyncManager {
  // Configuration
  configure(config: FormSyncConfig): void;
  
  // Field operations
  updateField(fieldName: string, value: unknown): void;
  getFieldValue(fieldName: string): unknown;
  getFieldState(fieldName: string): FormFieldState | undefined;
  
  // Form operations
  getFormData(): Record<string, unknown>;
  setFormData(data: Record<string, unknown>): void;
  getFormState(): FormState;
  resetForm(): void;
  
  // Validation
  validateField(fieldName: string): Promise<FormValidationResult>;
  validateForm(): Promise<FormValidationResult>;
  
  // Submission
  submitForm(): Promise<FormSubmissionResult>;
  
  // Events
  onFieldChange(callback: (event: FormFieldSyncEvent) => void): () => void;
  onValidation(callback: (result: FormValidationResult) => void): () => void;
  onSubmission(callback: (result: FormSubmissionResult) => void): () => void;
  onPrefill(callback: (event: FormPrefillEvent) => void): () => void;
  
  // Cleanup
  destroy(): void;
}

// Form sync hook options
export interface UseFormSyncOptions {
  formId: string;
  messageId: string;
  roomId: string;
  schema: FormSchema;
  initialData?: Record<string, unknown>;
  enableRealTimeSync?: boolean;
  enableValidation?: boolean;
  syncDebounceMs?: number;
  validationDebounceMs?: number;
  onSubmit?: (data: Record<string, unknown>) => Promise<unknown>;
  onValidation?: (result: FormValidationResult) => void;
  onError?: (error: string) => void;
}

// Form sync hook return type
export interface UseFormSyncReturn {
  // Form state
  formData: Record<string, unknown>;
  formState: FormState;
  fieldStates: Record<string, FormFieldState>;
  
  // Form operations
  updateField: (fieldName: string, value: unknown) => void;
  setFormData: (data: Record<string, unknown>) => void;
  resetForm: () => void;
  
  // Validation
  validateField: (fieldName: string) => Promise<FormValidationResult>;
  validateForm: () => Promise<FormValidationResult>;
  
  // Submission
  submitForm: () => Promise<FormSubmissionResult>;
  
  // Status
  isValid: boolean;
  isDirty: boolean;
  isSubmitting: boolean;
  isConnected: boolean;
  
  // Errors
  errors: Record<string, string>;
  warnings: Record<string, string>;
}

// Form field component props
export interface FormFieldProps {
  name: string;
  field: FormField;
  value: unknown;
  error?: string;
  warning?: string;
  onChange: (value: unknown) => void;
  onBlur?: () => void;
  onFocus?: () => void;
  disabled?: boolean;
  readOnly?: boolean;
}

// Form renderer props
export interface FormRendererProps {
  formId: string;
  messageId: string;
  roomId: string;
  schema: FormSchema;
  title?: string;
  description?: string;
  initialData?: Record<string, unknown>;
  onSubmit?: (data: Record<string, unknown>) => Promise<unknown>;
  onCancel?: () => void;
  className?: string;
  disabled?: boolean;
  readOnly?: boolean;
}

// Form validation rule
export interface FormValidationRule {
  type: 'required' | 'minLength' | 'maxLength' | 'min' | 'max' | 'pattern' | 'email' | 'url' | 'custom';
  value?: unknown;
  message: string;
  validator?: (value: unknown, formData: Record<string, unknown>) => boolean | Promise<boolean>;
}

// Form field validation rules
export interface FormFieldValidationRules {
  [fieldName: string]: FormValidationRule[];
}

// Form validation context
export interface FormValidationContext {
  formId: string;
  messageId: string;
  schema: FormSchema;
  rules: FormFieldValidationRules;
  formData: Record<string, unknown>;
  fieldName?: string;
}

// Form sync error types
export type FormSyncErrorType = 
  | 'CONNECTION_ERROR'
  | 'VALIDATION_ERROR'
  | 'SUBMISSION_ERROR'
  | 'SCHEMA_ERROR'
  | 'FIELD_ERROR'
  | 'TIMEOUT_ERROR'
  | 'PERMISSION_ERROR';

// Form sync error
export interface FormSyncError {
  type: FormSyncErrorType;
  message: string;
  details?: Record<string, unknown>;
  fieldName?: string;
  timestamp: string;
}

// Form sync event types
export type FormSyncEventType = 
  | 'field_change'
  | 'field_validation'
  | 'form_validation'
  | 'form_submission'
  | 'form_prefill'
  | 'form_reset'
  | 'form_error';

// Form sync event
export interface FormSyncEvent {
  type: FormSyncEventType;
  formId: string;
  messageId: string;
  data: unknown;
  timestamp: string;
  userId?: string;
}

// Export all types
export type {
  FormSyncConfig,
  FormFieldSyncEvent,
  FormValidationEvent,
  FormValidationResult,
  FormSubmissionEvent,
  FormSubmissionResult,
  FormPrefillEvent,
  FormState,
  FormFieldState,
  FormSyncManager,
  UseFormSyncOptions,
  UseFormSyncReturn,
  FormFieldProps,
  FormRendererProps,
  FormValidationRule,
  FormFieldValidationRules,
  FormValidationContext,
  FormSyncErrorType,
  FormSyncError,
  FormSyncEventType,
  FormSyncEvent,
};
