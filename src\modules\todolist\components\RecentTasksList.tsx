import React from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import { Badge, Spinner, Typography, IconButton, Tooltip } from '@/shared/components/common';
import { formatDate } from '@/shared/utils/date';

import { TaskDto, TaskStatus, TaskPriority } from '../types/task.types';

interface RecentTasksListProps {
  tasks: TaskDto[];
  isLoading: boolean;
}

/**
 * Component to display a list of recent tasks
 */
const RecentTasksList: React.FC<RecentTasksListProps> = ({ tasks, isLoading }) => {
  const { t } = useTranslation(['common', 'todolist']);
  const navigate = useNavigate();

  // Get status badge color
  const getStatusBadgeColor = (status: TaskStatus | null) => {
    switch (status) {
      case TaskStatus.PENDING:
        return 'bg-gray-100 text-gray-800';
      case TaskStatus.IN_PROGRESS:
        return 'bg-blue-100 text-blue-800';
      case TaskStatus.COMPLETED:
      case TaskStatus.APPROVED:
        return 'bg-green-100 text-green-800';
      case TaskStatus.REJECTED:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get priority badge color
  const getPriorityBadgeColor = (priority: TaskPriority | null) => {
    switch (priority) {
      case TaskPriority.LOW:
        return 'bg-green-100 text-green-800';
      case TaskPriority.MEDIUM:
        return 'bg-blue-100 text-blue-800';
      case TaskPriority.HIGH:
        return 'bg-orange-100 text-orange-800';
      case TaskPriority.URGENT:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get status text
  const getStatusText = (status: TaskStatus | null) => {
    switch (status) {
      case TaskStatus.PENDING:
        return t('todolist:task.status.todo', 'To Do');
      case TaskStatus.IN_PROGRESS:
        return t('todolist:task.status.inProgress', 'In Progress');
      case TaskStatus.COMPLETED:
      case TaskStatus.APPROVED:
        return t('todolist:task.status.done', 'Done');
      case TaskStatus.REJECTED:
        return t('todolist:task.status.cancelled', 'Cancelled');
      default:
        return '';
    }
  };

  // Get priority text
  const getPriorityText = (priority: TaskPriority | null) => {
    switch (priority) {
      case TaskPriority.LOW:
        return t('todolist:task.priority.low', 'Low');
      case TaskPriority.MEDIUM:
        return t('todolist:task.priority.medium', 'Medium');
      case TaskPriority.HIGH:
        return t('todolist:task.priority.high', 'High');
      case TaskPriority.URGENT:
        return t('todolist:task.priority.urgent', 'Urgent');
      default:
        return '';
    }
  };

  // Handle task click
  const handleTaskClick = (taskId: number) => {
    navigate(`/todolist/tasks/${taskId}`);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-32">
        <Spinner size="md" />
      </div>
    );
  }

  if (tasks.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        {t('todolist:dashboard.noTasks', 'No tasks found')}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {tasks.map(task => (
        <div
          key={task.id}
          className="border rounded-lg p-3 hover:bg-gray-50 cursor-pointer"
          onClick={() => handleTaskClick(task.id)}
        >
          <div className="flex justify-between items-start">
            <div>
              <Typography variant="subtitle1" className="font-medium">
                {task.title}
              </Typography>
              {task.description && (
                <Typography variant="body2" className="text-gray-500 mt-1 line-clamp-2">
                  {task.description}
                </Typography>
              )}
            </div>
            <div className="flex space-x-2">
              <Badge className={getStatusBadgeColor(task.status)}>
                {getStatusText(task.status)}
              </Badge>
              {task.priority && (
                <Badge className={getPriorityBadgeColor(task.priority)}>
                  {getPriorityText(task.priority)}
                </Badge>
              )}
            </div>
          </div>
          <div className="flex justify-between items-center mt-2">
            <div className="text-xs text-gray-500">
              {t('todolist:task.fields.createdAt', 'Created')}: {formatDate(task.createdAt || 0)}
            </div>
            <div className="flex items-center">
              {task.assigneeId && (
                <div className="text-xs text-gray-500 mr-2">
                  {t('todolist:task.fields.assignee', 'Assignee')}: ID {task.assigneeId}
                </div>
              )}
              <Tooltip content={t('todolist:task.viewDetails', 'View Details')}>
                <IconButton
                  icon="eye"
                  size="sm"
                  variant="ghost"
                  onClick={e => {
                    e.stopPropagation();
                    handleTaskClick(task.id);
                  }}
                />
              </Tooltip>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default RecentTasksList;
