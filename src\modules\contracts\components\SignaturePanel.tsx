import React from 'react';
import { useTranslation } from 'react-i18next';

import { Card, Typography } from '@/shared/components/common';

/**
 * Signature Panel Component
 * Component panel ký số điện tử
 */
const SignaturePanel: React.FC = () => {
  const { t } = useTranslation(['common', 'contracts']);

  return (
    <Card className="p-6">
      <Typography variant="h2" className="mb-4">
        {t('contracts:sign')}
      </Typography>
      <div className="text-center py-8">
        <Typography variant="body1" className="text-muted-foreground">
          Panel ký số điện tử đang được phát triển...
        </Typography>
      </div>
    </Card>
  );
};

export default SignaturePanel;
