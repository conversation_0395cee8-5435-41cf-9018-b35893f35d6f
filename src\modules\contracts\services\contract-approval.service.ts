import { apiClient } from '@/shared/api/axios';
import type {
  ContractApproval,
  ApprovalQueryParams,
  ApprovalCreateDto,
  ApprovalUpdateDto,
  ApprovalActionDto,
  ApprovalRejectDto,
  ApprovalDelegateDto,
  ApprovalListResponse,
  ApprovalWorkflow,
} from '../types/contract-approval.types';

/**
 * Contract Approval API Service
 */
export class ContractApprovalService {
  private static readonly BASE_URL = '/api/contracts/approvals';

  /**
   * Get approvals with pagination and filtering
   */
  static async getApprovals(params?: ApprovalQueryParams): Promise<ApprovalListResponse> {
    const response = await apiClient.get(this.BASE_URL, { params });
    return response.data;
  }

  /**
   * Get approval by ID
   */
  static async getApproval(id: string): Promise<ContractApproval> {
    const response = await apiClient.get(`${this.BASE_URL}/${id}`);
    return response.data;
  }

  /**
   * Get approvals for a contract
   */
  static async getContractApprovals(contractId: string): Promise<ContractApproval[]> {
    const response = await apiClient.get(`/api/contracts/${contractId}/approvals`);
    return response.data;
  }

  /**
   * Get approval workflow for a contract
   */
  static async getApprovalWorkflow(contractId: string): Promise<ApprovalWorkflow> {
    const response = await apiClient.get(`/api/contracts/${contractId}/approval-workflow`);
    return response.data;
  }

  /**
   * Create new approval step
   */
  static async createApproval(data: ApprovalCreateDto): Promise<ContractApproval> {
    const response = await apiClient.post(this.BASE_URL, data);
    return response.data;
  }

  /**
   * Update approval
   */
  static async updateApproval(id: string, data: ApprovalUpdateDto): Promise<ContractApproval> {
    const response = await apiClient.put(`${this.BASE_URL}/${id}`, data);
    return response.data;
  }

  /**
   * Delete approval
   */
  static async deleteApproval(id: string): Promise<void> {
    await apiClient.delete(`${this.BASE_URL}/${id}`);
  }

  /**
   * Approve contract
   */
  static async approveContract(id: string, data?: ApprovalActionDto): Promise<ContractApproval> {
    const response = await apiClient.post(`${this.BASE_URL}/${id}/approve`, data);
    return response.data;
  }

  /**
   * Reject contract
   */
  static async rejectContract(id: string, data: ApprovalRejectDto): Promise<ContractApproval> {
    const response = await apiClient.post(`${this.BASE_URL}/${id}/reject`, data);
    return response.data;
  }

  /**
   * Delegate approval
   */
  static async delegateApproval(id: string, data: ApprovalDelegateDto): Promise<ContractApproval> {
    const response = await apiClient.post(`${this.BASE_URL}/${id}/delegate`, data);
    return response.data;
  }

  /**
   * Send approval reminder
   */
  static async sendApprovalReminder(id: string, message?: string): Promise<void> {
    await apiClient.post(`${this.BASE_URL}/${id}/remind`, { message });
  }

  /**
   * Cancel approval request
   */
  static async cancelApprovalRequest(id: string, reason?: string): Promise<ContractApproval> {
    const response = await apiClient.post(`${this.BASE_URL}/${id}/cancel`, { reason });
    return response.data;
  }

  /**
   * Restart approval workflow
   */
  static async restartApprovalWorkflow(contractId: string): Promise<ApprovalWorkflow> {
    const response = await apiClient.post(`/api/contracts/${contractId}/restart-approval`);
    return response.data;
  }

  /**
   * Skip approval step
   */
  static async skipApprovalStep(id: string, reason: string): Promise<ContractApproval> {
    const response = await apiClient.post(`${this.BASE_URL}/${id}/skip`, { reason });
    return response.data;
  }

  /**
   * Get pending approvals for user
   */
  static async getPendingApprovals(userId?: string) {
    const response = await apiClient.get(`${this.BASE_URL}/pending`, {
      params: { userId },
    });
    return response.data;
  }

  /**
   * Get approval statistics
   */
  static async getApprovalStats() {
    const response = await apiClient.get(`${this.BASE_URL}/stats`);
    return response.data;
  }

  /**
   * Get approval history for a contract
   */
  static async getApprovalHistory(contractId: string) {
    const response = await apiClient.get(`/api/contracts/${contractId}/approval-history`);
    return response.data;
  }

  /**
   * Bulk approve contracts
   */
  static async bulkApproveContracts(approvalIds: string[], comments?: string) {
    const response = await apiClient.post(`${this.BASE_URL}/bulk-approve`, {
      approvalIds,
      comments,
    });
    return response.data;
  }

  /**
   * Bulk reject contracts
   */
  static async bulkRejectContracts(approvalIds: string[], reason: string) {
    const response = await apiClient.post(`${this.BASE_URL}/bulk-reject`, {
      approvalIds,
      reason,
    });
    return response.data;
  }

  /**
   * Get approval templates
   */
  static async getApprovalTemplates() {
    const response = await apiClient.get(`${this.BASE_URL}/templates`);
    return response.data;
  }

  /**
   * Create approval template
   */
  static async createApprovalTemplate(data: any) {
    const response = await apiClient.post(`${this.BASE_URL}/templates`, data);
    return response.data;
  }

  /**
   * Apply approval template to contract
   */
  static async applyApprovalTemplate(contractId: string, templateId: string) {
    const response = await apiClient.post(`/api/contracts/${contractId}/apply-approval-template`, {
      templateId,
    });
    return response.data;
  }

  /**
   * Get approval rules
   */
  static async getApprovalRules() {
    const response = await apiClient.get(`${this.BASE_URL}/rules`);
    return response.data;
  }

  /**
   * Create approval rule
   */
  static async createApprovalRule(data: any) {
    const response = await apiClient.post(`${this.BASE_URL}/rules`, data);
    return response.data;
  }

  /**
   * Update approval rule
   */
  static async updateApprovalRule(id: string, data: any) {
    const response = await apiClient.put(`${this.BASE_URL}/rules/${id}`, data);
    return response.data;
  }

  /**
   * Delete approval rule
   */
  static async deleteApprovalRule(id: string): Promise<void> {
    await apiClient.delete(`${this.BASE_URL}/rules/${id}`);
  }

  /**
   * Test approval rule
   */
  static async testApprovalRule(ruleId: string, contractData: any) {
    const response = await apiClient.post(`${this.BASE_URL}/rules/${ruleId}/test`, contractData);
    return response.data;
  }

  /**
   * Get approval notifications
   */
  static async getApprovalNotifications(userId?: string) {
    const response = await apiClient.get(`${this.BASE_URL}/notifications`, {
      params: { userId },
    });
    return response.data;
  }

  /**
   * Mark approval notification as read
   */
  static async markNotificationAsRead(notificationId: string): Promise<void> {
    await apiClient.patch(`${this.BASE_URL}/notifications/${notificationId}/read`);
  }
}
