import { EventInput, DateSelectArg, EventClickArg, EventChangeArg } from '@fullcalendar/core';

/**
 * Calendar event type
 */
export interface CalendarEvent extends EventInput {
  id: string;
  title: string;
  start: string | Date;
  end?: string | Date;
  allDay?: boolean;
  color?: string;
  textColor?: string;
  description?: string;
  location?: string;
  status?: 'confirmed' | 'tentative' | 'cancelled';
  editable?: boolean;
  className?: string;
  extendedProps?: {
    type?: 'meeting' | 'appointment' | 'deadline' | 'training' | 'workshop' | 'planning' | 'event' | 'leave' | 'client' | 'personal' | 'default';
    [key: string]: string | number | boolean | object | null | undefined;
  };
}

/**
 * Calendar props
 */
export interface CalendarProps {
  /**
   * Initial events to display
   */
  events?: CalendarEvent[];

  /**
   * Initial date to display
   */
  initialDate?: Date;

  /**
   * Initial view to display
   */
  initialView?: 'dayGridMonth' | 'timeGridWeek' | 'timeGridDay' | 'listWeek';

  /**
   * Whether to show weekend days
   */
  weekends?: boolean;

  /**
   * Whether events can be edited
   */
  editable?: boolean;

  /**
   * Whether events can be selected
   */
  selectable?: boolean;

  /**
   * Whether to show all-day slot in time grid views
   */
  allDaySlot?: boolean;

  /**
   * Height of the calendar
   */
  height?: string | number;

  /**
   * Additional class name for the calendar
   */
  className?: string;

  /**
   * Callback when a date is selected
   */
  onDateSelect?: (selectInfo: DateSelectArg) => void;

  /**
   * Callback when an event is clicked
   */
  onEventClick?: (clickInfo: EventClickArg) => void;

  /**
   * Callback when an event is changed (dragged, resized)
   */
  onEventChange?: (changeInfo: EventChangeArg) => void;

  /**
   * Callback to add a new event
   */
  onAddEvent?: (event: CalendarEvent) => void;

  /**
   * Callback to update an event
   */
  onUpdateEvent?: (event: CalendarEvent) => void;

  /**
   * Callback to delete an event
   */
  onDeleteEvent?: (eventId: string) => void;
}
