/**
 * Chat WebSocket Hook
 * Main hook for chat WebSocket functionality
 */

import { useEffect, useState, useCallback, useRef } from 'react';
import { 
  ChatWebSocketService, 
  getChatWebSocketService, 
  initializeChatWebSocket,
  type ChatWebSocketConfig,
  type ConnectionStatus 
} from '../../services/chat-websocket.service';
import { 
  ChatMessage, 
  MessageContent, 
  TypingIndicator 
} from '../types/chat-message.types';
import { 
  MessageReceivedData,
  FormPrefillData,
  FormSubmitResultData,
  StreamChunkData,
  StreamCompleteData,
  ErrorData,
  SystemNotificationData
} from '../types/websocket-events.types';

// Hook options
export interface UseChatWebSocketOptions {
  config?: ChatWebSocketConfig;
  roomId?: string;
  userId?: string;
  userInfo?: {
    name: string;
    avatar?: string;
  };
  autoJoinRoom?: boolean;
  enableTypingIndicator?: boolean;
  typingTimeout?: number;
}

// Hook return type
export interface UseChatWebSocketReturn {
  // Connection
  isConnected: boolean;
  connectionStatus: ConnectionStatus;
  connect: () => Promise<void>;
  disconnect: () => void;
  
  // Room management
  joinRoom: (roomId: string, userId: string, userInfo?: { name: string; avatar?: string }) => Promise<void>;
  leaveRoom: (roomId: string, userId: string) => Promise<void>;
  currentRoomId: string | null;
  
  // Messages
  messages: ChatMessage[];
  sendMessage: (content: MessageContent, replyTo?: string) => Promise<void>;
  clearMessages: () => void;
  
  // Typing indicators
  typingUsers: TypingIndicator[];
  startTyping: () => void;
  stopTyping: () => void;
  
  // Forms
  submitForm: (formId: string, messageId: string, formData: Record<string, unknown>) => Promise<void>;
  updateFormField: (formId: string, messageId: string, fieldName: string, fieldValue: unknown) => void;
  
  // Streaming
  streamingMessages: Map<string, { chunks: string[]; isComplete: boolean }>;
  
  // Errors and notifications
  lastError: ErrorData | null;
  notifications: SystemNotificationData[];
  clearError: () => void;
  clearNotifications: () => void;
  
  // Service instance
  service: ChatWebSocketService | null;
}

/**
 * Main Chat WebSocket Hook
 */
export function useChatWebSocket(options: UseChatWebSocketOptions = {}): UseChatWebSocketReturn {
  const {
    config,
    roomId,
    userId,
    userInfo,
    autoJoinRoom = true,
    enableTypingIndicator = true,
    typingTimeout = 3000,
  } = options;

  // State
  const [service, setService] = useState<ChatWebSocketService | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>('disconnected');
  const [currentRoomId, setCurrentRoomId] = useState<string | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [typingUsers, setTypingUsers] = useState<TypingIndicator[]>([]);
  const [streamingMessages, setStreamingMessages] = useState<Map<string, { chunks: string[]; isComplete: boolean }>>(new Map());
  const [lastError, setLastError] = useState<ErrorData | null>(null);
  const [notifications, setNotifications] = useState<SystemNotificationData[]>([]);

  // Refs
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isTypingRef = useRef(false);

  // Initialize service
  useEffect(() => {
    if (config) {
      const newService = initializeChatWebSocket(config);
      setService(newService);
    } else {
      try {
        const existingService = getChatWebSocketService();
        setService(existingService);
      } catch (error) {
        console.warn('[useChatWebSocket] No service available and no config provided');
      }
    }
  }, [config]);

  // Set up event listeners
  useEffect(() => {
    if (!service) return;

    const unsubscribers: Array<() => void> = [];

    // Connection events
    unsubscribers.push(
      service.on('connection_status', (data) => {
        setConnectionStatus(data.status);
        setIsConnected(data.status === 'connected');
      })
    );

    // Message events
    unsubscribers.push(
      service.on('message_received', (data: MessageReceivedData) => {
        setMessages(prev => [...prev, data.message]);
      })
    );

    unsubscribers.push(
      service.on('message_updated', (data) => {
        setMessages(prev => 
          prev.map(msg => 
            msg.id === data.messageId 
              ? { ...msg, content: data.content, metadata: { ...msg.metadata, edited: true, editedAt: data.editedAt } }
              : msg
          )
        );
      })
    );

    unsubscribers.push(
      service.on('message_deleted', (data) => {
        setMessages(prev => prev.filter(msg => msg.id !== data.messageId));
      })
    );

    // Typing events
    unsubscribers.push(
      service.on('user_typing', (data: TypingIndicator) => {
        setTypingUsers(prev => {
          const filtered = prev.filter(user => user.userId !== data.userId);
          if (data.isTyping) {
            return [...filtered, data];
          }
          return filtered;
        });

        // Auto-remove typing indicator after timeout
        if (data.isTyping) {
          setTimeout(() => {
            setTypingUsers(prev => prev.filter(user => user.userId !== data.userId));
          }, typingTimeout);
        }
      })
    );

    // Stream events
    unsubscribers.push(
      service.on('stream_chunk', (data: StreamChunkData) => {
        setStreamingMessages(prev => {
          const newMap = new Map(prev);
          const existing = newMap.get(data.messageId) || { chunks: [], isComplete: false };
          existing.chunks[data.chunkIndex] = data.chunk;
          existing.isComplete = data.isComplete;
          newMap.set(data.messageId, existing);
          return newMap;
        });
      })
    );

    unsubscribers.push(
      service.on('stream_complete', (data: StreamCompleteData) => {
        setStreamingMessages(prev => {
          const newMap = new Map(prev);
          newMap.set(data.messageId, { 
            chunks: [data.finalContent], 
            isComplete: true 
          });
          return newMap;
        });

        // Update the actual message
        setMessages(prev => 
          prev.map(msg => 
            msg.id === data.messageId 
              ? { 
                  ...msg, 
                  content: { 
                    ...msg.content, 
                    data: { 
                      ...msg.content.data, 
                      text: data.finalContent,
                      isComplete: true 
                    } 
                  } 
                }
              : msg
          )
        );
      })
    );

    // Form events
    unsubscribers.push(
      service.on('form_prefill', (data: FormPrefillData) => {
        // Handle form prefill - this will be used by form components
        console.log('[useChatWebSocket] Form prefill received:', data);
      })
    );

    unsubscribers.push(
      service.on('form_submit_result', (data: FormSubmitResultData) => {
        if (!data.success && data.error) {
          setLastError({
            code: 'FORM_SUBMIT_ERROR',
            message: data.error,
            timestamp: new Date().toISOString(),
            details: { formId: data.formId, messageId: data.messageId }
          });
        }
      })
    );

    // Error events
    unsubscribers.push(
      service.on('error', (data: ErrorData) => {
        setLastError(data);
      })
    );

    // System notifications
    unsubscribers.push(
      service.on('system_notification', (data: SystemNotificationData) => {
        setNotifications(prev => [...prev, data]);
      })
    );

    // Room events
    unsubscribers.push(
      service.on('room_joined', (data) => {
        setCurrentRoomId(data.roomId);
      })
    );

    unsubscribers.push(
      service.on('room_left', (data) => {
        if (data.roomId === currentRoomId) {
          setCurrentRoomId(null);
          setMessages([]);
          setTypingUsers([]);
        }
      })
    );

    return () => {
      unsubscribers.forEach(unsubscribe => unsubscribe());
    };
  }, [service, currentRoomId, typingTimeout]);

  // Auto-join room
  useEffect(() => {
    if (service && isConnected && roomId && userId && autoJoinRoom && currentRoomId !== roomId) {
      joinRoom(roomId, userId, userInfo);
    }
  }, [service, isConnected, roomId, userId, autoJoinRoom, userInfo, currentRoomId]);

  // Connection methods
  const connect = useCallback(async () => {
    if (service) {
      await service.connect();
    }
  }, [service]);

  const disconnect = useCallback(() => {
    if (service) {
      service.disconnect();
      setCurrentRoomId(null);
      setMessages([]);
      setTypingUsers([]);
    }
  }, [service]);

  // Room methods
  const joinRoom = useCallback(async (roomId: string, userId: string, userInfo?: { name: string; avatar?: string }) => {
    if (service) {
      await service.joinRoom(roomId, userId, userInfo);
    }
  }, [service]);

  const leaveRoom = useCallback(async (roomId: string, userId: string) => {
    if (service) {
      await service.leaveRoom(roomId, userId);
    }
  }, [service]);

  // Message methods
  const sendMessage = useCallback(async (content: MessageContent, replyTo?: string) => {
    if (service && currentRoomId) {
      const tempId = `temp_${Date.now()}_${Math.random()}`;
      await service.sendMessage(currentRoomId, content, replyTo, tempId);
    }
  }, [service, currentRoomId]);

  const clearMessages = useCallback(() => {
    setMessages([]);
  }, []);

  // Typing methods
  const startTyping = useCallback(() => {
    if (service && currentRoomId && userId && enableTypingIndicator && !isTypingRef.current) {
      service.startTyping(currentRoomId, userId);
      isTypingRef.current = true;

      // Clear existing timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }

      // Set timeout to stop typing
      typingTimeoutRef.current = setTimeout(() => {
        stopTyping();
      }, typingTimeout);
    }
  }, [service, currentRoomId, userId, enableTypingIndicator, typingTimeout]);

  const stopTyping = useCallback(() => {
    if (service && currentRoomId && userId && isTypingRef.current) {
      service.stopTyping(currentRoomId, userId);
      isTypingRef.current = false;

      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
        typingTimeoutRef.current = null;
      }
    }
  }, [service, currentRoomId, userId]);

  // Form methods
  const submitForm = useCallback(async (formId: string, messageId: string, formData: Record<string, unknown>) => {
    if (service && currentRoomId) {
      await service.submitForm(formId, messageId, formData, currentRoomId);
    }
  }, [service, currentRoomId]);

  const updateFormField = useCallback((formId: string, messageId: string, fieldName: string, fieldValue: unknown) => {
    if (service && currentRoomId) {
      service.updateFormField(formId, messageId, fieldName, fieldValue, currentRoomId);
    }
  }, [service, currentRoomId]);

  // Utility methods
  const clearError = useCallback(() => {
    setLastError(null);
  }, []);

  const clearNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
      stopTyping();
    };
  }, [stopTyping]);

  return {
    // Connection
    isConnected,
    connectionStatus,
    connect,
    disconnect,
    
    // Room management
    joinRoom,
    leaveRoom,
    currentRoomId,
    
    // Messages
    messages,
    sendMessage,
    clearMessages,
    
    // Typing indicators
    typingUsers,
    startTyping,
    stopTyping,
    
    // Forms
    submitForm,
    updateFormField,
    
    // Streaming
    streamingMessages,
    
    // Errors and notifications
    lastError,
    notifications,
    clearError,
    clearNotifications,
    
    // Service instance
    service,
  };
}

export default useChatWebSocket;
