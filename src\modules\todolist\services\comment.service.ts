import { apiClient } from '@/shared/api';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';

import {
  CommentDto,
  CommentQueryDto,
  CreateCommentDto,
  UpdateCommentDto,
} from '../types/comment.types';

/**
 * Service for comments
 */
export const CommentService = {
  /**
   * Get comments for a task
   * @param taskId Task ID
   * @param params Query parameters
   * @returns Promise with API response containing comments
   */
  getComments: (taskId: number, params?: CommentQueryDto) => {
    return apiClient.get<PaginatedResult<CommentDto>>(`/api/tasks/${taskId}/comments`, { params });
  },

  /**
   * Get a comment by ID
   * @param taskId Task ID
   * @param commentId Comment ID
   * @returns Promise with API response containing the comment
   */
  getComment: (taskId: number, commentId: number) => {
    return apiClient.get<CommentDto>(`/api/tasks/${taskId}/comments/${commentId}`);
  },

  /**
   * Create a new comment
   * @param data Comment data
   * @returns Promise with API response containing the created comment
   */
  createComment: (data: CreateCommentDto) => {
    return apiClient.post<CommentDto>(`/api/tasks/${data.taskId}/comments`, data);
  },

  /**
   * Update a comment
   * @param taskId Task ID
   * @param commentId Comment ID
   * @param data Updated comment data
   * @returns Promise with API response containing the updated comment
   */
  updateComment: (taskId: number, commentId: number, data: UpdateCommentDto) => {
    return apiClient.patch<CommentDto>(`/api/tasks/${taskId}/comments/${commentId}`, data);
  },

  /**
   * Delete a comment
   * @param taskId Task ID
   * @param commentId Comment ID
   * @returns Promise with API response containing the result
   */
  deleteComment: (taskId: number, commentId: number) => {
    return apiClient.delete<boolean>(`/api/tasks/${taskId}/comments/${commentId}`);
  },
};
