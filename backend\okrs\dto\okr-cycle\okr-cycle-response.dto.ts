import { ApiProperty } from '@nestjs/swagger';
import { OkrCycleStatus } from '../../enum/okr-cycle-status.enum';

/**
 * DTO for OKR cycle response
 */
export class OkrCycleResponseDto {
  /**
   * Unique identifier for the OKR cycle
   * @example 1
   */
  @ApiProperty({
    description: 'ID của chu kỳ OKR',
    example: 1,
  })
  id: number;

  /**
   * Name of the OKR cycle
   * @example "Q1-2025"
   */
  @ApiProperty({
    description: 'Tên chu kỳ OKR',
    example: 'Q1-2025',
  })
  name: string;

  /**
   * Start date of the OKR cycle
   * @example "2025-01-01"
   */
  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> bắt đầu chu kỳ',
    example: '2025-01-01',
  })
  startDate: string;

  /**
   * End date of the OKR cycle
   * @example "2025-03-31"
   */
  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> kế<PERSON> thúc chu kỳ',
    example: '2025-03-31',
  })
  endDate: string;

  /**
   * Status of the OKR cycle
   * @example "ACTIVE"
   */
  @ApiProperty({
    description: 'Trạng thái chu kỳ',
    enum: OkrCycleStatus,
    example: OkrCycleStatus.ACTIVE,
  })
  status: OkrCycleStatus;

  /**
   * Creation timestamp (in milliseconds)
   * @example 1672531200000
   */
  @ApiProperty({
    description: 'Thời gian tạo (timestamp)',
    example: 1672531200000,
  })
  createdAt: number;
}
