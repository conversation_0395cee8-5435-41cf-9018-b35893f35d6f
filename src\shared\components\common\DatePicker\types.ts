import { ReactNode } from 'react';

/**
 * Props cho DatePicker component
 */
export interface DatePickerProps {
  /**
   * Giá trị đã chọn
   */
  value?: Date | null;

  /**
   * Callback khi giá trị thay đổi
   */
  onChange?: (date: Date | null) => void;

  /**
   * Định dạng hiển thị ngày
   * @default "dd/MM/yyyy"
   */
  format?: string;

  /**
   * Placeholder cho input
   */
  placeholder?: string;

  /**
   * Label hiển thị
   */
  label?: string;

  /**
   * Disabled component
   */
  disabled?: boolean;

  /**
   * Các ngày không được phép chọn
   */
  disabledDates?: Date[] | ((date: Date) => boolean);

  /**
   * Ngày tối thiểu được phép chọn
   */
  minDate?: Date;

  /**
   * Ngày tối đa được phép chọn
   */
  maxDate?: Date;

  /**
   * Hiển thị button clear
   */
  clearable?: boolean;

  /**
   * <PERSON><PERSON> trí hiển thị calendar
   */
  placement?: 'top' | 'bottom' | 'left' | 'right';

  /**
   * Kích thước component
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Chiều rộng 100%
   */
  fullWidth?: boolean;

  /**
   * Thông báo lỗi
   */
  error?: string;

  /**
   * Text hỗ trợ
   */
  helperText?: string;

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Hiển thị icon calendar
   */
  showCalendarIcon?: boolean;

  /**
   * Tự động đóng calendar khi chọn ngày
   */
  autoClose?: boolean;

  /**
   * Hiển thị ngày hiện tại
   */
  showToday?: boolean;

  /**
   * Hiển thị tuần
   */
  showWeekNumbers?: boolean;

  /**
   * Ngày đầu tiên của tuần (0: Chủ nhật, 1: Thứ 2, ...)
   * @default 1
   */
  firstDayOfWeek?: 0 | 1 | 2 | 3 | 4 | 5 | 6;

  /**
   * Tên các ngày trong tuần
   */
  weekDayNames?: string[];

  /**
   * Tên các tháng
   */
  monthNames?: string[];

  /**
   * Hiển thị button today
   */
  showTodayButton?: boolean;

  /**
   * Text cho button today
   */
  todayButtonText?: string;

  /**
   * Text cho button clear
   */
  clearButtonText?: string;

  /**
   * Callback khi calendar mở
   */
  onOpen?: () => void;

  /**
   * Callback khi calendar đóng
   */
  onClose?: () => void;

  /**
   * Ref cho input element
   */
  inputRef?: React.Ref<HTMLInputElement>;

  /**
   * Props bổ sung cho input
   */
  inputProps?: React.InputHTMLAttributes<HTMLInputElement>;

  /**
   * Icon calendar tùy chỉnh
   */
  calendarIcon?: ReactNode;

  /**
   * Chế độ hiển thị chỉ icon
   * Khi bật, DatePicker sẽ chỉ hiển thị icon calendar thay vì input field
   */
  iconOnly?: boolean;

  /**
   * Ẩn giá trị đã chọn khi ở chế độ iconOnly
   * Khi bật, DatePicker sẽ chỉ hiển thị icon mà không hiển thị giá trị đã chọn
   */
  hiddenInput?: boolean;

  /**
   * Ẩn border xung quanh icon khi ở chế độ iconOnly
   * Khi bật, DatePicker sẽ không hiển thị border xung quanh icon
   */
  noBorder?: boolean;
}

/**
 * Props cho DateTimePicker component
 */
export interface DateTimePickerProps extends Omit<DatePickerProps, 'format'> {
  /**
   * Định dạng hiển thị ngày và giờ
   * @default "dd/MM/yyyy HH:mm"
   */
  format?: string;

  /**
   * Định dạng thời gian (12h hoặc 24h)
   * @default "24h"
   */
  timeFormat?: '12h' | '24h';

  /**
   * Hiển thị giây
   * @default false
   */
  showSeconds?: boolean;

  /**
   * Bước nhảy cho phút
   * @default 1
   */
  minuteStep?: number;

  /**
   * Bước nhảy cho giờ
   * @default 1
   */
  hourStep?: number;
}

/**
 * Props cho RangePicker component
 */
export interface RangePickerProps
  extends Omit<DatePickerProps, 'value' | 'onChange' | 'placeholder'> {
  /**
   * Giá trị đã chọn
   */
  value?: [Date | null, Date | null];

  /**
   * Callback khi giá trị thay đổi
   */
  onChange?: (dates: [Date | null, Date | null]) => void;

  /**
   * Placeholder cho input
   */
  placeholder?: [string, string];

  /**
   * Số ngày tối đa được chọn trong range
   */
  maxRange?: number;

  /**
   * Hiển thị số ngày đã chọn
   */
  showDaysCount?: boolean;

  /**
   * Text hiển thị giữa hai input
   * @default "đến"
   */
  separatorText?: string;

  /**
   * Hiển thị hai tháng cạnh nhau
   */
  showTwoMonths?: boolean;

  /**
   * Tự động chọn ngày kết thúc sau khi chọn ngày bắt đầu
   */
  autoSelectEnd?: boolean;
}

/**
 * Props cho Calendar component
 */
export interface CalendarProps {
  /**
   * Ngày đã chọn
   */
  selectedDate?: Date | null;

  /**
   * Callback khi chọn ngày
   */
  onSelectDate?: (date: Date) => void;

  /**
   * Tháng hiển thị
   */
  month?: Date;

  /**
   * Callback khi tháng thay đổi
   */
  onMonthChange?: (date: Date) => void;

  /**
   * Các ngày không được phép chọn
   */
  disabledDates?: Date[] | ((date: Date) => boolean);

  /**
   * Ngày tối thiểu được phép chọn
   */
  minDate?: Date;

  /**
   * Ngày tối đa được phép chọn
   */
  maxDate?: Date;

  /**
   * Hiển thị ngày hiện tại
   */
  showToday?: boolean;

  /**
   * Hiển thị tuần
   */
  showWeekNumbers?: boolean;

  /**
   * Ngày đầu tiên của tuần (0: Chủ nhật, 1: Thứ 2, ...)
   */
  firstDayOfWeek?: 0 | 1 | 2 | 3 | 4 | 5 | 6;

  /**
   * Tên các ngày trong tuần
   */
  weekDayNames?: string[];

  /**
   * Tên các tháng
   */
  monthNames?: string[];

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Range mode
   */
  rangeMode?: boolean;

  /**
   * Ngày bắt đầu range
   */
  startDate?: Date | null;

  /**
   * Ngày kết thúc range
   */
  endDate?: Date | null;

  /**
   * Callback khi chọn range
   */
  onRangeSelect?: (start: Date | null, end: Date | null) => void;

  /**
   * Hiển thị button today
   */
  showTodayButton?: boolean;

  /**
   * Text cho button today
   */
  todayButtonText?: string;

  /**
   * Loại bỏ border radius bên phải (dùng khi kết hợp với time picker)
   */
  noRightRadius?: boolean;
}

/**
 * Props cho CalendarHeader component
 */
export interface CalendarHeaderProps {
  /**
   * Tháng hiển thị
   */
  month: Date;

  /**
   * Callback khi tháng thay đổi
   */
  onMonthChange: (date: Date) => void;

  /**
   * Tên các tháng
   */
  monthNames?: string[];

  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Props cho CalendarGrid component
 */
export interface CalendarGridProps {
  /**
   * Tháng hiển thị
   */
  month: Date;

  /**
   * Ngày đã chọn
   */
  selectedDate?: Date | null;

  /**
   * Callback khi chọn ngày
   */
  onSelectDate?: (date: Date) => void;

  /**
   * Các ngày không được phép chọn
   */
  disabledDates?: Date[] | ((date: Date) => boolean);

  /**
   * Ngày tối thiểu được phép chọn
   */
  minDate?: Date;

  /**
   * Ngày tối đa được phép chọn
   */
  maxDate?: Date;

  /**
   * Hiển thị ngày hiện tại
   */
  showToday?: boolean;

  /**
   * Hiển thị tuần
   */
  showWeekNumbers?: boolean;

  /**
   * Ngày đầu tiên của tuần (0: Chủ nhật, 1: Thứ 2, ...)
   */
  firstDayOfWeek?: 0 | 1 | 2 | 3 | 4 | 5 | 6;

  /**
   * Tên các ngày trong tuần
   */
  weekDayNames?: string[];

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Range mode
   */
  rangeMode?: boolean;

  /**
   * Ngày bắt đầu range
   */
  startDate?: Date | null;

  /**
   * Ngày kết thúc range
   */
  endDate?: Date | null;

  /**
   * Ngày đang được focus (cho keyboard navigation)
   */
  focusedDate?: Date | null;
}

/**
 * Props cho CalendarCell component
 */
export interface CalendarCellProps {
  /**
   * Ngày hiển thị
   */
  date: Date;

  /**
   * Ngày hiện tại (không sử dụng trực tiếp nhưng giữ lại để tương thích)
   */
  currentMonth?: Date;

  /**
   * Ngày đã chọn
   */
  selectedDate?: Date | null;

  /**
   * Callback khi chọn ngày
   */
  onSelectDate?: (date: Date) => void;

  /**
   * Ngày bị disabled
   */
  disabled?: boolean;

  /**
   * Ngày hiện tại
   */
  isToday?: boolean;

  /**
   * Ngày thuộc tháng hiện tại
   */
  isCurrentMonth?: boolean;

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Range mode
   */
  rangeMode?: boolean;

  /**
   * Ngày bắt đầu range
   */
  startDate?: Date | null;

  /**
   * Ngày kết thúc range
   */
  endDate?: Date | null;

  /**
   * Ngày nằm trong range
   */
  inRange?: boolean;

  /**
   * Ngày đang được focus (cho keyboard navigation)
   */
  focusedDate?: Date | null;
}

/**
 * Props cho YearMonthSelector component
 */
export interface YearMonthSelectorProps {
  /**
   * Tháng hiển thị
   */
  month: Date;

  /**
   * Callback khi tháng thay đổi
   */
  onMonthChange: (date: Date) => void;

  /**
   * Tên các tháng
   */
  monthNames?: string[];

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Ngày tối thiểu được phép chọn
   */
  minDate?: Date;

  /**
   * Ngày tối đa được phép chọn
   */
  maxDate?: Date;
}
