import { debounce } from 'lodash';
import { useCallback, useEffect, useRef } from 'react';

import { useQuery, UseQueryOptions, QueryKey } from '@tanstack/react-query';

/**
 * Custom hook for optimized data fetching with debouncing and caching
 * @param queryKey Query key for React Query
 * @param queryFn Query function that fetches data
 * @param options Additional options for useQuery
 * @param debounceMs Debounce time in milliseconds (default: 300ms)
 * @returns Query result with optimized data fetching
 */
export function useOptimizedQuery<TData, TError = unknown>(
  queryKey: QueryKey,
  queryFn: () => Promise<TData>,
  options?: Omit<UseQueryOptions<TData, TError, TData>, 'queryKey' | 'queryFn'>,
  debounceMs = 300
) {
  // Create a ref to store the latest query function
  const queryFnRef = useRef(queryFn);

  // Update the ref when the query function changes
  useEffect(() => {
    queryFnRef.current = queryFn;
  }, [queryFn]);

  // Create a debounced version of the query function
  const debouncedQueryFn = useCallback(async () => {
    const debouncedFn = debounce(async () => {
      return queryFnRef.current();
    }, debounceMs);
    return debouncedFn();
  }, [debounceMs]);

  // Use React Query with the debounced query function
  return useQuery({
    queryKey,
    queryFn: async () => {
      const result = await debouncedQueryFn();
      if (result === undefined) {
        throw new Error('Query function returned undefined');
      }
      return result;
    },
    ...options,
  });
}

/**
 * Custom hook for optimized data fetching with stale-while-revalidate pattern
 * @param queryKey Query key for React Query
 * @param queryFn Query function that fetches data
 * @param options Additional options for useQuery
 * @returns Query result with optimized data fetching
 */
export function useSWRQuery<TData, TError = unknown>(
  queryKey: QueryKey,
  queryFn: () => Promise<TData>,
  options?: Omit<UseQueryOptions<TData, TError, TData>, 'queryKey' | 'queryFn'>
) {
  return useQuery({
    queryKey,
    queryFn,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
    refetchOnWindowFocus: true,
    refetchOnMount: true,
    refetchOnReconnect: true,
    ...options,
  });
}

/**
 * Custom hook for optimized data fetching with pagination
 * @param queryKey Query key for React Query
 * @param queryFn Query function that fetches data
 * @param options Additional options for useQuery
 * @returns Query result with optimized data fetching
 */
export function usePaginatedQuery<TData, TError = unknown>(
  queryKey: QueryKey,
  queryFn: () => Promise<TData>,
  options?: Omit<UseQueryOptions<TData, TError, TData>, 'queryKey' | 'queryFn'>
) {
  return useQuery({
    queryKey,
    queryFn,
    placeholderData: previousData => previousData, // Replacement for keepPreviousData
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes (formerly cacheTime)
    ...options,
  });
}
