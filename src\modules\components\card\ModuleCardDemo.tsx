import { ResponsiveGrid, Typography } from '@/shared/components/common';
import React from 'react';
import { ModuleCard, ModuleCardCompact } from './index';

/**
 * Demo component để so sánh ModuleCard và ModuleCardCompact
 */
const ModuleCardDemo: React.FC = () => {
  const moduleData = [
    {
      title: 'Marketing',
      description: 'Quản lý chiến dịch marketing và phân tích hiệu quả',
      icon: 'megaphone' as const,
      linkTo: '/marketing',
      color: 'primary' as const,
    },
    {
      title: 'Business',
      description: 'Quản lý quy trình kinh doanh và báo cáo',
      icon: 'briefcase' as const,
      linkTo: '/business',
      color: 'success' as const,
    },
    {
      title: 'HRM',
      description: 'Quản lý nhân sự và tài nguyên con người',
      icon: 'users' as const,
      linkTo: '/hrm',
      color: 'info' as const,
    },
    {
      title: 'Finance',
      description: 'Quản lý tài chính và kế toán',
      icon: 'dollar-sign' as const,
      linkTo: '/finance',
      color: 'warning' as const,
    },
  ];

  return (
    <div className="w-full bg-background text-foreground p-6 space-y-8">
      {/* ModuleCard - Phiên bản gốc */}
      <div>
        <Typography variant="h4" className="mb-4 font-semibold">
          ModuleCard - Phiên bản gốc (Lớn)
        </Typography>
        <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 3, lg: 4 }}>
          {moduleData.map(module => (
            <ModuleCard
              key={module.title}
              title={module.title}
              description={module.description}
              icon={module.icon}
              linkTo={module.linkTo}
              gradientColor={module.color}
            />
          ))}
        </ResponsiveGrid>
      </div>

      {/* ModuleCardCompact - Phiên bản nhỏ gọn */}
      <div>
        <Typography variant="h4" className="mb-4 font-semibold">
          ModuleCardCompact - Phiên bản nhỏ gọn (Không border, nhỏ gọn hơn)
        </Typography>
        <Typography variant="body2" className="mb-4 text-muted">
          ✨ Đã cải thiện: Bỏ border, background nhẹ hơn, kích thước 80px cố định
        </Typography>
        <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 3, lg: 4 }}>
          {moduleData.map(module => (
            <ModuleCardCompact
              key={`compact-${module.title}`}
              title={module.title}
              description={module.description}
              icon={module.icon}
              linkTo={module.linkTo}
              color={module.color}
            />
          ))}
        </ResponsiveGrid>
      </div>

      {/* ModuleCardCompact - Không có description */}
      <div>
        <Typography variant="h4" className="mb-4 font-semibold">
          ModuleCardCompact - Chỉ có tiêu đề (Siêu nhỏ gọn)
        </Typography>
        <ResponsiveGrid maxColumns={{ xs: 2, sm: 3, md: 4, lg: 6 }}>
          {moduleData.map(module => (
            <ModuleCardCompact
              key={`mini-${module.title}`}
              title={module.title}
              icon={module.icon}
              linkTo={module.linkTo}
              color={module.color}
            />
          ))}
        </ResponsiveGrid>
      </div>

      {/* Loading states */}
      <div>
        <Typography variant="h4" className="mb-4 font-semibold">
          Loading States
        </Typography>
        <div className="space-y-4">
          <div>
            <Typography variant="h6" className="mb-2">
              ModuleCard Loading
            </Typography>
            <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 3, lg: 4 }}>
              {[1, 2, 3, 4].map(i => (
                <ModuleCard
                  key={`loading-${i}`}
                  title=""
                  description=""
                  icon="loader"
                  linkTo=""
                  loading={true}
                />
              ))}
            </ResponsiveGrid>
          </div>

          <div>
            <Typography variant="h6" className="mb-2">
              ModuleCardCompact Loading
            </Typography>
            <ResponsiveGrid maxColumns={{ xs: 2, sm: 3, md: 4, lg: 6 }}>
              {[1, 2, 3, 4].map(i => (
                <ModuleCardCompact
                  key={`compact-loading-${i}`}
                  title=""
                  icon="loader"
                  linkTo=""
                  loading={true}
                />
              ))}
            </ResponsiveGrid>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ModuleCardDemo;
