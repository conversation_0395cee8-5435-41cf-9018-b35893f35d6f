import React, { forwardRef, useState } from 'react';

import Icon from '../Icon';
import Input from '../Input';

/**
 * Props cho component NumberInput
 */
interface NumberInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type'> {
  /**
   * Giá trị tối thiểu
   */
  min?: number;
  /**
   * Giá trị tối đa
   */
  max?: number;
  /**
   * Bước nhảy khi tăng/giảm
   */
  step?: number;
  /**
   * <PERSON><PERSON> chính xác (số chữ số thập phân)
   */
  precision?: number;
  /**
   * Hàm xử lý khi giá trị thay đổi
   */
  onChange?: (value: number | null) => void;
  /**
   * Chế độ hiển thị nút tăng/giảm
   */
  controls?: 'right' | 'inside' | 'none';
  /**
   * Classes tùy chỉnh bổ sung
   */
  className?: string;
}

/**
 * NumberInput - Input số với kiểu dáng đẹp hơn
 */
export const NumberInput = forwardRef<HTMLInputElement, NumberInputProps>(
  (
    {
      min,
      max,
      step = 1,
      precision = 0,
      value,
      defaultValue,
      onChange,
      controls = 'right',
      className = '',
      disabled,
      ...rest
    },
    ref
  ) => {
    // Giá trị của input
    const [inputValue, setInputValue] = useState<string>(() => {
      const initialValue = value !== undefined ? value : defaultValue;
      return initialValue !== undefined ? String(initialValue) : '';
    });

    // Xử lý khi người dùng nhập vào input
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const val = e.target.value.replace(/[^\d.-]/g, '');
      setInputValue(val);

      if (onChange) {
        const numericValue = val === '' ? null : parseFloat(val);
        onChange(numericValue);
      }
    };

    // Xử lý khi người dùng nhấn nút tăng/giảm
    const handleStep = (increment: boolean) => {
      if (disabled) {return;}

      let numericValue = inputValue === '' ? 0 : parseFloat(inputValue);

      // Tăng/giảm giá trị theo step
      if (increment) {
        numericValue += step;
      } else {
        numericValue -= step;
      }

      // Kiểm tra giới hạn min/max
      if (min !== undefined && numericValue < min) {
        numericValue = min;
      }
      if (max !== undefined && numericValue > max) {
        numericValue = max;
      }

      // Làm tròn theo precision
      const multiplier = Math.pow(10, precision);
      numericValue = Math.round(numericValue * multiplier) / multiplier;

      // Cập nhật giá trị
      const newValue = String(numericValue);
      setInputValue(newValue);

      if (onChange) {
        onChange(numericValue);
      }
    };

    // Xử lý khi người dùng blur khỏi input
    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
      // Kiểm tra và điều chỉnh giá trị khi blur
      if (inputValue !== '') {
        let numericValue = parseFloat(inputValue);
        let adjusted = false;

        if (min !== undefined && numericValue < min) {
          numericValue = min;
          adjusted = true;
        }
        if (max !== undefined && numericValue > max) {
          numericValue = max;
          adjusted = true;
        }

        if (adjusted) {
          const newValue = String(numericValue);
          setInputValue(newValue);

          if (onChange) {
            onChange(numericValue);
          }
        }
      }

      // Gọi onBlur từ props nếu có
      if (rest.onBlur) {
        rest.onBlur(e);
      }
    };

    // Render các nút điều khiển bên trong
    const renderInnerControls = () => {
      if (controls === 'none') {return null;}

      return (
        <div className="absolute right-0 inset-y-0 flex flex-col border-l border-input divide-y divide-input">
          <button
            type="button"
            tabIndex={-1}
            className="flex-1 px-1 flex items-center justify-center text-muted-foreground hover:bg-muted/30 transition-colors"
            onClick={() => handleStep(true)}
            disabled={disabled || (max !== undefined && parseFloat(inputValue || '0') >= max)}
          >
            <Icon name="chevron-up" size="xs" />
          </button>
          <button
            type="button"
            tabIndex={-1}
            className="flex-1 px-1 flex items-center justify-center text-muted-foreground hover:bg-muted/30 transition-colors"
            onClick={() => handleStep(false)}
            disabled={disabled || (min !== undefined && parseFloat(inputValue || '0') <= min)}
          >
            <Icon name="chevron-down" size="xs" />
          </button>
        </div>
      );
    };

    // Styles cho input
    const inputClasses = `
      ${controls === 'inside' ? 'pr-7' : ''}
      ${className}
    `.trim();

    return (
      <div className="relative inline-flex items-center">
        <Input
          {...rest}
          type="text"
          inputMode="decimal"
          ref={ref}
          value={inputValue}
          onChange={handleInputChange}
          onBlur={handleBlur}
          className={inputClasses}
          disabled={disabled}
          min={min}
          max={max}
          step={step}
        />

        {controls === 'inside' && renderInnerControls()}

        {controls === 'right' && (
          <div className="flex flex-col ml-1 h-full rounded-md border border-input overflow-hidden">
            <button
              type="button"
              tabIndex={-1}
              className="p-1 flex items-center justify-center text-muted-foreground hover:bg-muted/30 transition-colors"
              onClick={() => handleStep(true)}
              disabled={disabled || (max !== undefined && parseFloat(inputValue || '0') >= max)}
            >
              <Icon name="chevron-up" size="xs" />
            </button>
            <div className="h-[1px] bg-input"></div>
            <button
              type="button"
              tabIndex={-1}
              className="p-1 flex items-center justify-center text-muted-foreground hover:bg-muted/30 transition-colors"
              onClick={() => handleStep(false)}
              disabled={disabled || (min !== undefined && parseFloat(inputValue || '0') <= min)}
            >
              <Icon name="chevron-down" size="xs" />
            </button>
          </div>
        )}
      </div>
    );
  }
);

NumberInput.displayName = 'NumberInput';

export default NumberInput;
