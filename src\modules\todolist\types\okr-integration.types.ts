/**
 * Interface for OKR Cycle
 */
export interface OkrCycleDto {
  id: number;
  name: string;
  startDate: number;
  endDate: number;
  status: OkrCycleStatus;
}

/**
 * Enum for OKR Cycle Status
 */
export enum OkrCycleStatus {
  PLANNING = 'PLANNING',
  ACTIVE = 'ACTIVE',
  CLOSED = 'CLOSED',
}

/**
 * Interface for Objective
 */
export interface ObjectiveDto {
  id: number;
  title: string;
  description?: string;
  cycleId: number;
  ownerId: number;
  progress: number;
  status: ObjectiveStatus;
}

/**
 * Enum for Objective Status
 */
export enum ObjectiveStatus {
  NOT_STARTED = 'NOT_STARTED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  AT_RISK = 'AT_RISK',
}

/**
 * Interface for Key Result
 */
export interface KeyResultDto {
  id: number;
  title: string;
  description?: string;
  objectiveId: number;
  ownerId: number;
  progress: number;
  status: KeyResultStatus;
  startValue: number;
  targetValue: number;
  currentValue: number;
  format: KeyResultFormat;
}

/**
 * Enum for Key Result Status
 */
export enum KeyResultStatus {
  NOT_STARTED = 'NOT_STARTED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  AT_RISK = 'AT_RISK',
}

/**
 * Enum for Key Result Format
 */
export enum KeyResultFormat {
  NUMBER = 'NUMBER',
  PERCENTAGE = 'PERCENTAGE',
  CURRENCY = 'CURRENCY',
  BOOLEAN = 'BOOLEAN',
}

/**
 * Interface for Task-KeyResult Link
 */
export interface TaskKeyResultLinkDto {
  id: number;
  taskId: number;
  keyResultId: number;
  createdAt: number;
  updatedAt: number;
}

/**
 * Interface for Create Task-KeyResult Link
 */
export interface CreateTaskKeyResultLinkDto {
  taskId: number;
  keyResultId: number;
}

/**
 * Interface for Task with Key Results
 */
export interface TaskWithKeyResultsDto {
  taskId: number;
  keyResults: KeyResultDto[];
}

/**
 * Interface for Key Result with Tasks
 */
export interface KeyResultWithTasksDto {
  keyResult: KeyResultDto;
  tasks: {
    id: number;
    title: string;
    status: string;
  }[];
}
