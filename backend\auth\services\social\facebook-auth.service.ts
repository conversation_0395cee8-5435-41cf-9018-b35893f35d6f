import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  ISocialAuthProvider,
  SocialUserInfo,
} from './social-provider.interface';
import { SocialProvider } from '../../enum';
import axios from 'axios';
import { AppException } from '@/common/exceptions/app.exception';
import { AUTH_ERROR_CODE } from '../../errors/auth-error.code';

/**
 * Service xử lý xác thực qua Facebook
 */
@Injectable()
export class FacebookAuthService implements ISocialAuthProvider {
  private readonly logger = new Logger(FacebookAuthService.name);
  private readonly facebookApiUrl = 'https://graph.facebook.com/v18.0/me';
  private readonly facebookDebugTokenUrl =
    'https://graph.facebook.com/debug_token';

  constructor(private readonly configService: ConfigService) {}

  /**
   * Lấy thông tin người dùng từ Facebook bằng access token
   * @param accessToken Access token từ Facebook
   * @returns Thông tin người dùng
   */
  async getUserInfo(accessToken: string): Promise<SocialUserInfo> {
    try {
      // Xác thực token trước khi lấy thông tin
      const isValid = await this.verifyToken(accessToken);
      if (!isValid) {
        throw new AppException(
          AUTH_ERROR_CODE.INVALID_SOCIAL_TOKEN,
          'Facebook access token không hợp lệ hoặc đã hết hạn',
        );
      }

      // Gọi API Facebook để lấy thông tin người dùng
      const response = await axios.get(`${this.facebookApiUrl}`, {
        params: {
          fields: 'id,email,name,first_name,last_name,picture.type(large)',
          access_token: accessToken,
        },
      });

      const userData = response.data;

      // Chuyển đổi dữ liệu từ Facebook sang định dạng chung
      return {
        id: userData.id,
        email: userData.email,
        name: userData.name,
        firstName: userData.first_name,
        lastName: userData.last_name,
        displayName: userData.name,
        photoUrl: userData.picture?.data?.url,
        provider: SocialProvider.FACEBOOK,
        accessToken,
        rawProfile: userData,
      };
    } catch (error) {
      this.logger.error(
        `Error getting user info from Facebook: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AUTH_ERROR_CODE.SOCIAL_AUTH_FAILED,
        `Không thể lấy thông tin người dùng từ Facebook: ${error.message}`,
      );
    }
  }

  /**
   * Xác thực Facebook access token
   * @param accessToken Access token từ Facebook
   * @returns True nếu token hợp lệ, false nếu không
   */
  async verifyToken(accessToken: string): Promise<boolean> {
    try {
      const appId = this.configService.get<string>('FACEBOOK_APP_ID');
      const appSecret = this.configService.get<string>('FACEBOOK_APP_SECRET');

      if (!appId || !appSecret) {
        this.logger.warn('Facebook App ID or App Secret not configured');
        return false;
      }

      // Gọi API Facebook để xác thực token
      const response = await axios.get(this.facebookDebugTokenUrl, {
        params: {
          input_token: accessToken,
          access_token: `${appId}|${appSecret}`,
        },
      });

      const data = response.data.data;

      // Kiểm tra xem token có hợp lệ không
      if (!data.is_valid) {
        this.logger.warn(
          `Facebook token is invalid: ${data.error?.message || 'Unknown error'}`,
        );
        return false;
      }

      // Kiểm tra xem token có thuộc về ứng dụng của chúng ta không
      if (data.app_id !== appId) {
        this.logger.warn(`Token belongs to different app ID: ${data.app_id}`);
        return false;
      }

      return true;
    } catch (error) {
      this.logger.error(
        `Error verifying Facebook token: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }

  /**
   * Lấy loại nhà cung cấp mạng xã hội
   * @returns Facebook
   */
  getProvider(): SocialProvider {
    return SocialProvider.FACEBOOK;
  }
}
