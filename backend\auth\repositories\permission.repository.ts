import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { Permission } from '../entities/permission.entity';
import { RolePermission } from '../entities/role-permission.entity';
import { UserRole } from '../entities/user-role.entity';

/**
 * Repository cho Permission
 */
@Injectable()
export class PermissionRepository {
  private readonly logger = new Logger(PermissionRepository.name);

  constructor(
    @InjectRepository(Permission)
    private readonly permissionRepository: Repository<Permission>,
    @InjectRepository(RolePermission)
    private readonly rolePermissionRepository: Repository<RolePermission>,
    @InjectRepository(UserRole)
    private readonly userRoleRepository: Repository<UserRole>,
  ) {}

  /**
   * Lấy danh sách quyền của người dùng theo định dạng 'module:action'
   * @param userId ID của người dùng
   * @returns <PERSON>h sách quyền theo định dạng 'module:action'
   */
  async getUserPermissions(userId: number): Promise<string[]> {
    try {
      // Lấy danh sách role của user
      const userRoles = await this.userRoleRepository.find({
        where: { userId },
      });

      if (!userRoles || userRoles.length === 0) {
        return [];
      }

      const roleIds = userRoles
        .map((userRole) => userRole.roleId)
        .filter(Boolean) as number[];

      if (roleIds.length === 0) {
        return [];
      }

      // Lấy danh sách permission của các role
      const rolePermissions = await this.rolePermissionRepository.find({
        where: { roleId: In(roleIds) },
      });

      if (!rolePermissions || rolePermissions.length === 0) {
        return [];
      }

      const permissionIds = rolePermissions
        .map((rolePermission) => rolePermission.permissionId)
        .filter(Boolean) as number[];

      if (permissionIds.length === 0) {
        return [];
      }

      // Lấy thông tin chi tiết của các permission
      const permissions = await this.permissionRepository.find({
        where: { id: In(permissionIds) },
      });

      // Chuyển đổi thành định dạng 'module:action'
      return permissions.map(
        (permission) => `${permission.module}:${permission.action}`,
      );
    } catch (error) {
      this.logger.error(
        `Error getting user permissions: ${error.message}`,
        error.stack,
      );
      return [];
    }
  }

  /**
   * Lấy danh sách tất cả các quyền
   * @returns Danh sách tất cả các quyền
   */
  async findAll(): Promise<Permission[]> {
    return this.permissionRepository.find();
  }

  /**
   * Tìm quyền theo module và action
   * @param module Tên module
   * @param action Tên action
   * @returns Thông tin quyền hoặc null nếu không tìm thấy
   */
  async findByModuleAndAction(
    module: string,
    action: string,
  ): Promise<Permission | null> {
    return this.permissionRepository.findOne({
      where: { module, action },
    });
  }
}
