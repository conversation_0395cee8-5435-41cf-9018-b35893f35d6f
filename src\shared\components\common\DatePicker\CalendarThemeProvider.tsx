import React, { useEffect } from 'react';
import {
  CalendarThemeContextType,
  CalendarThemeVariant,
  CalendarColorScheme
} from './types/theme.types';
import { useCalendarTheme } from './hooks/useCalendarTheme';
import { CalendarThemeContext } from './contexts/CalendarThemeContext';
import { useCalendarThemeContext } from './hooks/useCalendarThemeContext';

/**
 * Props cho CalendarThemeProvider
 */
export interface CalendarThemeProviderProps {
  children: React.ReactNode;

  /**
   * Theme variant mặc định
   */
  defaultVariant?: CalendarThemeVariant;

  /**
   * Color scheme mặc định
   */
  defaultColorScheme?: CalendarColorScheme;

  /**
   * Bật/tắt animations
   */
  enableAnimations?: boolean;

  /**
   * Respect user's reduced motion preference
   */
  respectReducedMotion?: boolean;

  /**
   * Persist theme settings
   */
  persistSettings?: boolean;

  /**
   * Storage key for persisting settings
   */
  storageKey?: string;

  /**
   * Inject CSS variables vào document
   */
  injectCSSVariables?: boolean;

  /**
   * CSS selector để inject variables
   */
  cssVariablesTarget?: string;
}

/**
 * CalendarThemeProvider component
 * Cung cấp theme context cho tất cả Calendar components
 */
export const CalendarThemeProvider: React.FC<CalendarThemeProviderProps> = ({
  children,
  defaultVariant = 'auto',
  defaultColorScheme = 'blue',
  enableAnimations = true,
  respectReducedMotion = true,
  persistSettings = true,
  storageKey = 'calendar-theme-settings',
  injectCSSVariables = true,
  cssVariablesTarget = ':root',
}) => {
  // Use calendar theme hook
  const themeHook = useCalendarTheme({
    defaultVariant,
    defaultColorScheme,
    enableAnimations,
    respectReducedMotion,
    persistSettings,
    storageKey,
  });

  // Inject CSS variables into document
  useEffect(() => {
    if (!injectCSSVariables || typeof document === 'undefined') return;

    const cssVariables = themeHook.getCSSVariables();
    const targetElement = cssVariablesTarget === ':root'
      ? document.documentElement
      : document.querySelector(cssVariablesTarget);

    if (!targetElement) return;

    // Apply CSS variables
    Object.entries(cssVariables).forEach(([property, value]) => {
      (targetElement as HTMLElement).style.setProperty(property, value);
    });

    // Cleanup function
    return () => {
      Object.keys(cssVariables).forEach((property) => {
        (targetElement as HTMLElement).style.removeProperty(property);
      });
    };
  }, [themeHook.theme, injectCSSVariables, cssVariablesTarget, themeHook]);

  // Context value
  const contextValue: CalendarThemeContextType = {
    theme: themeHook.theme,
    config: themeHook.config,
    setVariant: themeHook.setVariant,
    setColorScheme: themeHook.setColorScheme,
    setCustomTheme: themeHook.setCustomTheme,
    toggleAnimations: themeHook.toggleAnimations,
  };

  return (
    <CalendarThemeContext.Provider value={contextValue}>
      <div
        className={themeHook.getThemeClasses()}
        style={injectCSSVariables ? undefined : themeHook.getCSSVariables()}
      >
        {children}
      </div>
    </CalendarThemeContext.Provider>
  );
};





/**
 * Component để customize theme settings
 */
export interface CalendarThemeCustomizerProps {
  /**
   * Show variant selector
   */
  showVariantSelector?: boolean;

  /**
   * Show color scheme selector
   */
  showColorSchemeSelector?: boolean;

  /**
   * Show animation toggle
   */
  showAnimationToggle?: boolean;

  /**
   * Custom className
   */
  className?: string;

  /**
   * Render as inline controls
   */
  inline?: boolean;
}

export const CalendarThemeCustomizer: React.FC<CalendarThemeCustomizerProps> = ({
  showVariantSelector = true,
  showColorSchemeSelector = true,
  showAnimationToggle = true,
  className = '',
  inline = false,
}) => {
  const { config, setVariant, setColorScheme, toggleAnimations } = useCalendarThemeContext();

  const containerClasses = inline
    ? `flex items-center gap-4 ${className}`
    : `space-y-4 ${className}`;

  const variants: { value: CalendarThemeVariant; label: string }[] = [
    { value: 'light', label: 'Light' },
    { value: 'dark', label: 'Dark' },
    { value: 'auto', label: 'Auto' },
  ];

  const colorSchemes: { value: CalendarColorScheme; label: string }[] = [
    { value: 'blue', label: 'Blue' },
    { value: 'green', label: 'Green' },
    { value: 'purple', label: 'Purple' },
    { value: 'red', label: 'Red' },
    { value: 'orange', label: 'Orange' },
    { value: 'pink', label: 'Pink' },
    { value: 'indigo', label: 'Indigo' },
    { value: 'teal', label: 'Teal' },
  ];

  return (
    <div className={containerClasses}>
      {showVariantSelector && (
        <div className={inline ? '' : 'space-y-2'}>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Theme Variant
          </label>
          <select
            value={config.variant}
            onChange={(e) => setVariant(e.target.value as CalendarThemeVariant)}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          >
            {variants.map((variant) => (
              <option key={variant.value} value={variant.value}>
                {variant.label}
              </option>
            ))}
          </select>
        </div>
      )}

      {showColorSchemeSelector && (
        <div className={inline ? '' : 'space-y-2'}>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Color Scheme
          </label>
          <select
            value={config.colorScheme}
            onChange={(e) => setColorScheme(e.target.value as CalendarColorScheme)}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          >
            {colorSchemes.map((scheme) => (
              <option key={scheme.value} value={scheme.value}>
                {scheme.label}
              </option>
            ))}
          </select>
        </div>
      )}

      {showAnimationToggle && (
        <div className={inline ? 'flex items-center' : 'flex items-center space-x-2'}>
          <input
            type="checkbox"
            id="enable-animations"
            checked={config.enableAnimations}
            onChange={toggleAnimations}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label
            htmlFor="enable-animations"
            className="text-sm font-medium text-gray-700 dark:text-gray-300"
          >
            Enable Animations
          </label>
        </div>
      )}
    </div>
  );
};

// Export only components to satisfy react-refresh/only-export-components rule
export default CalendarThemeProvider;
