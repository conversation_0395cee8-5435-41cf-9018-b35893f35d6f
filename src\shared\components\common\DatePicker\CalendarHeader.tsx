import React, { useState } from 'react';
import { CalendarHeaderProps } from './types';
import { formatDate, incrementMonth, decrementMonth } from './utils';
import { useTheme } from '@/shared/contexts/theme';
import { useTranslation } from 'react-i18next';
import YearMonthSelector from './YearMonthSelector';
import { Icon } from '@/shared/components/common';

/**
 * Component hiển thị header của calendar
 */
const CalendarHeader: React.FC<CalendarHeaderProps> = ({
  month,
  onMonthChange,
  monthNames,
  className = '',
}) => {
  const { t, i18n } = useTranslation();
  useTheme(); // Sử dụng hook theme mới

  // State để hiển thị selector chọn tháng/năm
  const [showSelector, setShowSelector] = useState(false);

  // Lấy tên tháng và năm hiện tại
  const currentMonthName = formatDate(month, 'MMMM', i18n.language);
  const currentYear = formatDate(month, 'yyyy', i18n.language);

  // Xử lý chuyển tháng trước
  const handlePrevMonth = () => {
    onMonthChange(decrementMonth(month));
  };

  // Xử lý chuyển tháng sau
  const handleNextMonth = () => {
    onMonthChange(incrementMonth(month));
  };

  // Xử lý toggle selector
  const handleToggleSelector = () => {
    setShowSelector(!showSelector);
  };

  // Base classes
  const baseClasses = 'flex items-center justify-between p-2';

  // Combine all classes
  const headerClasses = [baseClasses, className].join(' ');

  return (
    <div className={headerClasses}>
      {/* Nút chuyển tháng trước */}
      <button
        type="button"
        className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
        onClick={handlePrevMonth}
        aria-label={t('datepicker.previousMonth', 'Previous month')}
      >
        <Icon name="chevron-left" size="sm" />
      </button>

      {/* Hiển thị tháng và năm */}
      <button
        type="button"
        className="flex items-center space-x-1 px-2 py-1 rounded hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors font-medium"
        onClick={handleToggleSelector}
        aria-label={t('datepicker.selectMonthYear', 'Select month and year')}
        aria-expanded={showSelector}
      >
        <span>{currentMonthName}</span>
        <span>{currentYear}</span>
        <Icon name={showSelector ? 'chevron-up' : 'chevron-down'} size="xs" />
      </button>

      {/* Nút chuyển tháng sau */}
      <button
        type="button"
        className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
        onClick={handleNextMonth}
        aria-label={t('datepicker.nextMonth', 'Next month')}
      >
        <Icon name="chevron-right" size="sm" />
      </button>

      {/* Selector chọn tháng/năm */}
      {showSelector && (
        <div className="absolute top-12 left-0 right-0 z-10 bg-card shadow-lg rounded-md animate-fade-in">
          <YearMonthSelector
            month={month}
            onMonthChange={(newMonth: Date) => {
              onMonthChange(newMonth);
              setShowSelector(false);
            }}
            monthNames={monthNames}
          />
        </div>
      )}
    </div>
  );
};

export default CalendarHeader;
