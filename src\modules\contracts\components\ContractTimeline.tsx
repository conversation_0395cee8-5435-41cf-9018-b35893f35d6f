import React from 'react';
import { useTranslation } from 'react-i18next';

import { Card, Typography } from '@/shared/components/common';

/**
 * Contract Timeline Component
 * Component timeline lịch sử hợp đồng
 */
const ContractTimeline: React.FC = () => {
  const { t } = useTranslation(['common', 'contracts']);

  return (
    <Card className="p-6">
      <Typography variant="h2" className="mb-4">
        {t('contracts:version_history')}
      </Typography>
      <div className="text-center py-8">
        <Typography variant="body1" className="text-muted-foreground">
          Timeline lịch sử hợp đồng đang đư<PERSON>c ph<PERSON>t triển...
        </Typography>
      </div>
    </Card>
  );
};

export default ContractTimeline;
