/**
 * Enum for Attachment Type
 */
export enum AttachmentType {
  IMAGE = 'IMAGE',
  DOCUMENT = 'DOCUMENT',
  VIDEO = 'VIDEO',
  AUDIO = 'AUDIO',
  OTHER = 'OTHER',
}

/**
 * Interface for Attachment
 */
export interface AttachmentDto {
  id: number;
  taskId: number;
  userId: number;
  fileName: string;
  fileSize: number;
  fileType: string;
  fileUrl: string;
  thumbnailUrl?: string;
  type: AttachmentType;
  createdAt: number | null;
  updatedAt: number | null;
  userName?: string;
}

/**
 * Interface for Attachment Query
 */
export interface AttachmentQueryDto {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
  taskId?: number;
  type?: AttachmentType;
}

/**
 * Interface for Create Attachment
 */
export interface CreateAttachmentDto {
  taskId: number;
  fileName: string;
  fileSize: number;
  fileType: string;
  fileUrl: string;
  thumbnailUrl?: string;
  type: AttachmentType;
}

/**
 * Interface for Update Attachment
 */
export interface UpdateAttachmentDto {
  fileName?: string;
  thumbnailUrl?: string;
}
