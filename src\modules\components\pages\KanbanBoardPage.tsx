import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Card, Typography, Tabs, Select } from '@/shared/components/common';
import {
  KanbanBoard,
  KanbanBoardData,
  KanbanMoveResult,
  KanbanVariant,
  KanbanFunctionality,
  KanbanTheme,
  KanbanCard,
  KanbanCardDetails,
} from '@/shared/components/kanban';

/**
 * Trang demo cho Kanban Board
 */
const KanbanBoardPage: React.FC = () => {
  const { t } = useTranslation();

  // State cho các biến thể
  const [variant, setVariant] = useState<KanbanVariant>(KanbanVariant.DEFAULT);
  const [functionality, setFunctionality] = useState<KanbanFunctionality>(
    KanbanFunctionality.INTERACTIVE
  );
  const [theme, setTheme] = useState<KanbanTheme>(KanbanTheme.DEFAULT);
  const [activeTab, setActiveTab] = useState<string>('demo');

  // State cho thẻ được chọn
  const [selectedCard, setSelectedCard] = useState<KanbanCard | null>(null);

  // Dữ liệu mẫu cho Kanban Board
  const initialData: KanbanBoardData = {
    columns: [
      {
        id: 'column-1',
        title: t('kanban.status.todo', 'To Do'),
        cardIds: ['card-1', 'card-2', 'card-3'],
        color: 'blue',
      },
      {
        id: 'column-2',
        title: t('kanban.status.inProgress', 'In Progress'),
        cardIds: ['card-4', 'card-5'],
        color: 'orange',
      },
      {
        id: 'column-3',
        title: t('kanban.status.review', 'Review'),
        cardIds: ['card-6'],
        color: 'purple',
      },
      {
        id: 'column-4',
        title: t('kanban.status.done', 'Done'),
        cardIds: ['card-7', 'card-8'],
        color: 'green',
      },
    ],
    cards: {
      'card-1': {
        id: 'card-1',
        title: 'Thiết kế giao diện người dùng',
        description: 'Thiết kế giao diện người dùng cho ứng dụng di động',
        priority: 'high',
        dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        assignee: {
          id: 'user-1',
          name: 'Nguyễn Văn A',
          avatar: 'https://i.pravatar.cc/150?img=1',
        },
        labels: [
          { id: 'label-1', name: 'UI/UX', color: 'blue' },
          { id: 'label-2', name: 'Mobile', color: 'green' },
        ],
        commentsCount: 3,
        attachmentsCount: 2,
      },
      'card-2': {
        id: 'card-2',
        title: 'Phát triển API backend',
        description: 'Xây dựng các API RESTful cho ứng dụng',
        priority: 'medium',
        dueDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
        assignee: {
          id: 'user-2',
          name: 'Trần Thị B',
          avatar: 'https://i.pravatar.cc/150?img=2',
        },
        labels: [
          { id: 'label-3', name: 'Backend', color: 'red' },
          { id: 'label-4', name: 'API', color: 'purple' },
        ],
        commentsCount: 1,
        attachmentsCount: 0,
      },
      'card-3': {
        id: 'card-3',
        title: 'Cấu hình CI/CD',
        description: 'Thiết lập quy trình CI/CD cho dự án',
        priority: 'low',
        assignee: {
          id: 'user-3',
          name: 'Lê Văn C',
          avatar: 'https://i.pravatar.cc/150?img=3',
        },
        labels: [{ id: 'label-5', name: 'DevOps', color: 'orange' }],
      },
      'card-4': {
        id: 'card-4',
        title: 'Tích hợp thanh toán',
        description: 'Tích hợp cổng thanh toán vào ứng dụng',
        priority: 'high',
        dueDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
        assignee: {
          id: 'user-4',
          name: 'Phạm Thị D',
          avatar: 'https://i.pravatar.cc/150?img=4',
        },
        labels: [
          { id: 'label-6', name: 'Payment', color: 'green' },
          { id: 'label-7', name: 'Integration', color: 'blue' },
        ],
        commentsCount: 5,
        attachmentsCount: 1,
        progress: 30,
      },
      'card-5': {
        id: 'card-5',
        title: 'Tối ưu hiệu suất',
        description: 'Tối ưu hiệu suất tải trang và trải nghiệm người dùng',
        priority: 'medium',
        assignee: {
          id: 'user-5',
          name: 'Hoàng Văn E',
          avatar: 'https://i.pravatar.cc/150?img=5',
        },
        labels: [{ id: 'label-8', name: 'Performance', color: 'yellow' }],
        progress: 50,
      },
      'card-6': {
        id: 'card-6',
        title: 'Viết tài liệu API',
        description: 'Viết tài liệu cho các API của ứng dụng',
        priority: 'low',
        dueDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),
        assignee: {
          id: 'user-6',
          name: 'Ngô Thị F',
          avatar: 'https://i.pravatar.cc/150?img=6',
        },
        labels: [{ id: 'label-9', name: 'Documentation', color: 'gray' }],
        progress: 80,
      },
      'card-7': {
        id: 'card-7',
        title: 'Thiết kế logo',
        description: 'Thiết kế logo cho ứng dụng',
        priority: 'medium',
        assignee: {
          id: 'user-7',
          name: 'Đỗ Văn G',
          avatar: 'https://i.pravatar.cc/150?img=7',
        },
        labels: [{ id: 'label-10', name: 'Design', color: 'pink' }],
        commentsCount: 2,
        attachmentsCount: 3,
        progress: 100,
      },
      'card-8': {
        id: 'card-8',
        title: 'Nghiên cứu thị trường',
        description: 'Nghiên cứu thị trường và đối thủ cạnh tranh',
        priority: 'high',
        assignee: {
          id: 'user-8',
          name: 'Vũ Thị H',
          avatar: 'https://i.pravatar.cc/150?img=8',
        },
        labels: [
          { id: 'label-11', name: 'Research', color: 'cyan' },
          { id: 'label-12', name: 'Marketing', color: 'indigo' },
        ],
        commentsCount: 4,
        progress: 100,
      },
    },
  };

  // State cho dữ liệu Kanban Board
  const [boardData, setBoardData] = useState<KanbanBoardData>(initialData);

  // Xử lý khi có thay đổi dữ liệu
  const handleChange = (data: KanbanBoardData) => {
    setBoardData(data);
  };

  // Xử lý khi kéo thả thẻ
  const handleCardMove = (result: KanbanMoveResult) => {
    console.log('Card moved:', result);
  };

  // Xử lý khi click vào thẻ
  const handleCardClick = (card: KanbanCard) => {
    console.log('Card clicked:', card);
    setSelectedCard(card);
  };

  // Xử lý khi cập nhật thẻ
  const handleCardUpdate = (cardId: string, updates: Partial<KanbanCard>) => {
    setBoardData(prev => {
      const newData = { ...prev };
      const newCards = { ...prev.cards };

      newCards[cardId] = {
        ...newCards[cardId],
        ...updates,
      };

      newData.cards = newCards;
      return newData;
    });

    // Cập nhật thẻ được chọn
    if (selectedCard && selectedCard.id === cardId) {
      setSelectedCard({
        ...selectedCard,
        ...updates,
      });
    }
  };

  // Xử lý khi xóa thẻ
  const handleCardDelete = (cardId: string) => {
    setBoardData(prev => {
      const newData = { ...prev };
      const newCards = { ...prev.cards };
      const newColumns = [...prev.columns];

      // Tìm cột chứa thẻ
      const columnIndex = newColumns.findIndex(col => col.cardIds.includes(cardId));

      if (columnIndex !== -1) {
        // Xóa cardId khỏi cột
        const column = { ...newColumns[columnIndex] };
        column.cardIds = column.cardIds.filter(id => id !== cardId);
        newColumns[columnIndex] = column;
      }

      // Xóa thẻ khỏi danh sách cards
      delete newCards[cardId];

      newData.cards = newCards;
      newData.columns = newColumns;

      return newData;
    });

    // Đóng modal chi tiết
    setSelectedCard(null);
  };

  // Các tùy chọn cho Select
  const variantOptions = [
    { value: KanbanVariant.DEFAULT, label: t('kanban.variants.default', 'Default') },
    { value: KanbanVariant.COMPACT, label: t('kanban.variants.compact', 'Compact') },
    { value: KanbanVariant.DETAILED, label: t('kanban.variants.detailed', 'Detailed') },
    { value: KanbanVariant.HORIZONTAL, label: t('kanban.variants.horizontal', 'Horizontal') },
    { value: KanbanVariant.VERTICAL, label: t('kanban.variants.vertical', 'Vertical') },
    { value: KanbanVariant.SWIMLANE, label: t('kanban.variants.swimlane', 'Swimlane') },
    { value: KanbanVariant.TIMELINE, label: t('kanban.variants.timeline', 'Timeline') },
    { value: KanbanVariant.GRID, label: t('kanban.variants.grid', 'Grid') },
    { value: KanbanVariant.MASONRY, label: t('kanban.variants.masonry', 'Masonry') },
  ];

  const functionalityOptions = [
    {
      value: KanbanFunctionality.INTERACTIVE,
      label: t('kanban.functionality.interactive', 'Interactive'),
    },
    { value: KanbanFunctionality.SIMPLE, label: t('kanban.functionality.simple', 'Simple') },
    { value: KanbanFunctionality.ADVANCED, label: t('kanban.functionality.advanced', 'Advanced') },
    { value: KanbanFunctionality.READONLY, label: t('kanban.functionality.readonly', 'Read-only') },
    {
      value: KanbanFunctionality.COLLABORATIVE,
      label: t('kanban.functionality.collaborative', 'Collaborative'),
    },
    { value: KanbanFunctionality.FILTERED, label: t('kanban.functionality.filtered', 'Filtered') },
    {
      value: KanbanFunctionality.SEARCHABLE,
      label: t('kanban.functionality.searchable', 'Searchable'),
    },
    { value: KanbanFunctionality.SORTABLE, label: t('kanban.functionality.sortable', 'Sortable') },
  ];

  const themeOptions = [
    { value: KanbanTheme.DEFAULT, label: t('kanban.theme.default', 'Default') },
    { value: KanbanTheme.COLORFUL, label: t('kanban.theme.colorful', 'Colorful') },
    { value: KanbanTheme.MINIMAL, label: t('kanban.theme.minimal', 'Minimal') },
    { value: KanbanTheme.BORDERED, label: t('kanban.theme.bordered', 'Bordered') },
    { value: KanbanTheme.DARK, label: t('kanban.theme.dark', 'Dark') },
    { value: KanbanTheme.LIGHT, label: t('kanban.theme.light', 'Light') },
    { value: KanbanTheme.GRADIENT, label: t('kanban.theme.gradient', 'Gradient') },
    { value: KanbanTheme.MATERIAL, label: t('kanban.theme.material', 'Material') },
    { value: KanbanTheme.GLASSMORPHISM, label: t('kanban.theme.glassmorphism', 'Glassmorphism') },
    { value: KanbanTheme.NEUMORPHISM, label: t('kanban.theme.neumorphism', 'Neumorphism') },
  ];

  // Các tab
  const tabs = [
    {
      key: 'demo',
      label: t('components.kanban.demo', 'Demo'),
      children: (
        <div className="h-[calc(100vh-250px)] min-h-[700px]">
          <div className="mb-4 grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Typography variant="body2" className="mb-1 font-medium">
                {t('kanban.variants.title', 'Layout Variant')}
              </Typography>
              <Select
                options={variantOptions}
                value={variant}
                onChange={value => setVariant(value as KanbanVariant)}
                className="w-full"
              />
            </div>
            <div>
              <Typography variant="body2" className="mb-1 font-medium">
                {t('kanban.functionality.title', 'Functionality')}
              </Typography>
              <Select
                options={functionalityOptions}
                value={functionality}
                onChange={value => setFunctionality(value as KanbanFunctionality)}
                className="w-full"
              />
            </div>
            <div>
              <Typography variant="body2" className="mb-1 font-medium">
                {t('kanban.theme.title', 'Theme')}
              </Typography>
              <Select
                options={themeOptions}
                value={theme}
                onChange={value => setTheme(value as KanbanTheme)}
                className="w-full"
              />
            </div>
          </div>
          <div className="h-full overflow-hidden">
            <KanbanBoard
              initialData={boardData}
              onChange={handleChange}
              onCardMove={handleCardMove}
              onCardClick={handleCardClick}
              variant={variant}
              functionality={functionality}
              theme={theme}
            />
          </div>

          {/* Modal chi tiết thẻ */}
          {selectedCard && (
            <KanbanCardDetails
              card={selectedCard}
              onClose={() => setSelectedCard(null)}
              onUpdate={handleCardUpdate}
              onDelete={handleCardDelete}
              editable={functionality !== KanbanFunctionality.READONLY}
            />
          )}
        </div>
      ),
    },
    {
      key: 'usage',
      label: t('components.kanban.usage', 'Usage'),
      children: (
        <>
          <Typography variant="body1" className="mb-2">
            {t(
              'components.kanban.usageDescription',
              'Để sử dụng Kanban Board, bạn cần import component và cung cấp dữ liệu ban đầu:'
            )}
          </Typography>

          <pre className="bg-gray-100 dark:bg-gray-800 p-4 rounded-md overflow-auto mb-4">
            {`import {
  KanbanBoard,
  KanbanBoardData,
  KanbanVariant,
  KanbanFunctionality,
  KanbanTheme
} from '@/shared/components/kanban';

// Dữ liệu ban đầu
const initialData: KanbanBoardData = {
  columns: [
    {
      id: 'column-1',
      title: 'To Do',
      cardIds: ['card-1', 'card-2'],
      color: 'blue',
    },
    {
      id: 'column-2',
      title: 'In Progress',
      cardIds: ['card-3'],
      color: 'orange',
    },
    // Thêm các cột khác...
  ],
  cards: {
    'card-1': {
      id: 'card-1',
      title: 'Task 1',
      description: 'Description for task 1',
      priority: 'high',
      // Thêm các thuộc tính khác...
    },
    // Thêm các thẻ khác...
  },
};

// Sử dụng component
<KanbanBoard
  initialData={initialData}
  onChange={handleChange}
  onCardMove={handleCardMove}
  onCardClick={handleCardClick}
  variant={KanbanVariant.DEFAULT}
  functionality={KanbanFunctionality.INTERACTIVE}
  theme={KanbanTheme.DEFAULT}
/>`}
          </pre>

          <Typography variant="h5" className="mb-2 mt-4">
            {t('components.kanban.props', 'Props')}
          </Typography>

          <ul className="list-disc pl-6 mb-4 space-y-2">
            <li>
              <code>initialData</code>: Dữ liệu ban đầu của Kanban Board
            </li>
            <li>
              <code>onChange</code>: Callback khi có thay đổi dữ liệu
            </li>
            <li>
              <code>onCardMove</code>: Callback khi kéo thả thẻ
            </li>
            <li>
              <code>onCardClick</code>: Callback khi click vào thẻ
            </li>
            <li>
              <code>onCardAdd</code>: Callback khi thêm thẻ mới
            </li>
            <li>
              <code>onCardEdit</code>: Callback khi sửa thẻ
            </li>
            <li>
              <code>onCardDelete</code>: Callback khi xóa thẻ
            </li>
            <li>
              <code>onColumnAdd</code>: Callback khi thêm cột mới
            </li>
            <li>
              <code>onColumnEdit</code>: Callback khi sửa cột
            </li>
            <li>
              <code>onColumnDelete</code>: Callback khi xóa cột
            </li>
            <li>
              <code>onColumnReorder</code>: Callback khi thay đổi thứ tự cột
            </li>
            <li>
              <code>allowAddColumn</code>: Có cho phép thêm cột mới không
            </li>
            <li>
              <code>allowColumnDrag</code>: Có cho phép kéo thả cột không
            </li>
            <li>
              <code>allowAddCard</code>: Có cho phép thêm thẻ mới không
            </li>
            <li>
              <code>allowCardDrag</code>: Có cho phép kéo thả thẻ không
            </li>
            <li>
              <code>variant</code>: Biến thể giao diện (DEFAULT, COMPACT, DETAILED, HORIZONTAL,
              VERTICAL)
            </li>
            <li>
              <code>functionality</code>: Biến thể chức năng (INTERACTIVE, SIMPLE, ADVANCED,
              READONLY)
            </li>
            <li>
              <code>theme</code>: Biến thể theme (DEFAULT, COLORFUL, MINIMAL, BORDERED)
            </li>
            <li>
              <code>loading</code>: Có đang loading không
            </li>
            <li>
              <code>className</code>: Custom class name
            </li>
          </ul>
        </>
      ),
    },
    {
      key: 'variants',
      label: t('kanban.variants.title', 'Variants'),
      children: (
        <div className="space-y-6">
          <div>
            <Typography variant="h5" className="mb-2">
              {t('kanban.variants.title', 'Layout Variants')}
            </Typography>
            <ul className="list-disc pl-6 mb-4 space-y-2">
              <li>
                <code>DEFAULT</code>:{' '}
                {t('kanban.variants.defaultDescription', 'Bố cục mặc định với các cột ngang')}
              </li>
              <li>
                <code>COMPACT</code>:{' '}
                {t('kanban.variants.compactDescription', 'Bố cục nhỏ gọn với ít thông tin hơn')}
              </li>
              <li>
                <code>DETAILED</code>:{' '}
                {t('kanban.variants.detailedDescription', 'Hiển thị nhiều thông tin chi tiết hơn')}
              </li>
              <li>
                <code>HORIZONTAL</code>:{' '}
                {t('kanban.variants.horizontalDescription', 'Bố cục ngang (giống DEFAULT)')}
              </li>
              <li>
                <code>VERTICAL</code>:{' '}
                {t(
                  'kanban.variants.verticalDescription',
                  'Bố cục dọc với các cột xếp theo chiều dọc'
                )}
              </li>
              <li>
                <code>SWIMLANE</code>:{' '}
                {t('kanban.variants.swimlaneDescription', 'Bố cục swimlane với header có màu')}
              </li>
              <li>
                <code>TIMELINE</code>:{' '}
                {t('kanban.variants.timelineDescription', 'Bố cục timeline với đường kẻ dọc')}
              </li>
              <li>
                <code>GRID</code>:{' '}
                {t(
                  'kanban.variants.gridDescription',
                  'Bố cục lưới với các cột trong grid responsive'
                )}
              </li>
              <li>
                <code>MASONRY</code>:{' '}
                {t(
                  'kanban.variants.masonryDescription',
                  'Bố cục masonry với các cột có chiều cao khác nhau'
                )}
              </li>
            </ul>
          </div>

          <div>
            <Typography variant="h5" className="mb-2">
              {t('kanban.functionality.title', 'Functionality Variants')}
            </Typography>
            <ul className="list-disc pl-6 mb-4 space-y-2">
              <li>
                <code>INTERACTIVE</code>:{' '}
                {t('kanban.functionality.interactiveDescription', 'Đầy đủ tính năng tương tác')}
              </li>
              <li>
                <code>SIMPLE</code>:{' '}
                {t('kanban.functionality.simpleDescription', 'Chỉ có các tính năng cơ bản')}
              </li>
              <li>
                <code>ADVANCED</code>:{' '}
                {t('kanban.functionality.advancedDescription', 'Có thêm các tính năng nâng cao')}
              </li>
              <li>
                <code>READONLY</code>:{' '}
                {t('kanban.functionality.readonlyDescription', 'Chỉ xem, không thể thay đổi')}
              </li>
              <li>
                <code>COLLABORATIVE</code>:{' '}
                {t(
                  'kanban.functionality.collaborativeDescription',
                  'Tính năng cộng tác thời gian thực'
                )}
              </li>
              <li>
                <code>FILTERED</code>:{' '}
                {t('kanban.functionality.filteredDescription', 'Khả năng lọc')}
              </li>
              <li>
                <code>SEARCHABLE</code>:{' '}
                {t('kanban.functionality.searchableDescription', 'Tính năng tìm kiếm')}
              </li>
              <li>
                <code>SORTABLE</code>:{' '}
                {t('kanban.functionality.sortableDescription', 'Khả năng sắp xếp')}
              </li>
            </ul>
          </div>

          <div>
            <Typography variant="h5" className="mb-2">
              {t('kanban.theme.title', 'Theme Variants')}
            </Typography>
            <ul className="list-disc pl-6 mb-4 space-y-2">
              <li>
                <code>DEFAULT</code>: {t('kanban.theme.defaultDescription', 'Theme mặc định')}
              </li>
              <li>
                <code>COLORFUL</code>:{' '}
                {t(
                  'kanban.theme.colorfulDescription',
                  'Mỗi thẻ có màu sắc dựa trên mức độ ưu tiên'
                )}
              </li>
              <li>
                <code>MINIMAL</code>: {t('kanban.theme.minimalDescription', 'Giao diện tối giản')}
              </li>
              <li>
                <code>BORDERED</code>: {t('kanban.theme.borderedDescription', 'Có viền rõ ràng')}
              </li>
              <li>
                <code>DARK</code>: {t('kanban.theme.darkDescription', 'Theme tối với chữ sáng')}
              </li>
              <li>
                <code>LIGHT</code>: {t('kanban.theme.lightDescription', 'Theme sáng với chữ tối')}
              </li>
              <li>
                <code>GRADIENT</code>: {t('kanban.theme.gradientDescription', 'Nền gradient')}
              </li>
              <li>
                <code>MATERIAL</code>:{' '}
                {t('kanban.theme.materialDescription', 'Thiết kế Material với đổ bóng')}
              </li>
              <li>
                <code>GLASSMORPHISM</code>:{' '}
                {t('kanban.theme.glassmorphismDescription', 'Hiệu ứng Glassmorphism với blur')}
              </li>
              <li>
                <code>NEUMORPHISM</code>:{' '}
                {t('kanban.theme.neumorphismDescription', 'Hiệu ứng Neumorphism với đổ bóng mềm')}
              </li>
            </ul>
          </div>
        </div>
      ),
    },
  ];

  return (
    <div className="p-4 sm:p-6 w-full max-w-full">
      <div className="mb-8">
        <Typography variant="h3" className="mb-2">
          {t('components.kanban.title', 'Kanban Board')}
        </Typography>
        <Typography variant="body1" color="muted">
          {t(
            'components.kanban.description',
            'Bảng Kanban với khả năng kéo thả các thẻ giữa các cột'
          )}
        </Typography>
      </div>

      <Card className="p-4 mb-8 w-full max-w-full overflow-hidden">
        <Tabs items={tabs} activeKey={activeTab} onChange={key => setActiveTab(key)} />
      </Card>
    </div>
  );
};

export default KanbanBoardPage;
